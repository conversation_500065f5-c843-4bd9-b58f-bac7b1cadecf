import { Link, usePage } from '@inertiajs/react';
import React, { useState } from 'react';

interface PagamentoData {
    id: string;
    transaction_amount: number;
    description: string;
    payment_method_id: string;
    payment_type_id: string;
    status: string;
    status_detail?: string;
    date_created: string;
    date_approved?: string;
    init_point?: string;
    external_reference?: string;
    user_name?: string;
    created_at_formatted?: string;
}

interface PageProps {
    pagamentosRecentes: PagamentoData[];
    [key: string]: unknown;
}

const TestePagamento: React.FC = () => {
    const { pagamentosRecentes = [] } = usePage<PageProps>().props;
    const [loading, setLoading] = useState(false);
    const [pagamentoData, setPagamentoData] = useState<PagamentoData | null>(null);
    const [verificationData, setVerificationData] = useState<PagamentoData | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [paymentId, setPaymentId] = useState<string>('');

    const criarPagamento = async () => {
        setLoading(true);
        setError(null);
        setSuccess(null);

        try {
            const response = await fetch('/teste-pagamento/criar', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
            });

            const data = await response.json();

            if (data.success) {
                setPagamentoData(data.pagamento);
                setPaymentId(data.pagamento.id);
                setSuccess('Pagamento criado com sucesso! ID: ' + data.pagamento.id);

                // Redirecionar para o link de pagamento em uma nova aba
                if (data.pagamento.init_point) {
                    window.open(data.pagamento.init_point, '_blank');
                }
            } else {
                setError(data.message || 'Erro ao criar pagamento');
            }
        } catch (err) {
            setError('Erro ao comunicar com o servidor');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const verificarPagamento = async () => {
        if (!paymentId) {
            setError('Por favor, crie um pagamento primeiro');
            return;
        }

        setLoading(true);
        setError(null);
        setSuccess(null);

        try {
            const response = await fetch('/teste-pagamento/verificar', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ pagamento_id: paymentId }),
            });

            const data = await response.json();

            if (data.success) {
                setVerificationData(data.pagamento);
                setSuccess('Pagamento verificado com sucesso! Status: ' + data.pagamento.status);
            } else {
                setError(data.message || 'Erro ao verificar pagamento');
            }
        } catch (err) {
            setError('Erro ao comunicar com o servidor');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const formatarData = (dataString: string) => {
        return new Date(dataString).toLocaleString('pt-BR');
    };

    const formatarMoeda = (valor: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(valor);
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'approved':
                return 'text-green-600 bg-green-100';
            case 'pending':
                return 'text-yellow-600 bg-yellow-100';
            case 'rejected':
                return 'text-red-600 bg-red-100';
            case 'in_process':
                return 'text-blue-600 bg-blue-100';
            default:
                return 'text-gray-600 bg-gray-100';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'approved':
                return 'Aprovado';
            case 'pending':
                return 'Pendente';
            case 'rejected':
                return 'Rejeitado';
            case 'in_process':
                return 'Em Processamento';
            case 'cancelled':
                return 'Cancelado';
            default:
                return status;
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                <div className="mb-8 text-center">
                    <h1 className="mb-2 text-3xl font-bold text-gray-900">Teste de Pagamento - Mercado Pago</h1>
                    <p className="text-gray-600">Crie um link de pagamento, pague e depois verifique o status</p>
                </div>

                <div className="mb-6 rounded-lg bg-white p-6 shadow-lg">
                    <div className="mb-6 flex flex-col gap-4 sm:flex-row">
                        <button
                            onClick={criarPagamento}
                            disabled={loading}
                            className="flex-1 rounded-lg bg-blue-600 px-6 py-3 font-medium text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                            {loading ? 'Criando...' : 'Criar Pagamento de Teste'}
                        </button>

                        <button
                            onClick={verificarPagamento}
                            disabled={loading || !paymentId}
                            className="flex-1 rounded-lg bg-green-600 px-6 py-3 font-medium text-white hover:bg-green-700 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                            {loading ? 'Verificando...' : 'Verificar Status do Pagamento'}
                        </button>
                    </div>

                    {error && <div className="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">{error}</div>}

                    {success && <div className="mb-4 rounded border border-green-400 bg-green-100 px-4 py-3 text-green-700">{success}</div>}

                    <div className="mb-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
                        <h3 className="mb-2 font-semibold text-blue-900">Instruções:</h3>
                        <ol className="list-inside list-decimal space-y-1 text-blue-800">
                            <li>Clique em "Criar Pagamento de Teste" para gerar um link</li>
                            <li>Uma nova aba abrirá com a página de pagamento do Mercado Pago</li>
                            <li>Realize o pagamento de teste (use dados de teste do Mercado Pago)</li>
                            <li>Volte para esta página e clique em "Verificar Status do Pagamento"</li>
                            <li>O sistema irá consultar diretamente com o Mercado Pago o status real</li>
                        </ol>
                    </div>
                </div>

                {/* Seletor de pagamentos recentes */}
                {pagamentosRecentes.length > 0 && (
                    <div className="mb-6 rounded-lg bg-white p-6 shadow-lg">
                        <h2 className="mb-4 text-xl font-bold text-gray-900">Pagamentos de Teste Recentes</h2>
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Selecione um pagamento para verificar:
                            </label>
                            <select
                                value={paymentId}
                                onChange={(e) => setPaymentId(e.target.value)}
                                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                            >
                                <option value="">Escolha um pagamento...</option>
                                {pagamentosRecentes.map((pagamento) => (
                                    <option key={pagamento.id} value={pagamento.id}>
                                        {pagamento.user_name} - {formatarMoeda(pagamento.transaction_amount)} - {pagamento.created_at_formatted}
                                    </option>
                                ))}
                            </select>
                        </div>
                        
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            ID
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Usuário
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Valor
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Data
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Ações
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {pagamentosRecentes.map((pagamento) => (
                                        <tr key={pagamento.id}>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                <span className="font-mono text-xs">{pagamento.id}</span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {pagamento.user_name}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {formatarMoeda(pagamento.transaction_amount)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(pagamento.status)}`}>
                                                    {getStatusText(pagamento.status)}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {pagamento.created_at_formatted}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button
                                                    onClick={() => setPaymentId(pagamento.id)}
                                                    className="text-blue-600 hover:text-blue-900 mr-3"
                                                >
                                                    Selecionar
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                )}

                {pagamentoData && (
                    <div className="mb-6 rounded-lg bg-white p-6 shadow-lg">
                        <h2 className="mb-4 text-xl font-bold text-gray-900">Dados do Pagamento Criado</h2>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">ID do Pagamento</label>
                                <p className="font-mono text-lg text-gray-900">{pagamentoData.id}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Status</label>
                                <span className={`inline-flex rounded-full px-3 py-1 text-sm font-medium ${getStatusColor(pagamentoData.status)}`}>
                                    {getStatusText(pagamentoData.status)}
                                </span>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Valor</label>
                                <p className="text-lg font-semibold text-gray-900">{formatarMoeda(pagamentoData.transaction_amount)}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Método de Pagamento</label>
                                <p className="text-gray-900">{pagamentoData.payment_method_id}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Tipo de Pagamento</label>
                                <p className="text-gray-900">{pagamentoData.payment_type_id}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Data de Criação</label>
                                <p className="text-gray-900">{formatarData(pagamentoData.date_created)}</p>
                            </div>
                            {pagamentoData.date_approved && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Data de Aprovação</label>
                                    <p className="text-gray-900">{formatarData(pagamentoData.date_approved)}</p>
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {verificationData && (
                    <div className="mb-6 rounded-lg bg-white p-6 shadow-lg">
                        <h2 className="mb-4 text-xl font-bold text-gray-900">Status Verificado (via API)</h2>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">ID do Pagamento</label>
                                <p className="font-mono text-lg text-gray-900">{verificationData.id}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Status Atual</label>
                                <span className={`inline-flex rounded-full px-3 py-1 text-sm font-medium ${getStatusColor(verificationData.status)}`}>
                                    {getStatusText(verificationData.status)}
                                </span>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Valor</label>
                                <p className="text-lg font-semibold text-gray-900">{formatarMoeda(verificationData.transaction_amount)}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Status Detalhado</label>
                                <p className="text-gray-900">{verificationData.status_detail}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Método de Pagamento</label>
                                <p className="text-gray-900">{verificationData.payment_method_id}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Data de Verificação</label>
                                <p className="text-gray-900">{new Date().toLocaleString('pt-BR')}</p>
                            </div>
                        </div>

                        {verificationData.status === 'approved' && (
                            <div className="mt-4 rounded-lg border border-green-200 bg-green-50 p-4">
                                <p className="font-medium text-green-800">
                                    ✅ Pagamento confirmado! O sistema validou com sucesso o pagamento através da API do Mercado Pago.
                                </p>
                            </div>
                        )}
                    </div>
                )}

                <div className="rounded-lg bg-white p-6 shadow-lg">
                    <h2 className="mb-4 text-xl font-bold text-gray-900">Informações Importantes</h2>
                    <div className="space-y-4">
                        <div>
                            <h3 className="mb-2 font-semibold text-gray-900">Como funciona:</h3>
                            <ul className="list-inside list-disc space-y-1 text-gray-700">
                                <li>Esta rota cria pagamentos como agendamentos, mas sem validação no checkout</li>
                                <li>A validação ocorre apenas através de webhook ou verificação manual</li>
                                <li>Se o webhook não for recebido, você pode verificar manualmente</li>
                                <li>O sistema consulta diretamente a API do Mercado Pago para confirmar o pagamento</li>
                            </ul>
                        </div>

                        <div>
                            <h3 className="mb-2 font-semibold text-gray-900">Ambiente:</h3>
                            <p className="text-gray-700">
                                Você está no ambiente de <span className="font-semibold text-blue-600">SANDBOX</span>. Use os dados de teste do
                                Mercado Pago para simular pagamentos.
                            </p>
                        </div>

                        <div>
                            <h3 className="mb-2 font-semibold text-gray-900">Dados de Teste:</h3>
                            <div className="rounded-lg bg-gray-50 p-4">
                                <p className="mb-2 text-sm text-gray-600">Cartão de Crédito (Teste):</p>
                                <ul className="space-y-1 text-sm text-gray-700">
                                    <li>
                                        <strong>Número:</strong> 5031 4332 1540 6351
                                    </li>
                                    <li>
                                        <strong>CVV:</strong> 123
                                    </li>
                                    <li>
                                        <strong>Validade:</strong> 11/2025
                                    </li>
                                    <li>
                                        <strong>Titular:</strong> Qualquer nome
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="mt-6 text-center">
                    <Link href="/" className="font-medium text-blue-600 hover:text-blue-800">
                        Voltar para a página inicial
                    </Link>
                </div>
            </div>
        </div>
    );
};

export default TestePagamento;
