import { Badge } from '@/components/ui/badge';
import PublicLayout from '@/layouts/public-layout';
import { Mail, MapPin, Phone } from 'lucide-react';

export default function TermosUso() {
    return (
        <PublicLayout
            title="Termos de Uso - F4 Fisio"
            description="Termos de uso da plataforma F4 Fisio. Conheça as condições para utilização dos nossos serviços."
        >
            <div className="bg-background py-12">
                <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                    <div className="mb-12 text-center">
                        <h1 className="text-4xl font-medium text-balance md:text-5xl">Termos de Uso</h1>
                        <p className="mt-6 text-xl text-muted-foreground">Última atualização: {new Date().toLocaleDateString('pt-BR')}</p>
                    </div>

                    <div className="prose prose-lg max-w-none">
                        <section className="mb-8">
                            <h2 className="mb-4 text-2xl font-semibold">1. Aceitação dos Termos</h2>
                            <p className="leading-relaxed text-muted-foreground">
                                Ao acessar e utilizar os serviços da F4 Fisio, você concorda em cumprir e estar vinculado a estes Termos de Uso. Se
                                você não concordar com qualquer parte destes termos, não deve utilizar nossos serviços.
                            </p>
                        </section>

                        <section className="mb-8">
                            <h2 className="mb-4 text-2xl font-semibold">2. Descrição dos Serviços</h2>
                            <p className="mb-4 leading-relaxed text-muted-foreground">A F4 Fisio oferece:</p>
                            <ul className="list-inside list-disc space-y-2 text-muted-foreground">
                                <li>Serviços de fisioterapia domiciliar</li>
                                <li>Plataforma de busca de estabelecimentos de saúde</li>
                                <li>Agendamento online de consultas</li>
                                <li>Programa de afiliados</li>
                                <li>Atendimento corporativo</li>
                            </ul>
                        </section>

                        <section className="mb-8">
                            <h2 className="mb-4 text-2xl font-semibold">3. Cadastro e Conta do Usuário</h2>
                            <div className="space-y-4">
                                <div>
                                    <h3 className="mb-2 text-lg font-medium">3.1 Responsabilidades do Usuário</h3>
                                    <ul className="list-inside list-disc space-y-1 text-muted-foreground">
                                        <li>Fornecer informações verdadeiras e atualizadas</li>
                                        <li>Manter a confidencialidade da senha</li>
                                        <li>Notificar sobre uso não autorizado da conta</li>
                                        <li>Ser responsável por todas as atividades em sua conta</li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 className="mb-2 text-lg font-medium">3.2 Elegibilidade</h3>
                                    <p className="text-muted-foreground">
                                        Você deve ter pelo menos 18 anos ou ter autorização dos pais/responsáveis para utilizar nossos serviços.
                                    </p>
                                </div>
                            </div>
                        </section>

                        <section className="mb-8">
                            <h2 className="mb-4 text-2xl font-semibold">4. Uso Aceitável</h2>
                            <p className="mb-4 leading-relaxed text-muted-foreground">Você concorda em NÃO:</p>
                            <ul className="list-inside list-disc space-y-2 text-muted-foreground">
                                <li>Usar os serviços para fins ilegais ou não autorizados</li>
                                <li>Interferir ou interromper os serviços</li>
                                <li>Tentar acessar contas de outros usuários</li>
                                <li>Transmitir vírus ou códigos maliciosos</li>
                                <li>Fazer uso comercial não autorizado da plataforma</li>
                                <li>Fornecer informações falsas ou enganosas</li>
                            </ul>
                        </section>

                        <section className="mb-8">
                            <h2 className="mb-4 text-2xl font-semibold">5. Serviços de Fisioterapia</h2>
                            <div className="space-y-4">
                                <div>
                                    <h3 className="mb-2 text-lg font-medium">5.1 Natureza dos Serviços</h3>
                                    <p className="text-muted-foreground">
                                        Os serviços de fisioterapia são prestados por profissionais credenciados e independentes. A F4 Fisio atua como
                                        intermediadora entre pacientes e fisioterapeutas.
                                    </p>
                                </div>
                                <div>
                                    <h3 className="mb-2 text-lg font-medium">5.2 Responsabilidades</h3>
                                    <ul className="list-inside list-disc space-y-1 text-muted-foreground">
                                        <li>O paciente deve fornecer informações médicas precisas</li>
                                        <li>Seguir as orientações do fisioterapeuta</li>
                                        <li>Informar sobre mudanças no estado de saúde</li>
                                        <li>Manter ambiente adequado para o atendimento</li>
                                    </ul>
                                </div>
                            </div>
                        </section>

                        <section className="mb-8">
                            <h2 className="mb-4 text-2xl font-semibold">6. Pagamentos e Cancelamentos</h2>
                            <div className="space-y-4">
                                <div>
                                    <h3 className="mb-2 text-lg font-medium">6.1 Pagamentos</h3>
                                    <ul className="list-inside list-disc space-y-1 text-muted-foreground">
                                        <li>Pagamentos devem ser efetuados conforme acordado</li>
                                        <li>Preços podem ser alterados com aviso prévio</li>
                                        <li>Taxas de atraso podem ser aplicadas</li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 className="mb-2 text-lg font-medium">6.2 Cancelamentos</h3>
                                    <ul className="list-inside list-disc space-y-1 text-muted-foreground">
                                        <li>Cancelamentos devem ser feitos com 24h de antecedência</li>
                                        <li>Cancelamentos tardios podem gerar cobrança</li>
                                        <li>Serviços/contratos podem ser cancelados a qualquer momento</li>
                                    </ul>
                                </div>
                            </div>
                        </section>

                        <section className="mb-8">
                            <h2 className="mb-4 text-2xl font-semibold">7. Propriedade Intelectual</h2>
                            <p className="leading-relaxed text-muted-foreground">
                                Todo o conteúdo da plataforma F4 Fisio, incluindo textos, imagens, logos, design e software, é protegido por direitos
                                autorais e outras leis de propriedade intelectual. É proibida a reprodução sem autorização expressa.
                            </p>
                        </section>

                        <section className="mb-8">
                            <h2 className="mb-4 text-2xl font-semibold">8. Limitação de Responsabilidade</h2>
                            <p className="leading-relaxed text-muted-foreground">
                                A F4 Fisio não se responsabiliza por danos indiretos, incidentais ou consequenciais decorrentes do uso dos serviços.
                                Nossa responsabilidade é limitada ao valor pago pelos serviços.
                            </p>
                        </section>

                        <section className="mb-8">
                            <h2 className="mb-4 text-2xl font-semibold">9. Modificações dos Termos</h2>
                            <p className="leading-relaxed text-muted-foreground">
                                Reservamo-nos o direito de modificar estes termos a qualquer momento. As alterações entrarão em vigor imediatamente
                                após a publicação. O uso continuado dos serviços constitui aceitação dos novos termos.
                            </p>
                        </section>

                        <section className="mb-8">
                            <h2 className="mb-4 text-2xl font-semibold">10. Lei Aplicável</h2>
                            <p className="leading-relaxed text-muted-foreground">
                                Estes termos são regidos pelas leis brasileiras. Qualquer disputa será resolvida nos tribunais competentes de São
                                Paulo, SP.
                            </p>
                        </section>

                        <section className="mb-8">
                            <h2 className="mb-4 text-2xl font-semibold">11. Contato</h2>
                            <p className="leading-relaxed text-muted-foreground">Para dúvidas sobre estes termos, entre em contato:</p>
                            <div className="mt-4 space-y-6">
                                <div className="text-center">
                                    <p className="text-lg font-medium">F4 Fisio</p>
                                </div>

                                <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                                    {/* Telefone */}
                                    <div className="flex flex-col items-center text-center">
                                        <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                            <Phone className="h-6 w-6" />
                                        </Badge>
                                        <h4 className="mt-3 text-sm font-semibold text-foreground">Telefone</h4>
                                        <p className="text-sm text-muted-foreground">(11) 97819-6207</p>
                                    </div>

                                    {/* Email */}
                                    <div className="flex flex-col items-center text-center">
                                        <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                            <Mail className="h-6 w-6" />
                                        </Badge>
                                        <h4 className="mt-3 text-sm font-semibold text-foreground">Email</h4>
                                        <p className="text-sm text-muted-foreground"><EMAIL></p>
                                    </div>

                                    {/* Endereço */}
                                    <div className="flex flex-col items-center text-center sm:col-span-2 lg:col-span-1">
                                        <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                            <MapPin className="h-6 w-6" />
                                        </Badge>
                                        <h4 className="mt-3 text-sm font-semibold text-foreground">Endereço</h4>
                                        <p className="text-sm text-muted-foreground">São Paulo, SP</p>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </PublicLayout>
    );
}
