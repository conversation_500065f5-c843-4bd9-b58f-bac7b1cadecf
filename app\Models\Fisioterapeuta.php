<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Fisioterapeuta extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'crefito',
        'specializations',
        'bio',
        'hourly_rate',
        'session_rate',
        'travel_fee',
        'service_rates',
        'pricing_mode',
        'available_areas',
        'working_hours',
        'available',
        'rating',
        'total_reviews',
        'status',
        'rejection_reason',
    ];
    
    protected $attributes = [
        'status' => 'pending',
    ];

    protected $casts = [
        'specializations' => 'array',
        'available_areas' => 'array',
        'working_hours' => 'array',
        'available' => 'boolean',
        'hourly_rate' => 'decimal:2',
        'session_rate' => 'decimal:2',
        'travel_fee' => 'decimal:2',
        'service_rates' => 'array',
        'rating' => 'decimal:2',
        'status' => 'string',
    ];

    // Relacionamentos
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function agendamentos()
    {
        return $this->hasMany(Agendamento::class, 'fisioterapeuta_id', 'user_id');
    }

    public function avaliacoes()
    {
        return $this->hasMany(Avaliacao::class, 'fisioterapeuta_id', 'user_id');
    }

    public function disponibilidades()
    {
        return $this->hasMany(Disponibilidade::class, 'fisioterapeuta_id', 'user_id');
    }

    // Scopes
    public function scopeDisponiveis($query)
    {
        return $query->where('available', true);
    }

    public function scopePorArea($query, $area)
    {
        return $query->whereJsonContains('available_areas', $area);
    }

    public function scopePorEspecializacao($query, $especializacao)
    {
        return $query->whereJsonContains('specializations', $especializacao);
    }

    // Métodos auxiliares
    public function updateRating()
    {
        $avaliacoes = $this->avaliacoes;
        $this->total_reviews = $avaliacoes->count();
        $this->rating = $avaliacoes->avg('rating') ?? 0;
        $this->save();
    }
}
