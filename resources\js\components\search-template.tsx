import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, Search, Filter } from 'lucide-react';

export interface SearchFilter {
    key: string;
    label: string;
    type: 'select' | 'input' | 'checkbox' | 'range';
    options?: { value: string; label: string }[];
    placeholder?: string;
    min?: number;
    max?: number;
}

export interface SearchConfig {
    title?: string;
    description?: string;
    placeholder?: string;
    showAdvancedFilters?: boolean;
    filters?: SearchFilter[];
    showResults?: boolean;
    showPagination?: boolean;
}

export interface SearchState {
    query: string;
    filters: Record<string, any>;
    page: number;
    loading: boolean;
    error: string;
}

export interface SearchTemplateProps {
    config: SearchConfig;
    state: SearchState;
    onFilterChange: (key: string, value: any) => void;
    onSearch: (query: string, filters: Record<string, any>) => void;
    onReset?: () => void;
    onClearFilters?: () => void;
    resultCount?: number;
    children?: React.ReactNode;
}

export function useSearchState(initialFilters: Record<string, any> = {}) {
    const [state, setState] = useState({
        query: '',
        filters: initialFilters,
        page: 1,
        loading: false,
        error: '',
    });

    const updateQuery = useCallback((query: string) => {
        setState(prev => ({ ...prev, query }));
    }, []);

    const updateFilter = useCallback((key: string, value: any) => {
        setState(prev => ({
            ...prev,
            filters: { ...prev.filters, [key]: value }
        }));
    }, []);

    const updateFilters = useCallback((filters: Record<string, any>) => {
        setState(prev => ({ ...prev, filters }));
    }, []);

    const setLoading = useCallback((loading: boolean) => {
        setState(prev => ({ ...prev, loading }));
    }, []);

    const setError = useCallback((error: string) => {
        setState(prev => ({ ...prev, error }));
    }, []);

    const reset = useCallback(() => {
        setState({
            query: '',
            filters: {},
            page: 1,
            loading: false,
            error: '',
        });
    }, []);

    const clearFilters = useCallback(() => {
        setState(prev => ({ ...prev, filters: {} }));
    }, []);

    return {
        state,
        updateQuery,
        updateFilter,
        updateFilters,
        setLoading,
        setError,
        reset,
        clearFilters,
    };
}

export default function SearchTemplate({
    config,
    state,
    onFilterChange,
    onSearch,
    onReset,
    onClearFilters,
    resultCount,
    children,
}: SearchTemplateProps) {
    const [showAdvanced, setShowAdvanced] = useState(false);
    const [localQuery, setLocalQuery] = useState(state.query);

    const handleSearch = useCallback(() => {
        onSearch(localQuery, state.filters);
    }, [localQuery, state.filters, onSearch]);

    const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            handleSearch();
        }
    }, [handleSearch]);

    const handleReset = useCallback(() => {
        setLocalQuery('');
        if (onClearFilters) return onClearFilters();
        if (onReset) return onReset();
    }, [onReset, onClearFilters]);

    const renderFilter = (filter: SearchFilter) => {
        const value = state.filters[filter.key];

        switch (filter.type) {
            case 'select':
                return (
                    <div key={filter.key} className="space-y-2">
                        <Label htmlFor={filter.key}>{filter.label}</Label>
                        <Select
                            value={value === '' ? undefined : value}
                            onValueChange={(val) => onFilterChange(filter.key, val)}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder={`Selecionar ${filter.label.toLowerCase()}`} />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">Todos</SelectItem>
                                {filter.options?.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                );

            case 'input':
                return (
                    <div key={filter.key} className="space-y-2">
                        <Label htmlFor={filter.key}>{filter.label}</Label>
                        <Input
                            id={filter.key}
                            value={value}
                            onChange={(e) => onFilterChange(filter.key, e.target.value)}
                            placeholder={filter.placeholder}
                        />
                    </div>
                );

            case 'checkbox':
                return (
                    <div key={filter.key} className="flex items-center space-x-2">
                        <input
                            type="checkbox"
                            id={filter.key}
                            checked={!!value}
                            onChange={(e) => onFilterChange(filter.key, e.target.checked)}
                            className="rounded border-gray-300"
                        />
                        <Label htmlFor={filter.key}>{filter.label}</Label>
                    </div>
                );

            case 'range':
                return (
                    <div key={filter.key} className="space-y-2">
                        <Label htmlFor={filter.key}>{filter.label}</Label>
                        <Input
                            id={filter.key}
                            type="range"
                            min={filter.min}
                            max={filter.max}
                            value={value}
                            onChange={(e) => onFilterChange(filter.key, e.target.value)}
                        />
                        <div className="text-sm text-gray-500">Valor: {value}</div>
                    </div>
                );

            default:
                return null;
        }
    };

    return (
        <Card className="w-full">
            <CardHeader>
                {config.title && <CardTitle>{config.title}</CardTitle>}
                {config.description && (
                    <p className="text-sm text-muted-foreground">{config.description}</p>
                )}
                {typeof resultCount === 'number' && (
                    <p className="text-xs text-muted-foreground mt-1">{resultCount} resultado(s)</p>
                )}
            </CardHeader>
            <CardContent className="space-y-4">
                {/* Main search input */}
                <div className="flex gap-2">
                    <div className="flex-1">
                        <Input
                            value={localQuery}
                            onChange={(e) => setLocalQuery(e.target.value)}
                            placeholder={config.placeholder || 'Digite sua busca...'}
                            onKeyPress={handleKeyPress}
                            disabled={state.loading}
                        />
                    </div>
                    <Button onClick={handleSearch} disabled={state.loading}>
                        <Search className="h-4 w-4 mr-2" />
                        {state.loading ? 'Buscando...' : 'Buscar'}
                    </Button>
                </div>

                {/* Advanced filters */}
                {config.showAdvancedFilters && config.filters && config.filters.length > 0 && (
                    <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
                        <CollapsibleTrigger asChild>
                            <Button variant="outline" className="w-full justify-between">
                                <span className="flex items-center">
                                    <Filter className="h-4 w-4 mr-2" />
                                    Filtros Avançados
                                </span>
                                <ChevronDown className={`h-4 w-4 transition-transform ${showAdvanced ? 'rotate-180' : ''}`} />
                            </Button>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="space-y-4 pt-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {config.filters.map(renderFilter)}
                            </div>
                            <div className="flex pt-2">
                                <Button variant="outline" onClick={handleReset}>
                                    Limpar
                                </Button>
                            </div>
                        </CollapsibleContent>
                    </Collapsible>
                )}

                {/* Error display */}
                {state.error && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm text-red-600">{state.error}</p>
                    </div>
                )}

                {/* Results section */}
                {config.showResults && children && (
                    <div className="space-y-4">
                        {children}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}