<?php

require_once 'vendor/autoload.php';

// Carregar configurações Laravel
$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel');
\Illuminate\Support\Facades\Facade::setFacadeApplication($app);

echo "=== BUSCANDO AGENDAMENTOS DO FISIOTERAPEUTA 4 ===\n";
$agendamentos = \App\Models\Agendamento::where('fisioterapeuta_id', 4)->get();

foreach($agendamentos as $agendamento) {
    echo "ID: {$agendamento->id} - Data/Hora: {$agendamento->scheduled_at} - Status: {$agendamento->status}\n";
}
echo "Total: {$agendamentos->count()} agendamentos\n";

echo "\n=== VERIFICANDO AGENDAMENTOS DO DIA 6 DE SETEMBRO ===\n";
$dataEspecifica = \App\Models\Agendamento::where('fisioterapeuta_id', 4)
    ->whereDate('scheduled_at', '2025-09-06')
    ->get();

foreach($dataEspecifica as $agendamento) {
    echo "ID: {$agendamento->id} - Hor<PERSON>rio: {$agendamento->scheduled_at->format('H:i:s')} - Status: {$agendamento->status}\n";
}

echo "\n=== VERIFICANDO EXISTÊNCIA DE AGENDAMENTO 11:00 ===\n";
$agendamento11 = \App\Models\Agendamento::where('fisioterapeuta_id', 4)
    ->whereDate('scheduled_at', '2025-09-06')
    ->whereTime('scheduled_at', '>=', '11:00:00')
    ->whereTime('scheduled_at', '<', '12:00:00')
    ->whereNotIn('status', ['cancelado'])
    ->first();

if ($agendamento11) {
    echo "ENCONTRADO: ID {$agendamento11->id} às {$agendamento11->scheduled_at}\n";
} else {
    echo "Nenhum agendamento encontrado para 11:00-12:00\n";
}

?>
