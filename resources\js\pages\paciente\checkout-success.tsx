import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Head, Link, usePage } from '@inertiajs/react';
import { ArrowRight, Home, Mail, AlertCircle } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
}

interface AgendamentoData {
    id: number;
    scheduled_at?: string;
    service_type?: string;
    price?: number;
    fisioterapeuta?: string;
    estabelecimento?: string;
    address?: string;
    current_status?: string;
}

interface FlashData {
    simulation_url?: string;
    payment_type?: 'agendamento' | 'teste';
    agendamento_id?: string;
    is_test_environment?: boolean;
    agendamento?: AgendamentoData;
    error?: string;
    error_message?: string;
}

interface PageProps {
    user: User;
    flash?: FlashData;
}



export default function CheckoutSuccess() {
    const pageProps = usePage().props as unknown as PageProps;

    //Dados do backend (já validados)
    const flash = pageProps.flash || {};
    const paymentType = flash.payment_type || 'agendamento';
    const agendamentoId = flash.agendamento_id;
    const agendamentoData = flash.agendamento;
    const errorType = flash.error;
    const errorMessage = flash.error_message;

    // Se há erro, mostrar componente de erro
    if (errorType) {
        return (
            <div className="flex min-h-screen items-center justify-center bg-background">
                <Card className="w-full max-w-md">
                    <CardHeader>
                        <h2 className="text-center text-xl font-semibold text-foreground">Falha na Validação</h2>
                    </CardHeader>
                    <CardContent className="space-y-4 text-center">
                        <div className="mx-auto w-fit rounded-full bg-red-100 p-3">
                            <AlertCircle className="h-6 w-6 text-red-600" />
                        </div>
                        <p className="text-muted-foreground">
                            {errorMessage || 'Ocorreu um erro ao validar o pagamento.'}
                        </p>
                        <div className="space-y-2 pt-4 border-t">
                            <Button variant="outline" asChild className="w-full">
                                <Link href="/suporte" className="flex items-center gap-2">
                                    <Mail className="h-4 w-4" />
                                    Entrar em Contato
                                </Link>
                            </Button>
                            <Button asChild className="w-full">
                                <Link href="/" className="flex items-center gap-2">
                                    <Home className="h-4 w-4" />
                                    Voltar ao Início
                                </Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    // Verificação básica de segurança (se não há agendamento nem dados válidos)
    if (paymentType !== 'teste' && !agendamentoId && !agendamentoData) {
        return (
            <div className="flex min-h-screen items-center justify-center bg-background">
                <Card className="w-full max-w-md">
                    <CardHeader>
                        <h2 className="text-center text-xl font-semibold text-foreground">Acesso Restrito</h2>
                    </CardHeader>
                    <CardContent className="space-y-4 text-center">
                        <div className="mx-auto w-fit rounded-full bg-red-100 p-3">
                            <AlertCircle className="h-6 w-6 text-red-600" />
                        </div>
                        <p className="text-muted-foreground">
                            Esta página contém informações sensíveis e só pode ser acessada pelo proprietário do pagamento.
                        </p>
                        <Link href="/" className="inline-block">
                            <Button variant="outline" className="w-full">
                                Voltar ao Início
                            </Button>
                        </Link>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-background">
            <Head title={paymentType === 'teste' ? 'Pagamento de Teste Confirmado!' : 'Pagamento da Consulta Confirmado!'} />

            {/* Celebratory Header */}
            <div className="relative overflow-hidden bg-gradient-to-br from-card to-background">
                <div className="relative container mx-auto px-4 py-16 text-center">
                    <div className="mx-auto mb-6 flex items-center justify-center">
                        <img src="/images/logo.png" alt="Logo F4 Fisio" className="size-20 object-contain" />
                    </div>
                    <h1 className="mb-4 text-4xl font-bold text-foreground md:text-5xl">
                        {paymentType === 'teste' ? 'Compra de Teste Realizada com Sucesso!' : 'Consulta Agendada com Sucesso!'}
                    </h1>
                    <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
                        {paymentType === 'teste'
                            ? 'Obrigado pela sua compra de teste! Seu pedido foi processado com sucesso e você receberá todas as informações por e-mail.'
                            : 'Obrigado pela sua compra! Sua consulta foi paga com sucesso e está confirmada em nosso sistema.'}
                    </p>
                </div>
            </div>

            <div className="container mx-auto px-4 pb-12">
                <div className="mx-auto max-w-4xl space-y-8">
                    {/* Next Steps Card */}
                    <div className="grid gap-6">
                        <Card className="border-border bg-card">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-card-foreground">
                                    <Mail className="h-5 w-5" />
                                    Próximos Passos
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Próximos passos restantes */}
                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                                    <h4 className="mb-2 font-medium text-blue-900">O que acontece agora?</h4>
                                    <div className="space-y-2 text-sm text-blue-800">
                                        <p>• Aguarde a confirmação do profissional sobre a disponibilidade</p>
                                        <p>• Quando confirmado, acompanhe o deslocamento em tempo real (a caminho/chegou)</p>
                                        <p>• Receberá notificações do início e progresso da sessão</p>
                                        <p>• Após a conclusão, será possível avaliar o atendimento</p>
                                        <p>• Todo o progresso ficará registrado no seu histórico</p>
                                    </div>
                                </div>
                                {/* Timeline de status do agendamento */}
                                <div className="space-y-4">
                                    <h4 className="mb-3 font-medium text-card-foreground">Status do Agendamento</h4>

                                    <div className="space-y-3">
                                        {/* Função auxiliar para renderizar status */}
                                        {(() => {
                                            // Quando chega nessa página, o pagamento foi aprovado, então começamos de 'agendado'
                                            const paymentApproved = true; // Se chegou aqui, pagamento foi aprovado
                                            const currentStatus = agendamentoData?.current_status || (paymentApproved ? 'agendado' : 'pendente');

                                            // Ordem de status: remove 'pendente' quando pagamento foi aprovado
                                            const statusOrder = paymentApproved
                                                ? ['agendado', 'confirmado', 'a caminho', 'em_andamento', 'concluido']
                                                : ['pendente', 'agendado', 'confirmado', 'a caminho', 'em_andamento', 'concluido'];

                                            const currentIndex = statusOrder.indexOf(currentStatus);

                                            return statusOrder.map((statusKey, index) => {
                                                const isCompleted = index < currentIndex;
                                                const isCurrent = index === currentIndex;
                                                const isNext = index === currentIndex + 1; // Próxima etapa
                                                const isFuture = index > currentIndex;
                                                const isCancelled = currentStatus === 'cancelado';

                                                const statusInfo = {
                                                    'pendente': {
                                                        current: {
                                                            title: 'Aguardando Pagamento',
                                                            description: 'Processando seu pagamento',
                                                            icon: 'clock'
                                                        },
                                                        completed: {
                                                            title: 'Pagamento Processado',
                                                            description: 'Pagamento foi processado com sucesso',
                                                            icon: 'check'
                                                        }
                                                    },
                                                    'agendado': {
                                                        current: {
                                                            title: 'Pagamento Aprovado',
                                                            description: 'Sua transação foi processada com sucesso',
                                                            icon: 'check'
                                                        },
                                                        completed: {
                                                            title: 'Agendamento Confirmado',
                                                            description: 'Agendamento foi confirmado no sistema',
                                                            icon: 'check'
                                                        }
                                                    },
                                                    'confirmado': {
                                                        current: {
                                                            title: 'Aguardando Confirmação do Profissional',
                                                            description: 'Aguardando confirmação do profissional sobre o agendamento',
                                                            icon: 'user-check'
                                                        },
                                                        completed: {
                                                            title: 'Profissional Confirmado',
                                                            description: 'O profissional confirmou sua disponibilidade',
                                                            icon: 'user-check'
                                                        }
                                                    },
                                                    'a caminho': {
                                                        current: {
                                                            title: 'Profissional a Caminho',
                                                            description: 'O profissional está se deslocando até o local',
                                                            icon: 'navigation'
                                                        },
                                                        completed: {
                                                            title: 'Profissional em Deslocamento',
                                                            description: 'O profissional está a caminho do atendimento',
                                                            icon: 'navigation'
                                                        }
                                                    },
                                                    'em_andamento': {
                                                        current: {
                                                            title: 'Sessão em Andamento',
                                                            description: 'A sessão de fisioterapia está sendo realizada',
                                                            icon: 'play'
                                                        },
                                                        completed: {
                                                            title: 'Sessão Iniciada',
                                                            description: 'A sessão de fisioterapia foi iniciada',
                                                            icon: 'play'
                                                        }
                                                    },
                                                    'concluido': {
                                                        current: {
                                                            title: 'Sessão Concluída',
                                                            description: 'A fisioterapia foi realizada com sucesso',
                                                            icon: 'success'
                                                        },
                                                        completed: {
                                                            title: 'Atendimento Finalizado',
                                                            description: 'O atendimento foi concluído com sucesso',
                                                            icon: 'success'
                                                        }
                                                    }
                                                };

                                                const statusConfig = statusInfo[statusKey as keyof typeof statusInfo];

                                                if (!statusConfig) return null;

                                                // Choose the appropriate message based on status
                                                const info = isCompleted ? statusConfig.completed : statusConfig.current;

                                                return (
                                                    <div key={statusKey} className={`flex items-start gap-3 ${isFuture && !isNext ? 'opacity-50' : ''}`}>
                                                        {/* Status indicator */}
                                                        <div className="mt-1 h-3 w-3 shrink-0 rounded-full">
                                                            {isCompleted ? (
                                                                <div className="h-3 w-3 rounded-full bg-green-500"></div>
                                                            ) : isCurrent && !isCancelled ? (
                                                                <div className="h-3 w-3 rounded-full bg-green-500"></div>
                                                            ) : isNext && !isCancelled ? (
                                                                <div className="h-3 w-3 rounded-full bg-blue-500 animate-pulse"></div>
                                                            ) : isCancelled && statusKey === 'cancelado' ? (
                                                                <div className="h-3 w-3 rounded-full bg-red-500"></div>
                                                            ) : (
                                                                <div className="h-3 w-3 rounded-full bg-gray-400"></div>
                                                            )}
                                                        </div>

                                                        <div className="flex-1">
                                                            <p className="text-sm font-medium text-card-foreground">{info.title}</p>
                                                            <p className="text-xs text-muted-foreground">{info.description}</p>
                                                        </div>

                                                        {/* Status icon */}
                                                        <div className={`flex h-4 w-4 shrink-0 items-center justify-center rounded-full ${
                                                            isCompleted ? 'bg-green-100' :
                                                            (isCurrent && !isCancelled) ? 'bg-green-100' :
                                                            (isNext && !isCancelled) ? 'bg-blue-100' :
                                                            (isCancelled && statusKey === 'cancelado') ? 'bg-red-100' : 'bg-gray-100'
                                                        }`}>
                                                            {/* Renderiza ícone baseado no tipo */}
                                                            {isCompleted ? (
                                                                <svg className="h-2.5 w-2.5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                                </svg>
                                                            ) : (isCurrent && !isCancelled) ? (
                                                                <svg className="h-2.5 w-2.5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                                </svg>
                                                            ) : (isNext && !isCancelled) ? (
                                                                <svg className="h-2.5 w-2.5 animate-spin text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                                                </svg>
                                                            ) : statusKey === 'cancelado' && isCancelled ? (
                                                                <svg className="h-2.5 w-2.5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 3 010 1.414L11.414 10l4.293 4.293a1 1 0 101.415-1.415L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                                                </svg>
                                                            ) : (
                                                                <svg className="h-2.5 w-2.5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                                                </svg>
                                                            )}
                                                        </div>
                                                    </div>
                                                );
                                            });
                                        })()}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
                        {paymentType === 'teste' ? (
                            <Button asChild className="bg-primary text-primary-foreground hover:bg-primary/90">
                                <Link href="/teste-pagamento" className="flex items-center gap-2">
                                    Voltar ao Teste de Pagamento
                                    <ArrowRight className="h-4 w-4" />
                                </Link>
                            </Button>
                        ) : (
                            <>
                                <Button asChild className="bg-primary text-primary-foreground hover:bg-primary/90">
                                    <Link
                                        href={agendamentoId ? `/paciente/agendamentos/${agendamentoId}` : '/paciente/agendamentos'}
                                        className="flex items-center gap-2"
                                    >
                                        Verificar Agendamento
                                        <ArrowRight className="h-4 w-4" />
                                    </Link>
                                </Button>
                                <Button variant="outline" asChild className="border-border bg-transparent">
                                    <Link href="/paciente/dashboard" className="flex items-center gap-2">
                                        <Home className="h-4 w-4" />
                                        Voltar ao Dashboard
                                    </Link>
                                </Button>
                            </>
                        )}
                    </div>

                    {/* Support Section */}
                    <Card className="border-border bg-muted/50">
                        <CardContent className="pt-6 text-center">
                            <h3 className="mb-2 font-semibold text-foreground">
                                {paymentType === 'teste' ? 'Dúvidas sobre Testes?' : 'Precisa de Ajuda?'}
                            </h3>
                            <p className="mb-4 text-sm text-muted-foreground">
                                {paymentType === 'teste'
                                    ? 'Esta é uma transação de teste. Para pagamentos reais, nossa equipe está disponível.'
                                    : 'Nossa equipe de suporte está sempre pronta para ajudar você.'}
                            </p>
                            <div className="flex flex-col gap-2 sm:flex-row sm:justify-center">
                                <Button variant="outline" size="sm" asChild>
                                    <Link href="/ajuda">{paymentType === 'teste' ? 'Sobre Testes' : 'Central de Ajuda'}</Link>
                                </Button>
                                <Button variant="outline" size="sm" asChild>
                                    <Link href="/contato">Falar com Suporte</Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
}
