# Sistema de Afiliados com Simulação do Mercado Pago

## Visão Geral

Este sistema permite rastrear afiliados e aplicar comissões quando pagamentos são confirmados. Para desenvolvimento local, implementamos um simulador do Mercado Pago que permite testar todo o fluxo sem precisar de credenciais reais.

## Como Funciona

### 1. Rastreamento de Afiliados

- Quando um usuário acessa um link com `?ref=CODIGO_AFILIADO`, o sistema:
  - Salva o código em cookies (30 dias) e sessão
  - Valida se o afiliado existe e está ativo
  - Registra o clique para auditoria

### 2. Processo de Pagamento

- Quando o usuário inicia um pagamento:
  - Sistema verifica se há afiliado rastreado nos cookies/sessão
  - Se houver, cria uma `VendaAfiliado` temporária (status: pendente)
  - Gera um `transaction_id` único e associa à venda
  - Redireciona para o simulador do Mercado Pago (em localhost)

### 3. Simulação do Mercado Pago

- Em ambiente local (`APP_ENV=local`), o sistema usa o simulador
- Gera links de pagamento simulados: `/mercadopago/simulator/payment/{transaction_id}`
- Permite aprovar, rejeitar ou cancelar pagamentos
- Dispara webhooks simulados automaticamente

### 4. Confirmação via Webhook

- Quando o webhook é recebido (real ou simulado):
  - Busca a `VendaAfiliado` pelo `transaction_id`
  - Se o pagamento foi aprovado, confirma a venda
  - Aplica a comissão ao afiliado
  - Atualiza estatísticas do afiliado

## Configuração

### Variáveis de Ambiente

```env

# Configurações normais do Mercado Pago
MERCADOPAGO_ACCESS_TOKEN=your_token
MERCADOPAGO_PUBLIC_KEY=your_public_key
MERCADOPAGO_SANDBOX=true
```

### Banco de Dados

Execute as migrations:

```bash
php artisan migrate
```

Isso adicionará o campo `transaction_id` na tabela `vendas_afiliado`.

## Testando o Sistema

### 1. Criar um Afiliado

1. Acesse `/admin/afiliados`
2. Crie um novo afiliado
3. Aprove o afiliado
4. Anote o código do afiliado (ex: AF12345678)

### 2. Simular Acesso via Link de Afiliado

1. Acesse: `http://localhost:8000?ref=AF12345678`
2. Verifique nos logs que o afiliado foi rastreado
3. O código ficará salvo nos cookies por 30 dias

### 3. Fazer um Pagamento

1. Faça login como um usuário diferente do afiliado
2. Acesse uma página de pagamento
3. Inicie o processo de pagamento
4. Será redirecionado para o simulador

### 4. Simular Aprovação

**Opção 1: Via Interface do Usuário**
1. Na página do simulador, clique em "Aprovar Pagamento"
2. Será redirecionado para página de sucesso
3. Webhook será disparado automaticamente

**Opção 2: Via Interface Admin**
1. Acesse `/admin/mercadopago-simulator`
2. Veja todos os pagamentos pendentes
3. Clique em "Aprovar" para simular o webhook

### 5. Verificar Comissão

1. Acesse `/admin/afiliados`
2. Veja que as estatísticas do afiliado foram atualizadas
3. Verifique a tabela `vendas_afiliado` no banco

## Estrutura de Arquivos

### Backend

- `app/Services/MercadoPagoSimulatorService.php` - Lógica de simulação
- `app/Http/Controllers/MercadoPagoSimulatorController.php` - Controller do simulador
- `app/Services/MercadoPagoService.php` - Modificado para usar simulador
- `app/Http/Controllers/MercadoPagoWebhookController.php` - Webhook atualizado
- `app/Http/Controllers/Paciente/PagamentoController.php` - Captura afiliado

### Frontend

- `resources/js/pages/Simulator/MercadoPagoPayment.tsx` - Página de pagamento simulado
- `resources/js/pages/Simulator/PaymentSuccess.tsx` - Página de sucesso
- `resources/js/pages/Simulator/PaymentFailure.tsx` - Página de falha
- `resources/js/pages/Admin/MercadoPagoSimulator.tsx` - Interface admin

### Rotas

```php
// Simulador (público)
Route::prefix('mercadopago/simulator')->group(function () {
    Route::get('/payment/{transaction_id}', 'paymentPage');
    Route::post('/payment/{transaction_id}/process', 'processPayment');
    Route::get('/success/{transaction_id}', 'success');
    Route::get('/failure/{transaction_id}', 'failure');
});

// Admin (apenas admins)
Route::middleware(['auth', 'role:admin'])->group(function () {
    Route::get('/admin/mercadopago-simulator', 'adminInterface');
    Route::post('/admin/mercadopago-simulator/webhook', 'simulateWebhookAdmin');
});
```

## Logs e Debugging

O sistema gera logs detalhados em `storage/logs/laravel.log`:

- Rastreamento de afiliados
- Criação de vendas temporárias
- Simulação de pagamentos
- Confirmação de comissões
- Erros e exceções

Procure por:
- `Affiliate referral tracked`
- `Afiliado detectado no pagamento`
- `Simulação Mercado Pago`
- `Comissão de afiliado confirmada`

## Produção

Em produção, o sistema automaticamente usará o Mercado Pago real quando:
- `APP_ENV` não for `local`
- Credenciais do Mercado Pago estiverem configuradas

O fluxo de afiliados funciona identicamente, apenas usando o Mercado Pago real em vez do simulador.

## Troubleshooting

### Afiliado não está sendo rastreado
- Verifique se o código está correto
- Confirme que o afiliado está aprovado e ativo
- Verifique os logs para erros

### Comissão não está sendo aplicada
- Confirme que a venda foi criada com `transaction_id`
- Verifique se o webhook está sendo disparado
- Confirme que o status do pagamento mudou para "pago"

### Simulador não está funcionando
- Confirme que `APP_ENV=local`
- Verifique se as rotas estão registradas
- Confirme que os arquivos React foram compilados
