<?php

require_once 'vendor/autoload.php';

use App\Models\User;

try {
    // Inicializar <PERSON>
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    echo "=== Verificação do Usuário Paciente (ID: 1) ===\n\n";

    $user = User::with('assinaturas')->find(1);

    if ($user) {
        echo "✅ Usuário encontrado!\n";
        echo "   ID: " . $user->id . "\n";
        echo "   Nome: " . $user->name . "\n";
        echo "   Email: " . $user->email . "\n";
        echo "   Role: " . $user->role . "\n";
        echo "   Ativo: " . ($user->active ? 'Sim' : 'Não') . "\n";
        echo "   Has Subscription: " . ($user->has_subscription ? 'Sim' : 'Não') . "\n";
        echo "   Onboarding Completed: " . ($user->onboarding_completed ? 'Sim' : 'Não') . "\n";

        $assinaturasAtivas = $user->assinaturas->where('status', 'ativa');
        if ($assinaturasAtivas->count() > 0) {
            $assinatura = $assinaturasAtivas->first();
            echo "\n📋 Detalhes da Assinatura Ativa:\n";
            echo "   ID: " . $assinatura->id . "\n";
            echo "   Status: " . $assinatura->status . "\n";
            echo "   Plano: " . ($assinatura->plano ?? 'N/A') . "\n";
            echo "   Valor: R$ " . number_format($assinatura->amount ?? 0, 2, ',', '.') . "\n";
            echo "   Inicio: " . $assinatura->start_date . "\n";
            echo "   Fim: " . $assinatura->end_date . "\n";
            echo "   Pagamento Status: " . ($assinatura->payment_status ?? 'N/A') . "\n";
        } else {
            echo "\n❌ Usuário SEM assinatura ativa!\n";
            echo "   Total de assinaturas: " . $user->assinaturas->count() . "\n";
            foreach ($user->assinaturas as $ass) {
                echo "   - ID {$ass->id}: Status {$ass->status} [{$ass->start_date} - {$ass->end_date}]\n";
            }
        }

        // Verificar dados médicos
        echo "\n🏥 Dados Médicos:\n";
        echo "   Telefone: " . ($user->phone ? $user->phone : '❌ Não informado') . "\n";
        echo "   Data nascimento: " . ($user->birth_date ? $user->birth_date : '❌ Não informado') . "\n";
        echo "   Gênero: " . ($user->gender ? $user->gender : '❌ Não informado') . "\n";
        echo "   Endereço: " . ($user->address ? $user->address : '❌ Não informado') . "\n";
        echo "   Cidade: " . ($user->city ? $user->city : '❌ Não informado') . "\n";
        echo "   Estado: " . ($user->state ? $user->state : '❌ Não informado') . "\n";
        echo "   CEP: " . ($user->zip_code ? $user->zip_code : '❌ Não informado') . "\n";
        echo "   Histórico médico: " . ($user->medical_history ? substr($user->medical_history, 0, 50) . '...' : '❌ Não informado') . "\n";
        echo "   Contato emergência: " . ($user->emergency_contact ? substr($user->emergency_contact, 0, 50) . '...' : '❌ Não informado') . "\n";

    } else {
        echo "❌ Usuário ID 1 não encontrado!\n";
    }

} catch (\Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
