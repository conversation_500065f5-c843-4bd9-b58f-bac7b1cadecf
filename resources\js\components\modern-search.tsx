import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { MapPin, Phone, Star, Search, Navigation, Filter, Loader2, Clock, ExternalLink } from 'lucide-react';
import { useState } from 'react';

interface Estabelecimento {
    id: number;
    nome: string;
    categoria: string;
    descricao?: string;
    telefone?: string;
    whatsapp: string;
    whatsapp_link: string;
    endereco_completo: string;
    distancia: number;
    avaliacao_media: number;
    total_avaliacoes: number;
    horario_funcionamento?: any;
}

interface BuscaResponse {
    success: boolean;
    estabelecimentos: Estabelecimento[];
    total: number;
    coordenadas: {
        lat: number;
        lng: number;
    };
    message?: string;
}

const categoriaLabels = {
    dentista: 'Dentista',
    farmacia: 'Farmácia',
    fisioterapia: 'Fisioterapia',
    outros: 'Outros',
};

const categoriaEmojis = {
    dentista: '🦷',
    farmacia: '💊',
    fisioterapia: '💪',
    outros: '🏥',
};

const categoriaColors = {
    dentista: 'bg-blue-50 text-blue-700 border-blue-200',
    farmacia: 'bg-green-50 text-green-700 border-green-200',
    fisioterapia: 'bg-purple-50 text-purple-700 border-purple-200',
    outros: 'bg-gray-50 text-gray-700 border-gray-200',
};

export default function ModernSearch() {
    const [localizacao, setLocalizacao] = useState('');
    const [categoria, setCategoria] = useState('todos');
    const [raio, setRaio] = useState(10);
    const [estabelecimentos, setEstabelecimentos] = useState<Estabelecimento[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [buscaRealizada, setBuscaRealizada] = useState(false);

    const buscarEstabelecimentos = async () => {
        if (!localizacao.trim()) {
            setError('Por favor, informe sua localização');
            return;
        }

        setLoading(true);
        setError('');

        try {
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
            const response = await fetch('/api/estabelecimentos/buscar', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    Accept: 'application/json',
                },
                body: JSON.stringify({
                    localizacao,
                    categoria: categoria === 'todos' ? null : categoria,
                    raio,
                }),
            });

            const data: BuscaResponse = await response.json();

            if (data.success) {
                setEstabelecimentos(data.estabelecimentos);
                setBuscaRealizada(true);
            } else {
                setError(data.message || 'Erro ao buscar estabelecimentos');
            }
        } catch (err) {
            setError('Erro ao conectar com o servidor');
        } finally {
            setLoading(false);
        }
    };

    const obterLocalizacaoAtual = () => {
        if (!navigator.geolocation) {
            setError('Geolocalização não é suportada pelo seu navegador');
            return;
        }

        setLoading(true);
        navigator.geolocation.getCurrentPosition(
            async (position) => {
                const { latitude, longitude } = position.coords;

                try {
                    const response = await fetch(
                        `https://nominatim.openstreetmap.org/reverse?lat=${latitude}&lon=${longitude}&format=json&countrycodes=br`,
                    );
                    const data = await response.json();

                    if (data.display_name) {
                        const endereco = `${data.address?.city || data.address?.town || data.address?.village || ''}, ${data.address?.state || ''}`;
                        setLocalizacao(endereco);
                    } else {
                        setLocalizacao(`${latitude}, ${longitude}`);
                    }
                } catch (err) {
                    setLocalizacao(`${latitude}, ${longitude}`);
                } finally {
                    setLoading(false);
                }
            },
            (error) => {
                setError('Não foi possível obter sua localização');
                setLoading(false);
            },
        );
    };

    return (
        <div className="w-full">
            {/* Search Form - Modern Design */}
            <div className="mx-auto max-w-4xl">
                <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl">
                    <CardContent className="p-8">
                        {/* Search Input */}
                        <div className="mb-6">
                            <div className="relative">
                                <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
                                <Input
                                    placeholder="Digite seu CEP, cidade ou bairro..."
                                    value={localizacao}
                                    onChange={(e) => setLocalizacao(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && buscarEstabelecimentos()}
                                    className="h-14 pl-12 pr-32 text-lg border-2 border-gray-200 focus:border-primary rounded-xl"
                                />
                                <Button
                                    onClick={obterLocalizacaoAtual}
                                    disabled={loading}
                                    variant="ghost"
                                    size="sm"
                                    className="absolute right-2 top-1/2 -translate-y-1/2 text-primary hover:text-primary/80"
                                >
                                    <Navigation className="h-4 w-4 mr-1" />
                                    Usar GPS
                                </Button>
                            </div>
                        </div>

                        {/* Filters */}
                        <div className="mb-6 flex flex-wrap gap-4">
                            {/* Category Filter */}
                            <div className="flex-1 min-w-[200px]">
                                <div className="flex items-center gap-2 mb-2">
                                    <Filter className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm font-medium text-muted-foreground">Categoria</span>
                                </div>
                                <select
                                    value={categoria}
                                    onChange={(e) => setCategoria(e.target.value)}
                                    className="w-full h-12 px-4 border-2 border-gray-200 rounded-lg focus:border-primary focus:outline-none"
                                >
                                    <option value="todos">Todos os serviços</option>
                                    <option value="dentista">🦷 Dentistas</option>
                                    <option value="farmacia">💊 Farmácias</option>
                                    <option value="fisioterapia">💪 Fisioterapia</option>
                                    <option value="outros">🏥 Outros</option>
                                </select>
                            </div>

                            {/* Radius Filter */}
                            <div className="flex-1 min-w-[150px]">
                                <div className="flex items-center gap-2 mb-2">
                                    <MapPin className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm font-medium text-muted-foreground">Raio</span>
                                </div>
                                <select
                                    value={raio}
                                    onChange={(e) => setRaio(Number(e.target.value))}
                                    className="w-full h-12 px-4 border-2 border-gray-200 rounded-lg focus:border-primary focus:outline-none"
                                >
                                    <option value={5}>5 km</option>
                                    <option value={10}>10 km</option>
                                    <option value={20}>20 km</option>
                                    <option value={50}>50 km</option>
                                </select>
                            </div>
                        </div>

                        {/* Search Button */}
                        <Button
                            onClick={buscarEstabelecimentos}
                            disabled={loading}
                            className="w-full h-14 text-lg font-semibold rounded-xl bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-200"
                        >
                            {loading ? (
                                <>
                                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                                    Buscando...
                                </>
                            ) : (
                                <>
                                    <Search className="mr-2 h-5 w-5" />
                                    Buscar Profissionais
                                </>
                            )}
                        </Button>

                        {error && (
                            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                                <p className="text-red-600 text-sm">{error}</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Results */}
            {buscaRealizada && (
                <div className="mt-12 mx-auto max-w-6xl">
                    <div className="mb-8 text-center">
                        <h3 className="text-2xl font-bold text-foreground mb-2">
                            {estabelecimentos.length > 0
                                ? `${estabelecimentos.length} estabelecimento${estabelecimentos.length > 1 ? 's' : ''} encontrado${estabelecimentos.length > 1 ? 's' : ''}`
                                : 'Nenhum estabelecimento encontrado'}
                        </h3>
                        {estabelecimentos.length > 0 && (
                            <p className="text-muted-foreground">
                                Resultados próximos à sua localização
                            </p>
                        )}
                    </div>

                    {estabelecimentos.length === 0 ? (
                        <Card className="border-2 border-dashed border-gray-200">
                            <CardContent className="py-16 text-center">
                                <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-gray-100 flex items-center justify-center">
                                    <Search className="h-8 w-8 text-gray-400" />
                                </div>
                                <h4 className="text-lg font-semibold text-gray-900 mb-2">Nenhum resultado encontrado</h4>
                                <p className="text-gray-600 mb-4">
                                    Não encontramos estabelecimentos na sua região.
                                </p>
                                <p className="text-sm text-gray-500">
                                    Tente aumentar o raio de busca ou verificar se a localização está correta.
                                </p>
                            </CardContent>
                        </Card>
                    ) : (
                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                            {estabelecimentos.map((estabelecimento) => (
                                <EstabelecimentoCard key={estabelecimento.id} estabelecimento={estabelecimento} />
                            ))}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}

function EstabelecimentoCard({ estabelecimento }: { estabelecimento: Estabelecimento }) {
    return (
        <Card className="h-full hover:shadow-lg transition-all duration-200 border-2 hover:border-primary/20">
            <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                            <span className="text-2xl">{categoriaEmojis[estabelecimento.categoria as keyof typeof categoriaEmojis]}</span>
                            <h3 className="font-semibold text-lg text-foreground line-clamp-1">{estabelecimento.nome}</h3>
                        </div>
                        <div className="flex items-center gap-3 mb-3">
                            <Badge 
                                variant="secondary" 
                                className={`${categoriaColors[estabelecimento.categoria as keyof typeof categoriaColors]} border`}
                            >
                                {categoriaLabels[estabelecimento.categoria as keyof typeof categoriaLabels]}
                            </Badge>
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                <MapPin className="h-3 w-3" />
                                {estabelecimento.distancia} km
                            </div>
                        </div>
                    </div>
                </div>

                {estabelecimento.descricao && (
                    <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                        {estabelecimento.descricao}
                    </p>
                )}

                <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <MapPin className="h-4 w-4 flex-shrink-0" />
                        <span className="line-clamp-1">{estabelecimento.endereco_completo}</span>
                    </div>

                    {estabelecimento.avaliacao_media > 0 && (
                        <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                <span className="text-sm font-medium">{estabelecimento.avaliacao_media.toFixed(1)}</span>
                            </div>
                            <span className="text-xs text-muted-foreground">
                                ({estabelecimento.total_avaliacoes} avaliações)
                            </span>
                        </div>
                    )}

                    <div className="pt-4 border-t">
                        <a
                            href={estabelecimento.whatsapp_link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-full inline-flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg font-medium transition-colors"
                        >
                            <Phone className="h-4 w-4" />
                            Contatar via WhatsApp
                            <ExternalLink className="h-3 w-3" />
                        </a>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
