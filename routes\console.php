<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use App\Models\Assinatura;
use Carbon\Carbon;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Configurar schedule de backups automáticos
Schedule::command('backup:schedule full')
    ->dailyAt('02:00')
    ->name('backup-full')
    ->withoutOverlapping()
    ->runInBackground();

// Limpeza de backups antigos semanalmente
Schedule::job(new \App\Jobs\CleanupOldBackupsJob(30))
    ->weekly()
    ->sundays()
    ->at('03:00');

// Backup apenas do banco de dados a cada 6 horas
Schedule::command('backup:schedule database')
    ->everySixHours()
    ->name('backup-database-six-hourly')
    ->withoutOverlapping()
    ->runInBackground();

// Reset mensal das assinaturas (zerar uso e atualizar período) no dia 1 às 00:10
Schedule::command('assinaturas:reset-mensal')
    ->monthlyOn(1, '00:10')
    ->name('assinaturas-reset-mensal')
    ->withoutOverlapping()
    ->runInBackground();

// Finalizar assinaturas cujo período terminou (cancel_at_period_end = true)
Schedule::command('assinaturas:finalizar-periodo')
    ->dailyAt('00:20')
    ->name('assinaturas-finalizar-periodo')
    ->withoutOverlapping()
    ->runInBackground();

if (app()->environment(['local', 'development'])) {
    // Em desenvolvimento, rodar com mais frequência para testes
    Schedule::command('assinaturas:finalizar-periodo')
        ->everyTenMinutes()
        ->name('assinaturas-finalizar-periodo-dev')
        ->withoutOverlapping()
        ->runInBackground();
}

// Fallback: executar lógica de reset via closure (caso o comando não esteja disponível)
Schedule::call(function () {
    $hoje = Carbon::now();
    $inicioMes = $hoje->copy()->startOfMonth();
    $fimMes = $hoje->copy()->endOfMonth();

    // 0) Aplicar trocas de plano agendadas para este novo ciclo
    Assinatura::where('status', 'ativa')
        ->whereNotNull('scheduled_new_plano_id')
        ->whereDate('scheduled_change_date', '<=', $inicioMes)
        ->chunkById(500, function ($assinaturas) use ($inicioMes, $fimMes) {
            foreach ($assinaturas as $assinatura) {
                $assinatura->plano_id = $assinatura->scheduled_new_plano_id;
                $assinatura->scheduled_new_plano_id = null;
                $assinatura->scheduled_change_date = null;
                $assinatura->sessions_used = 0;
                $assinatura->current_period_start = $inicioMes;
                $assinatura->current_period_end = $fimMes;
                $assinatura->start_date = $inicioMes;
                $assinatura->end_date = $fimMes;
                $assinatura->save();
            }
        });

    // 1) Finalizar assinaturas com cancelamento agendado cujo período acabou antes do mês atual
    Assinatura::where('status', 'ativa')
        ->where('cancel_at_period_end', true)
        ->whereDate('end_date', '<', $inicioMes)
        ->chunkById(500, function ($assinaturas) {
            foreach ($assinaturas as $assinatura) {
                $assinatura->status = 'cancelada';
                $assinatura->cancel_at_period_end = false;
                $assinatura->save();
            }
        });

    // 2) Reset das demais assinaturas ativas
    Assinatura::where('status', 'ativa')
        ->chunkById(500, function ($assinaturas) use ($inicioMes, $fimMes) {
            foreach ($assinaturas as $assinatura) {
                $assinatura->sessions_used = 0;
                $assinatura->current_period_start = $inicioMes;
                $assinatura->current_period_end = $fimMes;
                $assinatura->start_date = $inicioMes;
                $assinatura->end_date = $fimMes;
                $assinatura->save();
            }
        });
})
->monthlyOn(1, '00:15')
->name('assinaturas-reset-fallback')
->withoutOverlapping()
;
