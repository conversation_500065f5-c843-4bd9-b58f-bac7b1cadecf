import { cn } from '@/lib/utils';
import React from 'react';

interface PatientCardProps {
  icon?: React.ReactNode;
  title: string;
  subtitle?: string;
  value?: React.ReactNode;
  helperText?: React.ReactNode;
  valueClassName?: string;
  className?: string;
  children?: React.ReactNode; // custom body overrides value/helper block
}

export default function PatientCard({
  icon,
  title,
  subtitle,
  value,
  helperText,
  valueClassName,
  className,
  children,
}: PatientCardProps) {
  return (
    <div className={cn('rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10', className)}>
      <div className="mb-4 flex items-center gap-3">
        {icon}
        <div>
          <p className="text-sm font-medium text-foreground">{title}</p>
          {subtitle ? <p className="text-xs text-muted-foreground">{subtitle}</p> : null}
        </div>
      </div>
      {children ? (
        children
      ) : (
        <div>
          {value !== undefined && (
            <div className={cn('mb-1 text-2xl font-semibold text-primary', valueClassName)}>{value}</div>
          )}
          {helperText ? <p className="text-sm text-muted-foreground">{helperText}</p> : null}
        </div>
      )}
    </div>
  );
}
