import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import PublicLayout from '@/layouts/public-layout';
import { Link, usePage } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, CreditCard, Home, Users } from 'lucide-react';
import { useEffect, useState } from 'react';

interface PlanoInfo {
    nome: string;
    preco: number;
    periodo: string;
    descricao: string;
    recursos: string[];
}

interface PagamentoInfo {
    id: string;
    data: string;
    status: string;
    metodo: string;
    cardLastFour?: string;
    externalReference?: string;
    amount?: string;
    currency?: string;
    installments?: string;
}

export default function MercadoPagoSuccess() {
    const { url } = usePage();
    const [planoInfo, setPlanoInfo] = useState<PlanoInfo | null>(null);
    const [pagamentoInfo, setPagamentoInfo] = useState<PagamentoInfo>({
        id: '',
        data: new Date().toLocaleDateString('pt-BR'),
        status: 'Aprovado',
        metodo: 'Cartão de Crédito',
    });

    useEffect(() => {
        // Extrair parâmetros da URL
        const urlParams = new URLSearchParams(url.split('?')[1] || '');
        
        // Obter dados da transação da URL
        const transactionId = urlParams.get('transaction_id') || 'MP' + Math.random().toString(36).substr(2, 9).toUpperCase();
        const status = urlParams.get('status') || 'approved';
        const paymentMethod = urlParams.get('payment_method') || 'credit_card';
        const cardLastFour = urlParams.get('card_last_four') || '';
        const externalReference = urlParams.get('external_reference') || '';
        const amount = urlParams.get('amount') || '';
        const currency = urlParams.get('currency') || 'BRL';
        const installments = urlParams.get('installments') || '1';
        
        // Obter dados do plano da URL
        const planName = urlParams.get('plan_name') || 'Plano Pessoal';
        const planPrice = parseFloat(urlParams.get('plan_price') || '180.00');
        const planPeriod = urlParams.get('plan_period') || 'mês';
        const planDescription = urlParams.get('plan_descricao') || 'Atendimento fisioterapêutico domiciliar personalizado';

        // Definir informações do plano
        const plano: PlanoInfo = {
            nome: planName,
            preco: planPrice,
            periodo: planPeriod,
            descricao: planDescription,
            recursos: [
                'Todos os recursos do Plano Busca',
                'Agendamento de consultas domiciliares',
                'Fisioterapeutas qualificados',
                'Atendimento personalizado',
                'Relatórios de avaliação',
                'Suporte telefônico',
                'Cancelamento flexível',
                'Histórico médico digital',
            ],
        };
        setPlanoInfo(plano);

        // Definir informações do pagamento
        setPagamentoInfo({
            id: transactionId,
            data: new Date().toLocaleDateString('pt-BR'),
            status: status === 'approved' ? 'Aprovado' : 'Pendente',
            metodo: paymentMethod === 'credit_card' ? 'Cartão de Crédito' : 'Cartão de Débito',
            cardLastFour,
            externalReference,
            amount,
            currency,
            installments
        });
    }, [url]);

    if (!planoInfo) {
        return (
            <PublicLayout title="Carregando..." description="Carregando informações do pagamento">
                <div className="flex min-h-screen items-center justify-center">
                    <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-primary"></div>
                </div>
            </PublicLayout>
        );
    }

    return (
        <PublicLayout title="Pagamento Aprovado - Mercado Pago" description="Pagamento realizado com sucesso">
            <div className="min-h-screen bg-gradient-to-b from-background to-muted/30 py-20">
                <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                    {/* Header de Sucesso */}
                    <div className="mb-12 text-center">
                        <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
                            <CheckCircle className="h-12 w-12 text-green-600" />
                        </div>
                        <h1 className="mb-4 text-4xl font-bold text-green-600">Pagamento Aprovado!</h1>
                        <p className="text-xl text-muted-foreground">Seu plano foi ativado com sucesso. Bem-vindo à F4 Fisio!</p>
                    </div>

                    {/* Informações do Plano */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-3">
                                <div className="rounded-full bg-green-50 p-2">
                                    <Users className="h-6 w-6 text-green-600" />
                                </div>
                                {planoInfo.nome}
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div>
                                    <h3 className="mb-3 font-semibold">Detalhes do Plano</h3>
                                    <div className="space-y-2">
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Preço:</span>
                                            <span className="font-semibold">
                                                R$ {planoInfo.preco.toFixed(2).replace('.', ',')}/{planoInfo.periodo}
                                            </span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Descrição:</span>
                                            <span className="font-semibold">{planoInfo.descricao}</span>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <h3 className="mb-3 font-semibold">Recursos Inclusos</h3>
                                    <div className="space-y-2">
                                        {planoInfo.recursos.slice(0, 4).map((recurso, index) => (
                                            <div key={index} className="flex items-center gap-2">
                                                <CheckCircle className="h-4 w-4 text-green-600" />
                                                <span className="text-sm">{recurso}</span>
                                            </div>
                                        ))}
                                        {planoInfo.recursos.length > 4 && (
                                            <div className="text-sm text-muted-foreground">+{planoInfo.recursos.length - 4} recursos adicionais</div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Informações do Pagamento */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-3">
                                <div className="rounded-full bg-blue-50 p-2">
                                    <CreditCard className="h-6 w-6 text-blue-600" />
                                </div>
                                Detalhes do Pagamento
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div className="space-y-4">
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">ID da Transação:</span>
                                        <span className="font-mono font-semibold">{pagamentoInfo.id}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Data:</span>
                                        <span className="font-semibold">{pagamentoInfo.data}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Status:</span>
                                        <Badge className="bg-green-100 text-green-800">{pagamentoInfo.status}</Badge>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Método:</span>
                                        <span className="font-semibold">{pagamentoInfo.metodo}</span>
                                    </div>
                                    {pagamentoInfo.cardLastFour && (
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Últimos 4 dígitos:</span>
                                            <span className="font-mono font-semibold">**** {pagamentoInfo.cardLastFour}</span>
                                        </div>
                                    )}
                                    {pagamentoInfo.externalReference && (
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Referência:</span>
                                            <span className="font-mono font-semibold">{pagamentoInfo.externalReference}</span>
                                        </div>
                                    )}
                                    {pagamentoInfo.amount && (
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Valor:</span>
                                            <span className="font-semibold">R$ {parseFloat(pagamentoInfo.amount).toFixed(2).replace('.', ',')}</span>
                                        </div>
                                    )}
                                    {pagamentoInfo.installments && (
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Parcelas:</span>
                                            <span className="font-semibold">{pagamentoInfo.installments}x</span>
                                        </div>
                                    )}
                                </div>
                                <div className="rounded-lg bg-blue-50 p-4">
                                    <h4 className="mb-2 font-semibold text-blue-800">Próximos Passos</h4>
                                    <ul className="space-y-1 text-sm text-blue-700">
                                        <li>• Seu plano foi ativado automaticamente</li>
                                        <li>• Você receberá um e-mail de confirmação</li>
                                        <li>• Acesse sua área do paciente para agendar consultas</li>
                                        <li>• Suporte disponível via WhatsApp</li>
                                    </ul>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Dados de Teste */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="text-yellow-800">🧪 Dados de Teste Utilizados</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="rounded-lg bg-yellow-50 p-4">
                                <p className="mb-3 text-sm text-yellow-800">
                                    <strong>Importante:</strong> Esta transação foi realizada em ambiente de teste.
                                </p>
                                <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-3">
                                    <div>
                                        <strong>Conta de Teste:</strong>
                                        <p className="font-mono">TESTUSER9211...</p>
                                    </div>
                                    <div>
                                        <strong>User ID:</strong>
                                        <p className="font-mono">2608760894</p>
                                    </div>
                                    <div>
                                        <strong>CPF:</strong>
                                        <p className="font-mono">12345678909</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Botões de Ação */}
                    <div className="flex flex-col justify-center gap-4 sm:flex-row">
                        <Button asChild size="lg" className="bg-primary hover:bg-primary/90">
                            <Link href="/dashboard">
                                <Home className="mr-2 h-5 w-5" />
                                Ir para Dashboard
                            </Link>
                        </Button>

                        <Button asChild variant="outline" size="lg">
                            <Link href="/teste-mercadopago">
                                <ArrowLeft className="mr-2 h-5 w-5" />
                                Testar Novamente
                            </Link>
                        </Button>
                    </div>

                    {/* Mensagem Final */}
                    <div className="mt-12 text-center">
                        <p className="text-muted-foreground">
                            Obrigado por escolher a F4 Fisio! Seu atendimento fisioterapêutico domiciliar está pronto para começar.
                        </p>
                    </div>
                </div>
            </div>
        </PublicLayout>
    );
}
