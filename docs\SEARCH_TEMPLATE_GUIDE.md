# Guia do Template de Busca Padronizado

Este documento explica como usar o componente `SearchTemplate` para criar interfaces de busca consistentes em toda a aplicação.

## Visão Geral

O `SearchTemplate` é um componente reutilizável que padroniza a interface de busca, incluindo:
- Campo de busca principal
- Filtros avançados (select, input, checkbox, range)
- Estados de loading e erro
- Paginação
- Contadores de resultados
- Estados vazios customizáveis

## Importação

```tsx
import SearchTemplate, { SearchConfig, useSearchState } from '@/components/search-template';
```

## Uso Básico

### 1. Busca Simples

```tsx
import SearchTemplate, { SearchConfig, useSearchState } from '@/components/search-template';

export function SimpleSearch() {
    const { state, updateFilter, setLoading, setError } = useSearchState();

    const config: SearchConfig = {
        title: 'Buscar Usuários',
        description: 'Encontre usuários cadastrados no sistema',
        placeholder: 'Digite o nome ou email...',
        showResults: true,
    };

    const handleSearch = async (query: string, filters: Record<string, any>) => {
        setLoading(true);
        try {
            // Sua lógica de busca aqui
            const response = await fetch('/api/search', {
                method: 'POST',
                body: JSON.stringify({ query, filters }),
            });
            const data = await response.json();
            // Processar resultados
        } catch (error) {
            setError('Erro ao buscar');
        } finally {
            setLoading(false);
        }
    };

    return (
        <SearchTemplate
            config={config}
            state={state}
            onSearch={handleSearch}
            onFilterChange={updateFilter}
            resultCount={0}
        />
    );
}
```

### 2. Busca com Filtros Avançados

```tsx
const config: SearchConfig = {
    title: 'Buscar Pacientes',
    showAdvancedFilters: true,
    showResults: true,
    showPagination: true,
    filters: [
        {
            key: 'status',
            label: 'Status',
            type: 'select',
            options: [
                { value: 'ativo', label: 'Ativo' },
                { value: 'inativo', label: 'Inativo' },
            ],
        },
        {
            key: 'city',
            label: 'Cidade',
            type: 'input',
            placeholder: 'Digite a cidade...',
        },
        {
            key: 'active_only',
            label: 'Apenas ativos',
            type: 'checkbox',
        },
        {
            key: 'price_range',
            label: 'Preço Máximo',
            type: 'range',
            min: 0,
            max: 1000,
            step: 50,
        },
    ],
};
```

## Tipos de Filtros

### Select
```tsx
{
    key: 'categoria',
    label: 'Categoria',
    type: 'select',
    options: [
        { value: 'todos', label: 'Todos' },
        { value: 'ativo', label: 'Ativo' },
    ],
}
```

### Input
```tsx
{
    key: 'cidade',
    label: 'Cidade',
    type: 'input',
    placeholder: 'Digite a cidade...',
}
```

### Checkbox
```tsx
{
    key: 'ativo_apenas',
    label: 'Apenas ativos',
    type: 'checkbox',
}
```

### Range (Slider)
```tsx
{
    key: 'preco',
    label: 'Preço Máximo',
    type: 'range',
    min: 0,
    max: 1000,
    step: 50,
}
```

## Hook useSearchState

O hook `useSearchState` gerencia o estado da busca:

```tsx
const {
    state,           // Estado atual (query, filters, page, loading, error)
    updateFilter,    // Atualizar filtro ou query
    updatePage,      // Atualizar página
    setLoading,      // Definir estado de loading
    setError,        // Definir erro
    clearFilters,    // Limpar todos os filtros
    reset,           // Resetar estado completo
} = useSearchState(initialFilters);
```

## Paginação

Para usar paginação, configure `showPagination: true` e forneça o objeto pagination:

```tsx
const pagination = {
    current_page: 1,
    total_pages: 5,
    total: 50,
    per_page: 10,
    has_next_page: true,
    has_prev_page: false,
    from: 1,
    to: 10,
};

<SearchTemplate
    config={config}
    state={state}
    onSearch={handleSearch}
    onFilterChange={updateFilter}
    onPageChange={updatePage}
    pagination={pagination}
    // ...
/>
```

## Resultados Customizados

Você pode renderizar resultados customizados passando children:

```tsx
<SearchTemplate
    config={config}
    state={state}
    onSearch={handleSearch}
    onFilterChange={updateFilter}
    resultCount={results.length}
>
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {results.map((item) => (
            <Card key={item.id}>
                {/* Seu card customizado */}
            </Card>
        ))}
    </div>
</SearchTemplate>
```

## Estado Vazio Customizado

```tsx
<SearchTemplate
    // ...
    emptyState={
        <Card>
            <CardContent className="py-12 text-center">
                <p>Nenhum resultado encontrado</p>
                <Button onClick={clearSearch}>Limpar busca</Button>
            </CardContent>
        </Card>
    }
/>
```

## Configurações Disponíveis

### SearchConfig
```tsx
interface SearchConfig {
    title?: string;                    // Título do formulário
    description?: string;              // Descrição do formulário
    placeholder?: string;              // Placeholder do campo de busca
    showAdvancedFilters?: boolean;     // Mostrar filtros avançados
    filters?: SearchFilter[];          // Array de filtros
    showResults?: boolean;             // Mostrar seção de resultados
    showPagination?: boolean;          // Mostrar paginação
}
```

### SearchFilter
```tsx
interface SearchFilter {
    key: string;                       // Chave única do filtro
    label: string;                     // Label exibido
    type: 'select' | 'input' | 'checkbox' | 'range';
    options?: { value: string; label: string }[];  // Para select
    placeholder?: string;              // Para input
    min?: number;                      // Para range
    max?: number;                      // Para range
    step?: number;                     // Para range
}
```

## Exemplos de Integração

### Com Inertia.js
```tsx
import { router } from '@inertiajs/react';

const handleSearch = (query: string, filters: Record<string, any>, page = 1) => {
    router.get('/pacientes', { 
        search: query, 
        ...filters, 
        page 
    }, {
        preserveState: true,
        preserveScroll: true,
    });
};
```

### Com API Fetch
```tsx
const handleSearch = async (query: string, filters: Record<string, any>) => {
    setLoading(true);
    try {
        const response = await fetch('/api/search', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ query, filters }),
        });
        const data = await response.json();
        setResults(data.results);
    } catch (error) {
        setError('Erro na busca');
    } finally {
        setLoading(false);
    }
};
```

## Boas Práticas

1. **Use o hook useSearchState**: Sempre use o hook para gerenciar estado
2. **Filtros iniciais**: Passe filtros iniciais para o hook quando necessário
3. **Loading states**: Sempre gerencie estados de loading adequadamente
4. **Error handling**: Implemente tratamento de erro consistente
5. **Paginação**: Use paginação para grandes conjuntos de dados
6. **Debounce**: Considere implementar debounce para buscas em tempo real
7. **Persistência**: Use query parameters para persistir estado de busca

## Migração de Componentes Existentes

Para migrar componentes de busca existentes:

1. Substitua o formulário de busca pelo `SearchTemplate`
2. Configure os filtros no objeto `SearchConfig`
3. Use o hook `useSearchState` para gerenciar estado
4. Mova a lógica de busca para a função `onSearch`
5. Adapte a renderização de resultados como children

## Suporte

Para dúvidas ou melhorias no template de busca, consulte a documentação ou abra uma issue no repositório do projeto.
