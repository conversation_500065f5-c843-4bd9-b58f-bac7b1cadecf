@echo off
REM Script para executar migrações para produção - Versão Windows
REM Este script carrega as variáveis do .env.production e executa as migrações

echo 🚀 Iniciando migrações para produção...

REM Verifica se o arquivo .env.production existe
if not exist ".env.production" (
    echo ❌ Erro: Arquivo .env.production não encontrado!
    pause
    exit /b 1
)

REM Faz backup do .env atual se existir
if exist ".env" (
    echo 📦 Fazendo backup do .env atual...
    copy .env .env.backup > nul
)

REM Copia o .env.production para .env
echo 📋 Carregando configurações de produção...
copy .env.production .env > nul

REM Executa as migrações
echo 🔄 Executando migrações do banco de dados...
php artisan migrate --force

REM Verifica se as migrações foram executadas com sucesso
if %errorlevel% equ 0 (
    echo ✅ Migrações executadas com sucesso!
    
    REM Opcional: Executar seeder de produção se existir
    if exist "database\seeders\ProductionDatabaseSeeder.php" (
        echo 🌱 Executando seeders de produção...
        php artisan db:seed --class=ProductionDatabaseSeeder --force
    )
    
    REM Opcional: Otimizar o Laravel após migrações
    echo ⚡ Otimizando aplicação...
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    php artisan cache:clear
    
    echo 🎉 Processo de migração para produção concluído com sucesso!
) else (
    echo ❌ Erro ao executar migrações!
    
    REM Restaura o .env original em caso de erro
    if exist ".env.backup" (
        echo 🔄 Restaurando .env original...
        copy .env.backup .env > nul
        del .env.backup > nul
    )
    
    pause
    exit /b 1
)

REM Pergunta se deseja restaurar o .env original
echo.
set /p "restore=Deseja restaurar o .env original? (s/n): "
if /i "%restore%"=="s" (
    if exist ".env.backup" (
        echo 🔄 Restaurando .env original...
        copy .env.backup .env > nul
        del .env.backup > nul
        echo ✅ .env original restaurado!
    ) else (
        echo ℹ️  Nenhum backup do .env encontrado para restaurar.
    )
) else (
    echo ℹ️  Mantendo .env de produção ativo.
    if exist ".env.backup" del .env.backup > nul
)

echo 👋 Script finalizado!
pause
