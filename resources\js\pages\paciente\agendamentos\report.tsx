import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Activity, ArrowLeft, Calendar, Clock, FileText, Star, Target, TrendingUp, User } from 'lucide-react';

interface Agendamento {
    id: number;
    data_hora: string;
    duracao: number;
    status: string;
    observacoes?: string;
    fisioterapeuta: {
        id: number;
        user: {
            name: string;
            email: string;
        };
        especialidade?: string;
    };
}

interface RelatorioSessao {
    id: number;
    observations: string;
    exercises: string;
    progress_notes: string;
    next_steps: string;
    pain_level_before: number;
    pain_level_after: number;
    mobility_assessment: string;
    patient_satisfaction: number;
    created_at: string;
    updated_at: string;
}

interface Avaliacao {
    id: number;
    rating: number;
    comment?: string;
    recommend: boolean;
    created_at: string;
}

interface ReportProps {
    agendamento: Agendamento;
    relatorio: RelatorioSessao;
    avaliacao?: Avaliacao;
}

export default function AgendamentoReport({ agendamento, relatorio, avaliacao }: ReportProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Início', href: '/paciente/dashboard' },
        { title: 'Agendamentos', href: '/paciente/agendamentos' },
        { title: 'Relatório', href: '' },
    ];
    const formatDateTime = (dateTime: string) => {
        return new Date(dateTime).toLocaleString('pt-BR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getPainLevelColor = (level: number) => {
        if (level <= 3) return 'text-green-600 bg-green-100';
        if (level <= 6) return 'text-yellow-600 bg-yellow-100';
        return 'text-red-600 bg-red-100';
    };

    const getSatisfactionStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, i) => (
            <Star key={i} className={`h-4 w-4 ${i < rating ? 'fill-current text-yellow-400' : 'text-gray-300'}`} />
        ));
    };

    const getPainImprovement = () => {
        const improvement = relatorio.pain_level_before - relatorio.pain_level_after;
        if (improvement > 0) {
            return {
                text: `Redução de ${improvement} ponto(s)`,
                color: 'text-green-600',
                icon: <TrendingUp className="h-4 w-4 text-green-600" />,
            };
        } else if (improvement < 0) {
            return {
                text: `Aumento de ${Math.abs(improvement)} ponto(s)`,
                color: 'text-red-600',
                icon: <TrendingUp className="h-4 w-4 rotate-180 text-red-600" />,
            };
        } else {
            return {
                text: 'Sem alteração',
                color: 'text-gray-600',
                icon: <Activity className="h-4 w-4 text-gray-600" />,
            };
        }
    };

    const painImprovement = getPainImprovement();

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Relatório da Sessão - ${formatDateTime(agendamento.data_hora)}`} />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                <div className="mb-8">
                    <div className="mb-4">
                        <Link href={route('paciente.agendamentos.index')}>
                            <Button variant="ghost" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar aos Agendamentos
                            </Button>
                        </Link>
                    </div>
                    <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">Relatório da Sessão</h1>
                    <p className="text-muted-foreground">Detalhes da sua sessão de fisioterapia</p>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Informações da Sessão */}
                    <Card className="lg:col-span-1">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Informações da Sessão
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-3">
                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                                    <User className="h-5 w-5 text-blue-600" />
                                </div>
                                <div>
                                    <p className="font-medium">{agendamento.fisioterapeuta.user.name}</p>
                                    {agendamento.fisioterapeuta.especialidade && (
                                        <p className="text-sm text-muted-foreground">{agendamento.fisioterapeuta.especialidade}</p>
                                    )}
                                </div>
                            </div>

                            <Separator />

                            <div className="space-y-3">
                                <div className="flex items-center gap-2 text-sm">
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                    <span>{formatDateTime(agendamento.data_hora)}</span>
                                </div>
                                <div className="flex items-center gap-2 text-sm">
                                    <Clock className="h-4 w-4 text-muted-foreground" />
                                    <span>{agendamento.duracao} minutos</span>
                                </div>
                                <div className="flex items-center gap-2 text-sm">
                                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                                        {agendamento.status === 'concluido' ? 'Concluída' : agendamento.status}
                                    </Badge>
                                </div>
                            </div>

                            <Separator />

                            {/* Métricas da Sessão */}
                            <div className="space-y-3">
                                <h4 className="text-sm font-medium">Avaliação da Dor</h4>
                                <div className="grid grid-cols-2 gap-3">
                                    <div className="text-center">
                                        <div
                                            className={`inline-flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${getPainLevelColor(relatorio.pain_level_before)}`}
                                        >
                                            {relatorio.pain_level_before}
                                        </div>
                                        <p className="mt-1 text-xs text-muted-foreground">Inicial</p>
                                    </div>
                                    <div className="text-center">
                                        <div
                                            className={`inline-flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${getPainLevelColor(relatorio.pain_level_after)}`}
                                        >
                                            {relatorio.pain_level_after}
                                        </div>
                                        <p className="mt-1 text-xs text-muted-foreground">Final</p>
                                    </div>
                                </div>
                                <div className="flex items-center justify-center gap-2">
                                    {painImprovement.icon}
                                    <span className={`text-sm ${painImprovement.color}`}>{painImprovement.text}</span>
                                </div>
                            </div>

                            <Separator />

                            {/* Satisfação */}
                            <div className="text-center">
                                <h4 className="mb-2 text-sm font-medium">Sua Avaliação</h4>
                                <div className="flex items-center justify-center gap-1">{getSatisfactionStars(relatorio.patient_satisfaction)}</div>
                                <p className="mt-1 text-xs text-muted-foreground">{relatorio.patient_satisfaction}/5 estrelas</p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Conteúdo do Relatório */}
                    <div className="space-y-6 lg:col-span-2">
                        {/* Observações */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <FileText className="h-5 w-5" />
                                    Observações da Sessão
                                </CardTitle>
                                <CardDescription>Anotações do fisioterapeuta sobre a sessão</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm leading-relaxed whitespace-pre-wrap">
                                    {relatorio.observations || 'Nenhuma observação registrada.'}
                                </p>
                            </CardContent>
                        </Card>

                        {/* Exercícios Realizados */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Activity className="h-5 w-5" />
                                    Exercícios Realizados
                                </CardTitle>
                                <CardDescription>Atividades e exercícios executados durante a sessão</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm leading-relaxed whitespace-pre-wrap">
                                    {relatorio.exercises || 'Nenhum exercício específico registrado.'}
                                </p>
                            </CardContent>
                        </Card>

                        {/* Evolução */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <TrendingUp className="h-5 w-5" />
                                    Evolução do Tratamento
                                </CardTitle>
                                <CardDescription>Progresso observado e notas sobre sua evolução</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm leading-relaxed whitespace-pre-wrap">
                                    {relatorio.progress_notes || 'Nenhuma nota de evolução registrada.'}
                                </p>
                            </CardContent>
                        </Card>

                        {/* Avaliação de Mobilidade */}
                        {relatorio.mobility_assessment && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Activity className="h-5 w-5" />
                                        Avaliação de Mobilidade
                                    </CardTitle>
                                    <CardDescription>Análise da sua mobilidade e amplitude de movimento</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-sm leading-relaxed whitespace-pre-wrap">{relatorio.mobility_assessment}</p>
                                </CardContent>
                            </Card>
                        )}

                        {/* Próximos Passos */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Target className="h-5 w-5" />
                                    Próximos Passos
                                </CardTitle>
                                <CardDescription>Recomendações e plano para as próximas sessões</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm leading-relaxed whitespace-pre-wrap">
                                    {relatorio.next_steps || 'Nenhuma recomendação específica registrada.'}
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Avaliação */}
                {avaliacao ? (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Star className="h-5 w-5" />
                                Sua Avaliação
                            </CardTitle>
                            <CardDescription>Avaliação enviada em {new Date(avaliacao.created_at).toLocaleDateString('pt-BR')}</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">Avaliação:</span>
                                <div className="flex items-center gap-1">
                                    {Array.from({ length: 5 }, (_, i) => (
                                        <Star
                                            key={i}
                                            className={`h-4 w-4 ${i < avaliacao.rating ? 'fill-current text-yellow-400' : 'text-gray-300'}`}
                                        />
                                    ))}
                                    <span className="ml-2 text-sm text-muted-foreground">{avaliacao.rating}/5 estrelas</span>
                                </div>
                            </div>

                            <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">Recomenda:</span>
                                <span className={`text-sm ${avaliacao.recommend ? 'text-green-600' : 'text-red-600'}`}>
                                    {avaliacao.recommend ? 'Sim' : 'Não'}
                                </span>
                            </div>

                            {avaliacao.comment && (
                                <div>
                                    <span className="text-sm font-medium">Comentário:</span>
                                    <p className="mt-1 text-sm whitespace-pre-wrap text-muted-foreground">{avaliacao.comment}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                ) : (
                    <Card className="border-dashed">
                        <CardContent className="flex flex-col items-center justify-center py-8">
                            <Star className="mb-4 h-12 w-12 text-muted-foreground" />
                            <h3 className="mb-2 text-lg font-medium">Avalie esta sessão</h3>
                            <p className="mb-4 text-center text-muted-foreground">Sua opinião é muito importante para nós e ajuda outros pacientes</p>
                            <Link href={route('paciente.agendamentos.evaluate', agendamento.id)}>
                                <Button>
                                    <Star className="mr-2 h-4 w-4" />
                                    Avaliar Sessão
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>
                )}

                {/* Ações */}
                <div className="flex justify-center gap-4">
                    <Button variant="outline" onClick={() => window.print()}>
                        <FileText className="mr-2 h-4 w-4" />
                        Imprimir Relatório
                    </Button>
                    <Link href={route('paciente.fisioterapeutas.index')}>
                        <Button>
                            <Calendar className="mr-2 h-4 w-4" />
                            Buscar Fisioterapeutas
                        </Button>
                    </Link>
                </div>
            </div>
        </AppLayout>
    );
}
