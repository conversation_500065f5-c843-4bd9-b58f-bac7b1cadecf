<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Agendamento;
use App\Models\Pagamento;
use App\Services\UnifiedPaymentService;
use App\Services\MercadoPagoService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Carbon\Carbon;

class AgendamentoPagamentoController extends Controller
{
    protected $paymentService;
    protected $mercadoPagoService;

    public function __construct(UnifiedPaymentService $paymentService, MercadoPagoService $mercadoPagoService)
    {
        $this->paymentService = $paymentService;
        $this->mercadoPagoService = $mercadoPagoService;
    }

    /**
     * Verifica o status de pagamento de um agendamento
     */
    public function verificarPagamento(Request $request, $agendamentoId)
    {
        try {
            $agendamento = Agendamento::with('pagamento')->findOrFail($agendamentoId);

            // Verificar se o agendamento pertence ao usuário logado
            $user = auth()->user();
            if ($user && $user->role === 'paciente' && $agendamento->paciente_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Você não tem permissão para verificar este agendamento.'
                ], 403);
            }

            // Realizar a verificação de pagamento
            $pagamentoConfirmado = $agendamento->verificarEConfirmarPagamento();

            if ($pagamentoConfirmado) {
                return response()->json([
                    'success' => true,
                    'message' => 'Pagamento confirmado! Agendamento atualizado para status "agendado".',
                    'agendamento' => [
                        'id' => $agendamento->id,
                        'status' => $agendamento->status,
                        'payment_status' => $agendamento->pagamento->status,
                        'payment_method' => $agendamento->pagamento->method,
                        'paid_at' => $agendamento->pagamento->paid_at?->format('d/m/Y H:i:s'),
                    ]
                ]);
            }

            // Se o pagamento ainda não foi confirmado, retornar informações para o modal
            $paymentLink = $agendamento->getPaymentLink();

            return response()->json([
                'success' => false,
                'message' => 'Pagamento ainda não confirmado.',
                'show_modal' => true,
                'modal_data' => [
                    'title' => 'Pagamento Pendente',
                    'message' => 'O pagamento para este agendamento ainda não foi confirmado. Por favor, realize o pagamento para confirmar sua sessão.',
                    'payment_link' => $paymentLink,
                    'payment_status' => $agendamento->pagamento->status,
                    'payment_method' => $agendamento->pagamento->method,
                    'amount' => 'R$ ' . number_format($agendamento->pagamento->amount, 2, ',', '.'),
                    'created_at' => $agendamento->pagamento->created_at->format('d/m/Y H:i:s'),
                ],
                'agendamento' => [
                    'id' => $agendamento->id,
                    'status' => $agendamento->status,
                    'payment_status' => $agendamento->pagamento->status,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao verificar pagamento do agendamento: ' . $e->getMessage(), [
                'agendamento_id' => $agendamentoId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro ao verificar pagamento. Tente novamente mais tarde.'
            ], 500);
        }
    }

    /**
     * Webhook para receber notificações de pagamento do Mercado Pago
     */
    public function webhookMercadoPago(Request $request)
    {
        try {
            $data = $request->all();
            
            Log::info('🔔 [WEBHOOK] Recebida notificação do Mercado Pago', [
                'data' => $data,
                'headers' => $request->headers->all()
            ]);

            // Verificar se é uma notificação de pagamento
            if (isset($data['type']) && $data['type'] === 'payment') {
                $paymentId = $data['data']['id'] ?? null;
                
                if ($paymentId) {
                    // Processar o webhook usando o UnifiedPaymentService
                    $result = $this->paymentService->processWebhook($paymentId);
                    
                    if ($result) {
                        // Buscar o pagamento para verificar se está associado a um agendamento
                        $pagamento = Pagamento::where('payment_id', $paymentId)
                            ->orWhere('id', $data['external_reference'] ?? null)
                            ->first();
                        
                        if ($pagamento && $pagamento->agendamento_id) {
                            $agendamento = Agendamento::find($pagamento->agendamento_id);
                            
                            if ($agendamento && $pagamento->status === 'pago' && $agendamento->status === 'pendente') {
                                // Atualizar status do agendamento
                                $agendamento->update(['status' => 'agendado']);
                                
                                // Disparar evento de confirmação
                                event(new \App\Events\AgendamentoConfirmado($agendamento));
                                
                                Log::info('✅ [WEBHOOK] Agendamento confirmado via webhook', [
                                    'agendamento_id' => $agendamento->id,
                                    'pagamento_id' => $pagamento->id,
                                    'payment_id' => $paymentId
                                ]);
                            }
                        }
                        
                        return response()->json(['success' => true]);
                    }
                }
            }

            return response()->json(['success' => false, 'message' => 'Tipo de notificação não suportado'], 400);

        } catch (\Exception $e) {
            Log::error('💥 [WEBHOOK] Erro ao processar webhook do Mercado Pago', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json(['success' => false, 'message' => 'Erro interno'], 500);
        }
    }

    /**
     * Reenvia link de pagamento para um agendamento
     */
    public function reenviarLinkPagamento(Request $request, $agendamentoId)
    {
        try {
            $agendamento = Agendamento::with('pagamento')->findOrFail($agendamentoId);

            // Verificar se o agendamento pertence ao usuário logado
            $user = auth()->user();
            if ($user && $user->role === 'paciente' && $agendamento->paciente_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Você não tem permissão para acessar este agendamento.'
                ], 403);
            }

            // Verificar se o agendamento tem pagamento associado
            if (!$agendamento->hasPayment()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Este agendamento não possui pagamento associado.'
                ], 404);
            }

            // Obter o link de pagamento
            $paymentLink = $agendamento->getPaymentLink();

            if (!$paymentLink) {
                return response()->json([
                    'success' => false,
                    'message' => 'Não foi possível gerar o link de pagamento.'
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Link de pagamento gerado com sucesso.',
                'payment_link' => $paymentLink,
                'agendamento' => [
                    'id' => $agendamento->id,
                    'status' => $agendamento->status,
                    'payment_status' => $agendamento->pagamento->status,
                    'amount' => 'R$ ' . number_format($agendamento->pagamento->amount, 2, ',', '.'),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao reenviar link de pagamento: ' . $e->getMessage(), [
                'agendamento_id' => $agendamentoId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro ao gerar link de pagamento. Tente novamente mais tarde.'
            ], 500);
        }
    }

    /**
     * Lista agendamentos pendentes de pagamento do usuário
     */
    public function listarPendentes(Request $request)
    {
        try {
            $user = auth()->user();
            $userId = $user ? $user->id : null;
            
            if (!$userId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Usuário não autenticado.'
                ], 401);
            }
            
            $agendamentos = Agendamento::with(['pagamento'])
                ->where('paciente_id', $userId)
                ->where('status', 'pendente')
                ->whereHas('pagamento', function($query) {
                    $query->where('status', 'pendente');
                })
                ->orderBy('scheduled_at', 'asc')
                ->get()
                ->map(function ($agendamento) {
                    $fisioterapeutaNome = 'N/A';

                    try {
                        if ($agendamento->fisioterapeuta_id) {
                            $fisioterapeutaModel = \App\Models\Fisioterapeuta::find($agendamento->fisioterapeuta_id);
                            if ($fisioterapeutaModel) {
                                $fisioterapeutaNome = $fisioterapeutaModel->user->name ?? 'Fisioterapeuta';
                            }
                        }
                    } catch (\Exception $e) {
                        Log::warning('⚠️ [LISTAR PENDENTES] Erro ao carregar fisioterapeuta', [
                            'agendamento_id' => $agendamento->id,
                            'erro' => $e->getMessage()
                        ]);
                    }

                    return [
                        'id' => $agendamento->id,
                        'scheduled_at' => $agendamento->scheduled_at->format('d/m/Y H:i'),
                        'fisioterapeuta' => $fisioterapeutaNome,
                        'service_type' => $agendamento->service_type,
                        'price' => 'R$ ' . number_format($agendamento->price, 2, ',', '.'),
                        'payment_status' => $agendamento->pagamento->status,
                        'payment_method' => $agendamento->pagamento->method,
                        'created_at' => $agendamento->created_at->format('d/m/Y H:i:s'),
                    ];
                });

            return response()->json([
                'success' => true,
                'agendamentos' => $agendamentos
            ]);

        } catch (\Exception $e) {
            $user = auth()->user();
            Log::error('Erro ao listar agendamentos pendentes: ' . $e->getMessage(), [
                'user_id' => $user ? $user->id : null,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro ao carregar agendamentos pendentes.'
            ], 500);
        }
    }


    /**
     * Método removido - validação otimizada foi movida para o método success
     */
    public function validarPagamentoUrlRemovido(Request $request)
    {
        try {
            $paymentId = $request->get('payment_id');
            $externalReference = $request->get('external_reference');
            $collectionStatus = $request->get('collection_status');
            $collectionId = $request->get('collection_id');
            $merchantOrderId = $request->get('merchant_order_id');
            $paymentType = $request->get('payment_type');
            $preferenceId = $request->get('preference_id');

            if (!$paymentId && !$externalReference) {
                return response()->json([
                    'success' => false,
                    'error' => 'Parâmetros inválidos',
                    'message' => 'Pagamento não encontrado - parâmetros insuficientes'
                ], 400);
            }

            Log::info('🔍 [VALIDAR PAGAMENTO] Iniciando validação via URL params', [
                'payment_id' => $paymentId,
                'external_reference' => $externalReference,
                'collection_status' => $collectionStatus,
                'collection_id' => $collectionId,
                'merchant_order_id' => $merchantOrderId,
                'status' => $request->get('status')
            ]);

            // 1. Primeiro, verificar se o pagamento já foi confirmado no banco
            $pagamento = null;
            $agendamento = null;

            // Buscar por external_reference primeiro
            if ($externalReference) {
                $pagamento = Pagamento::find($externalReference);
            }

            // Se não encontrou, buscar por payment_id
            if (!$pagamento && $paymentId) {
                $pagamento = Pagamento::where('payment_id', $paymentId)->first();
            }

            // Verificar se encontrou o pagamento
            if ($pagamento) {
                if ($pagamento->agendamento_id) {
                    $agendamento = Agendamento::find($pagamento->agendamento_id);
                }

                // Se o pagamento já foi confirmado no banco, não precisa verificar no MP
                if ($pagamento->status === 'pago' || ($collectionStatus === 'approved' && $pagamento->status === 'pendente')) {
                    Log::info('✅ [VALIDAR PAGAMENTO] Pagamento já confirmado no banco', [
                        'pagamento_id' => $pagamento->id,
                        'status' => $pagamento->status,
                        'external_reference' => $externalReference
                    ]);

                    return response()->json([
                        'success' => true,
                        'message' => 'Pagamento confirmado',
                        'data' => [
                            'pagamento_confirmado' => true,
                            'agendamento_id' => $agendamento?->id,
                            'pagamento_status' => $pagamento->status,
                        ]
                    ]);
                }
            }

            // 2. Se não encontrou no banco ou não está confirmado, verificar no Mercado Pago
            $paymentInfoMp = null;
            $verificadoMercadoPago = false;

            try {
                if ($paymentId) {
                    // Verificar via payment_id
                    $paymentInfoMp = $this->mercadoPagoService->getPayment($paymentId);
                    if ($paymentInfoMp) {
                        $verificadoMercadoPago = true;
                        Log::info('✅ [VALIDAR PAGAMENTO] Pagamento encontrado no Mercado Pago via payment_id', [
                            'payment_id' => $paymentId,
                            'mp_status' => $paymentInfoMp['status'] ?? null
                        ]);
                    }
                }

                // Se não conseguiu via payment_id, tentar via external_reference
                if (!$verificadoMercadoPago && $externalReference) {
                    $paymentInfoMp = $this->consultarPagamentoMercadoPago($externalReference);
                    if ($paymentInfoMp) {
                        $verificadoMercadoPago = true;
                        Log::info('✅ [VALIDAR PAGAMENTO] Pagamento encontrado no Mercado Pago via external_reference', [
                            'external_reference' => $externalReference,
                            'mp_status' => $paymentInfoMp['status'] ?? null
                        ]);
                    }
                }

            } catch (\Exception $e) {
                Log::warning('⚠️ [VALIDAR PAGAMENTO] Erro ao consultar Mercado Pago', [
                    'error' => $e->getMessage(),
                    'payment_id' => $paymentId,
                    'external_reference' => $externalReference
                ]);
            }

            // 3. Se encontrou no Mercado Pago, verificar se está aprovado
            if ($verificadoMercadoPago && $paymentInfoMp) {
                $mpStatus = $paymentInfoMp['status'] ?? null;
                $mpPaymentId = $paymentInfoMp['id'] ?? $paymentId;

                if ($mpStatus === 'approved') {
                    // Pagamento aprovado no MP, atualizar banco se necessário
                    $pagamentoAtualizado = $this->atualizarPagamentoBanco($pagamento, $paymentInfoMp, $agendamento);

                    if ($pagamentoAtualizado) {
                        return response()->json([
                            'success' => true,
                            'message' => 'Pagamento validado e banco atualizado',
                            'data' => [
                                'pagamento_confirmado' => true,
                                'agendamento_id' => $pagamentoAtualizado->agendamento_id,
                                'pagamento_status' => $pagamentoAtualizado->status,
                            ]
                        ]);
                    }
                } elseif (in_array($mpStatus, ['pending', 'in_process'])) {
                    return response()->json([
                        'success' => false,
                        'error' => 'pagamento_pendente',
                        'message' => 'Pagamento ainda em processamento'
                    ]);
                } elseif (in_array($mpStatus, ['rejected', 'cancelled'])) {
                    return response()->json([
                        'success' => false,
                        'error' => 'pagamento_rejeitado',
                        'message' => 'Pagamento foi rejeitado ou cancelado'
                    ]);
                }
            }

            // 4. Se chegou aqui, não conseguiu validar o pagamento
            return response()->json([
                'success' => false,
                'error' => 'pagamento_nao_encontrado',
                'message' => 'Não foi possível validar o pagamento'
            ]);

        } catch (\Exception $e) {
            Log::error('💥 [VALIDAR PAGAMENTO] Erro na validação', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'params' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'erro_interno',
                'message' => 'Erro interno ao validar pagamento'
            ], 500);
        }
    }

    /**
     * Consultar pagamento no Mercado Pago via external_reference
     */
    private function consultarPagamentoMercadoPago($externalReference)
    {
        try {
            $baseUrl = config('mercadopago.base_url');
            $accessToken = $this->mercadoPagoService->getAccessToken();

            $httpClient = \Illuminate\Support\Facades\Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ]);

            // Desabilitar SSL em desenvolvimento
            $disableSslVerify = app()->environment('local', 'development') || env('MERCADOPAGO_DISABLE_SSL_VERIFY', false);
            if ($disableSslVerify) {
                $httpClient = $httpClient->withOptions(['verify' => false]);
            }

            // Buscar pagamento pelo external_reference
            $response = $httpClient->get($baseUrl . '/v1/payments/search', [
                'external_reference' => $externalReference,
                'limit' => 10
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $pagamentos = $data['results'] ?? [];

                if (!empty($pagamentos)) {
                    // Pegar o pagamento mais recente com status aprovado
                    $pagamentoAprovado = null;
                    foreach ($pagamentos as $p) {
                        if (($p['status'] ?? null) === 'approved') {
                            $pagamentoAprovado = $p;
                            break;
                        }
                    }

                    // Se não encontrou aprovado, pegar o mais recente
                    if (!$pagamentoAprovado) {
                        usort($pagamentos, function($a, $b) {
                            $dateA = strtotime($a['date_created'] ?? 0);
                            $dateB = strtotime($b['date_created'] ?? 0);
                            return $dateB <=> $dateA;
                        });
                        $pagamentoAprovado = $pagamentos[0];
                    }

                    return $pagamentoAprovado;
                }
            }

            return null;

        } catch (\Exception $e) {
            Log::error('💥 [VALIDAR PAGAMENTO] Erro ao consultar MP via external_reference', [
                'external_reference' => $externalReference,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Atualizar pagamento no banco baseado nas informações do MP
     */
    private function atualizarPagamentoBanco($pagamentoExistente, $paymentInfoMp, $agendamento)
    {
        try {
            if (!$pagamentoExistente) {
                // Criar novo pagamento se não existe
                $pagamentoExistente = Pagamento::create([
                    'id' => $paymentInfoMp['external_reference'] ?? null,
                    'agendamento_id' => null, // Será atualizado se encontrar agendamento
                    'amount' => (float) ($paymentInfoMp['transaction_amount'] ?? 0),
                    'status' => 'pago',
                    'method' => $this->mapPaymentMethod(
                        $paymentInfoMp['payment_method_id'] ?? '',
                        $paymentInfoMp['payment_type_id'] ?? ''
                    ),
                    'payment_id' => $paymentInfoMp['id'],
                    'preference_id' => $paymentInfoMp['preference_id'] ?? null,
                    'paid_at' => Carbon::now(),
                    'gateway_response' => $paymentInfoMp,
                ]);

                Log::info('✅ [VALIDAR PAGAMENTO] Novo pagamento criado', [
                    'pagamento_id' => $pagamentoExistente->id,
                    'mp_payment_id' => $paymentInfoMp['id']
                ]);

            } else {
                // Atualizar pagamento existente
                $pagamentoExistente->update([
                    'status' => 'pago',
                    'payment_id' => $paymentInfoMp['id'],
                    'paid_at' => Carbon::now(),
                    'gateway_response' => $paymentInfoMp,
                ]);

                Log::info('✅ [VALIDAR PAGAMENTO] Pagamento existente atualizado', [
                    'pagamento_id' => $pagamentoExistente->id,
                    'old_status' => $pagamentoExistente->status
                ]);
            }

            // Atualizar agendamento se existir
            if ($agendamento && $pagamentoExistente) {
                $agendamento->update(['status' => 'agendado']);

                // Disparar evento de confirmação
                event(new \App\Events\AgendamentoConfirmado($agendamento));

                Log::info('✅ [VALIDAR PAGAMENTO] Agendamento confirmado', [
                    'agendamento_id' => $agendamento->id,
                    'status' => $agendamento->status
                ]);
            }

            return $pagamentoExistente;

        } catch (\Exception $e) {
            Log::error('💥 [VALIDAR PAGAMENTO] Erro ao atualizar banco', [
                'error' => $e->getMessage(),
                'pagamento_existente' => $pagamentoExistente?->id
            ]);
            return null;
        }
    }

    /**
     * Mapear método de pagamento do MP para formato interno
     */
    private function mapPaymentMethod($paymentMethodId, $paymentTypeId)
    {
        // PIX
        if ($paymentMethodId === 'pix' || $paymentTypeId === 'bank_transfer') {
            return 'pix';
        }

        // Boleto
        if ($paymentTypeId === 'ticket') {
            return 'boleto';
        }

        // Cartões de débito
        if ($paymentTypeId === 'debit_card') {
            return 'cartao_debito';
        }

        // Mapear bandeiras de cartão de crédito para o formato interno
        $creditCardMethods = ['visa', 'master', 'amex', 'elo', 'hipercard'];
        if (in_array($paymentMethodId, $creditCardMethods) || $paymentTypeId === 'credit_card') {
            return 'cartao_credito';
        }

        // Cartões de crédito (padrão)
        return 'cartao_credito';
    }

    /**
     * Página de sucesso para pagamento de agendamento
     */
    public function success(Request $request)
    {
        try {
            $paymentId = $request->get('payment_id');
            $externalReference = $request->get('external_reference');
            $collectionStatus = $request->get('collection_status', 'approved');
            $paymentType = $request->get('payment_type');

            Log::info('🎯 [AGENDAMENTO SUCCESS] Página de sucesso acessada', [
                'payment_id' => $paymentId,
                'external_reference' => $externalReference,
                'collection_status' => $collectionStatus,
                'payment_type' => $paymentType,
                'all_params' => $request->all()
            ]);

            // Buscar pagamento pelo external_reference ou payment_id
            $pagamento = null;
            $agendamento = null;

            if ($externalReference) {
                $pagamento = Pagamento::find($externalReference);
            }

            if (!$pagamento && $paymentId) {
                $pagamento = Pagamento::where('payment_id', $paymentId)->first();
            }

            if ($pagamento) {
                $agendamento = Agendamento::find($pagamento->agendamento_id);
            }

            // Determinar se é pagamento de teste ou agendamento
            $isTestEnvironment = app()->environment(['local', 'development']);

            // Verificar se é pagamento de teste baseado no usuário e características do pagamento
            $isTestPayment = false;
            if ($pagamento && $isTestEnvironment) {
                $isTestPayment = !$pagamento->assinatura_id &&
                                !$pagamento->agendamento_id &&
                                str_contains(strtolower($pagamento->notes ?? ''), 'pagamento de teste');
            }

            // Se não encontrou pagamento, verificar se é ambiente de teste
            if (!$pagamento && $isTestEnvironment) {
                // Em ambiente de teste, criar um pagamento de teste
                $pagamento = Pagamento::create([
                    'agendamento_id' => null,
                    'amount' => 100.00, // Valor de teste
                    'status' => 'pago',
                    'method' => 'account_money',
                    'payment_id' => $paymentId,
                    'paid_at' => now(),
                ]);

                Log::info('🧪 [AGENDAMENTO SUCCESS] Pagamento de teste criado', [
                    'pagamento_id' => $pagamento->id,
                    'payment_id' => $paymentId
                ]);
            }

            // ✅ VERIFICAÇÃO MANUAL DE PAGAMENTOS ANTES DE RENDERIZAR
            $statusCorrigido = false;
            if ($collectionStatus === 'approved' && $paymentId) {
                try {
                    // Buscar pagamento no banco
                    $pagamentoLocal = Pagamento::find($externalReference) ?? Pagamento::where('payment_id', $paymentId)->first();

                    // Se existe agendamento associado, verificar se pode ser atualizado
                    if ($pagamentoLocal && $pagamentoLocal->agendamento_id) {
                        $agendamentoParaVerificar = Agendamento::find($pagamentoLocal->agendamento_id);

                        if ($agendamentoParaVerificar && $agendamentoParaVerificar->status === 'pendente') {
                            // Verificar status real do pagamento no MP se necessário
                            if ($pagamentoLocal->status !== 'pago') {
                                Log::info('🔍 [CHECKOUT SUCCESS] Verificando pagamento pendente no Mercado Pago', [
                                    'payment_id' => $paymentId,
                                    'agendamento_id' => $agendamentoParaVerificar->id,
                                    'external_reference' => $externalReference
                                ]);

                                // Processar webhook para atualizar status
                                $this->paymentService->processWebhook($paymentId);

                                // Recarregar dados após atualização
                                $pagamentoLocal = Pagamento::find($pagamentoLocal->id);

                                Log::info('✅ [CHECKOUT SUCCESS] Pagamento verificado no MP', [
                                    'pagamento_id' => $pagamentoLocal->id,
                                    'status_antigo' => $agendamentoParaVerificar->status,
                                    'pagamento_status' => $pagamentoLocal->status ?? 'unknown'
                                ]);
                            }

                            // Se pagamento foi confirmado, atualizar agendamento
                            if ($pagamentoLocal && $pagamentoLocal->status === 'pago') {
                                $oldStatus = $agendamentoParaVerificar->status;
                                $agendamentoParaVerificar->update(['status' => 'agendado']);

                                // Recarregar agendamento para refletir mudanças
                                $agendamentoParaVerificar->refresh();

                                // Atualizar a variável $agendamento usada na view
                                if ($agendamento && $agendamento->id === $agendamentoParaVerificar->id) {
                                    $agendamento = $agendamentoParaVerificar;
                                }

                                Log::info('✅ [CHECKOUT SUCCESS] Agendamento confirmado via verificação manual', [
                                    'agendamento_id' => $agendamentoParaVerificar->id,
                                    'status_antigo' => $oldStatus,
                                    'status_novo' => $agendamentoParaVerificar->status,
                                    'pagamento_id' => $pagamentoLocal->id
                                ]);

                                // Marcar que houve correção manual
                                $statusCorrigido = true;
                            }
                        }
                    }

                    // Verificar permissão do usuário de forma robusta
                    $usuarioAutenticado = Auth::user();

                    // VERIFICAÇÃO PRINCIPAL: Usuário logado pode acessar os próprios pagamentos
                    $devePermitirAcesso = false;

                    if ($usuarioAutenticado) {
                        // Usuário está autenticado
                        Log::info('👤 [SUCCESS PAGE] Usuário autenticado verificado', [
                            'user_id' => $usuarioAutenticado->id,
                            'user_email' => $usuarioAutenticado->email,
                            'user_role' => $usuarioAutenticado->role,
                            'pagamento_encontrado' => $pagamentoLocal ? 'sim' : 'não',
                            'agendamento_id_pagamento' => $pagamentoLocal->agendamento_id ?? null,
                            'collection_status' => $collectionStatus
                        ]);

                        // Permissões especiais (admin/fisioterapeuta podem acessar tudo)
                        if (in_array($usuarioAutenticado->role, ['admin', 'fisioterapeuta'])) {
                            $devePermitirAcesso = true;
                            Log::info('🚫 [SUCCESS PAGE] Acesso concedido por role especial', [
                                'user_role' => $usuarioAutenticado->role
                            ]);
                        }

                        // Verificação para usuários pacientes - verificar se é dono do agendamento
                        elseif ($usuarioAutenticado->role === 'paciente') {
                            if ($pagamentoLocal && $pagamentoLocal->agendamento_id) {
                                $agendamentoParaVerificacao = Agendamento::find($pagamentoLocal->agendamento_id);

                                if ($agendamentoParaVerificacao) {
                                    $permissaoDono = ($agendamentoParaVerificacao->paciente_id === $usuarioAutenticado->id);
                                    $permissaoPagamento = ($pagamentoLocal->user_id === $usuarioAutenticado->id); // Campo user_id no pagamento se existir

                                    Log::info('🔒 [SUCCESS PAGE] Verificação de propriedade paciente', [
                                        'agendamento_paciente_id' => $agendamentoParaVerificacao->paciente_id,
                                        'usuario_id_atual' => $usuarioAutenticado->id,
                                        'permissao_dono_agendamento' => $permissaoDono,
                                        'pagamento_user_id' => $pagamentoLocal->user_id ?? 'N/A',
                                        'permissao_pagamento' => $permissaoPagamento
                                    ]);

                                    if ($permissaoDono || $permissaoPagamento) {
                                        $devePermitirAcesso = true;
                                    }
                                } else {
                                    Log::warning('🚫 [SUCCESS PAGE] Agendamento não encontrado para permissão', [
                                        'agendamento_id_procurado' => $pagamentoLocal->agendamento_id,
                                        'pagamento_id' => $pagamentoLocal->id
                                    ]);
                                }
                            }
                        }

                        // Verificação para fisioterapeutas - verificar se é o responsável do agendamento
                        elseif ($usuarioAutenticado->role === 'fisioterapeuta') {
                            if ($pagamentoLocal && $pagamentoLocal->agendamento_id) {
                                $agendamentoParaVerificacao = Agendamento::find($pagamentoLocal->agendamento_id);

                                if ($agendamentoParaVerificacao && $agendamentoParaVerificacao->fisioterapeuta_id === $usuarioAutenticado->id) {
                                    $devePermitirAcesso = true;
                                    Log::info('👨‍⚕️ [SUCCESS PAGE] Acesso concedido ao fisioterapeuta responsável', [
                                        'fisioterapeuta_id_agendamento' => $agendamentoParaVerificacao->fisioterapeuta_id,
                                        'usuario_id_atual' => $usuarioAutenticado->id
                                    ]);
                                }
                            }
                        }

                        // Bloquear acesso se não tem permissão
                        if (!$devePermitirAcesso) {
                            return Inertia::render('paciente/checkout-success', [
                                'user' => $usuarioAutenticado,
                                'flash' => [
                                    'payment_type' => 'agendamento',
                                    'is_test_environment' => $isTestEnvironment,
                                    'error' => 'acesso_negado',
                                    'error_message' => 'Esta página contém informações sensíveis e só pode ser acessada pelo proprietário do pagamento.',
                                ]
                            ]);
                        }

                    } else {
                        // Usuário não autenticado
                        if ($collectionStatus === 'approved' && !$isTestPayment) {
                            // REDIRECIONAR PARA LOGIN se tem parâmetros válidos
                            Log::info('🔐 [SUCCESS PAGE] Usuário não autenticado - redirecionando para login', [
                                'payment_id' => $paymentId,
                                'url_atual' => request()->fullUrl()
                            ]);

                            return redirect()->route('login', [
                                'redirect' => request()->fullUrl()
                            ]);
                        } else {
                            // Mostrar erro
                            return Inertia::render('paciente/checkout-success', [
                                'user' => null,
                                'flash' => [
                                    'payment_type' => 'agendamento',
                                    'is_test_environment' => $isTestEnvironment,
                                    'error' => 'acesso_negado',
                                    'error_message' => 'Você precisa estar logado para acessar esta página.',
                                ]
                            ]);
                        }
                    }

                } catch (\Exception $e) {
                    Log::warning('⚠️ [SUCCESS PAGE] Erro na validação automática', [
                        'error' => $e->getMessage(),
                        'payment_id' => $paymentId
                    ]);
                }
            }

            // Preparar dados para a view
            $flashData = [
                'payment_type' => $isTestPayment ? 'teste' : 'agendamento',
                'is_test_environment' => $isTestEnvironment,
                'agendamento_id' => $agendamento?->id,
                'status_corrigido' => $statusCorrigido,
                'pagamento_status' => $pagamentoLocal?->status ?? 'unknown',
                'pagamento_method' => $pagamentoLocal?->method ?? 'unknown',
            ];

            // Adicionar dados do agendamento se existir
            if ($agendamento) {
                // Carregar informações do fisioterapeuta de forma segura
                $fisioterapeuta = null;
                $estabelecimento = null;

                try {
                    // Tentar carregar informações do fisioterapeuta
                    if ($agendamento->fisioterapeuta_id) {
                        $fisioterapeutaModel = \App\Models\Fisioterapeuta::find($agendamento->fisioterapeuta_id);
                        if ($fisioterapeutaModel) {
                            $fisioterapeuta = $fisioterapeutaModel->user->name ?? 'Fisioterapeuta';
                            if ($fisioterapeutaModel->estabelecimento) {
                                $estabelecimento = $fisioterapeutaModel->estabelecimento->name ?? 'Estabelecimento';
                            }
                        }
                    }
                } catch (\Exception $e) {
                    Log::warning('⚠️ [SUCCESS PAGE] Erro ao carregar fisioterapeuta', [
                        'agendamento_id' => $agendamento->id,
                        'erro' => $e->getMessage()
                    ]);
                }

                $flashData['agendamento'] = [
                    'id' => $agendamento->id,
                    'scheduled_at' => $agendamento->scheduled_at?->toISOString(),
                    'service_type' => $agendamento->service_type,
                    'price' => $agendamento->price,
                    'fisioterapeuta' => $fisioterapeuta ?? 'A ser agendado',
                    'estabelecimento' => $estabelecimento ?? 'A ser definido',
                    'address' => $agendamento->address ?? 'A ser definido',
                    'current_status' => $agendamento->status,
                ];
            }

            // Se for ambiente de teste, adicionar URL de simulação
            if ($isTestEnvironment && $paymentId) {
                $flashData['simulation_url'] = route('teste-pagamento') . '?payment_id=' . $paymentId;
            }

            Log::info('📋 [AGENDAMENTO SUCCESS] Renderizando página de sucesso', [
                'flash_data' => $flashData,
                'is_test_payment' => $isTestPayment,
                'agendamento_found' => $agendamento ? true : false
            ]);

            // Retornar view de sucesso com dados apropriados
            return Inertia::render('paciente/checkout-success', [
                'user' => auth()->user(),
                'flash' => $flashData
            ]);

        } catch (\Exception $e) {
            Log::error('💥 [AGENDAMENTO SUCCESS] Erro na página de sucesso', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_params' => $request->all()
            ]);

            // Em caso de erro, renderizar página de sucesso com mensagem genérica
            return Inertia::render('paciente/checkout-success', [
                'user' => auth()->user(),
                'flash' => [
                    'payment_type' => 'agendamento',
                    'is_test_environment' => app()->environment(['local', 'development']),
                    'agendamento_id' => null,
                ]
            ]);
        }
    }
}
