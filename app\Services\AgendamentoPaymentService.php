<?php

namespace App\Services;

use App\Models\Agendamento;
use App\Models\Pagamento;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AgendamentoPaymentService
{
    protected $unifiedPaymentService;

    public function __construct(UnifiedPaymentService $unifiedPaymentService)
    {
        $this->unifiedPaymentService = $unifiedPaymentService;
    }

    /**
     * Cria um agendamento com status pendente e gera o pagamento
     */
    public function criarAgendamentoPendente(array $agendamentoData, array $pagamentoData)
    {
        try {
            DB::beginTransaction();

            // Criar o agendamento com status pendente
            $agendamento = Agendamento::create([
                'paciente_id' => $agendamentoData['paciente_id'],
                'fisioterapeuta_id' => $agendamentoData['fisioterapeuta_id'],
                'scheduled_at' => $agendamentoData['scheduled_at'],
                'duration' => $agendamentoData['duration'] ?? 60,
                'status' => 'pendente', // Status inicial pendente
                'service_type' => $agendamentoData['service_type'] ?? 'consulta',
                'notes' => $agendamentoData['notes'] ?? null,
                'address' => $agendamentoData['address'] ?? null,
                'price' => $pagamentoData['amount'],
            ]);

            Log::info('✅ [AGENDAMENTO] Agendamento criado com status pendente', [
                'agendamento_id' => $agendamento->id,
                'paciente_id' => $agendamento->paciente_id,
                'fisioterapeuta_id' => $agendamento->fisioterapeuta_id,
                'status' => $agendamento->status,
                'price' => $agendamento->price,
            ]);

            // Criar o registro de pagamento
            $pagamento = Pagamento::create([
                'agendamento_id' => $agendamento->id,
                'user_id' => $agendamentoData['paciente_id'],
                'amount' => $pagamentoData['amount'],
                'status' => 'pendente',
                'method' => null,
                'due_date' => Carbon::now()->addDays(3)->toDateString(), // Vencimento em 3 dias
                'notes' => $pagamentoData['description'] ?? "Pagamento agendamento #{$agendamento->id}",
            ]);

            Log::info('✅ [PAGAMENTO] Registro de pagamento criado', [
                'pagamento_id' => $pagamento->id,
                'agendamento_id' => $agendamento->id,
                'amount' => $pagamento->amount,
                'status' => $pagamento->status,
            ]);

            // Obter informações do paciente para o pagamento
            $paciente = User::find($agendamentoData['paciente_id']);

            // Criar preferência de pagamento no Mercado Pago
            $preference = $this->unifiedPaymentService->createUnifiedPayment([
                'title' => $pagamentoData['title'] ?? "Agendamento #{$agendamento->id}",
                'description' => $pagamentoData['description'] ?? "Pagamento para agendamento #{$agendamento->id}",
                'amount' => (float) $pagamentoData['amount'],
                'payer' => [
                    'name' => $paciente->name,
                    'email' => $paciente->email,
                ],
                'payment_methods' => 'all',
                'success_url' => route('agendamentos.verificar-pagamento', $agendamento->id),
                'failure_url' => route('agendamentos.verificar-pagamento', $agendamento->id),
                'pending_url' => route('agendamentos.verificar-pagamento', $agendamento->id),
                'external_reference' => (string) $pagamento->id,
                'notification_url' => route('webhook.agendamento-pagamento'),
            ]);

            if (!($preference['success'] ?? false)) {
                DB::rollBack();
                Log::error('❌ [PAGAMENTO] Erro ao criar preferência no Mercado Pago', [
                    'agendamento_id' => $agendamento->id,
                    'error' => $preference['message'] ?? 'Erro desconhecido'
                ]);
                
                return [
                    'success' => false,
                    'message' => $preference['message'] ?? 'Não foi possível gerar o pagamento. Tente novamente.'
                ];
            }

            // Atualizar o pagamento com o preference_id
            $pagamento->update([
                'preference_id' => $preference['preference_id'] ?? null,
                'transaction_id' => $preference['preference_id'] ?? null, // Manter para compatibilidade
            ]);

            DB::commit();

            Log::info('✅ [AGENDAMENTO] Agendamento e pagamento criados com sucesso', [
                'agendamento_id' => $agendamento->id,
                'pagamento_id' => $pagamento->id,
                'preference_id' => $preference['preference_id'] ?? null,
                'init_point' => $preference['init_point'] ?? null,
            ]);

            return [
                'success' => true,
                'message' => 'Agendamento criado com sucesso! Aguardando confirmação de pagamento.',
                'agendamento' => $agendamento,
                'pagamento' => $pagamento,
                'payment_data' => $preference,
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('💥 [AGENDAMENTO] Erro ao criar agendamento com pagamento', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'agendamento_data' => $agendamentoData,
                'pagamento_data' => $pagamentoData,
            ]);

            return [
                'success' => false,
                'message' => 'Erro ao criar agendamento. Tente novamente.',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Verifica e confirma pagamentos de agendamentos pendentes
     */
    public function verificarPagamentosPendentes()
    {
        try {
            // Buscar agendamentos pendentes com pagamentos pendentes
            $agendamentosPendentes = Agendamento::with('pagamento')
                ->where('status', 'pendente')
                ->whereHas('pagamento', function($query) {
                    $query->where('status', 'pendente');
                })
                ->get();

            $confirmados = 0;
            $erros = 0;

            foreach ($agendamentosPendentes as $agendamento) {
                try {
                    $resultado = $agendamento->verificarEConfirmarPagamento();
                    
                    if ($resultado) {
                        $confirmados++;
                        Log::info('✅ [VERIFICAÇÃO] Agendamento confirmado automaticamente', [
                            'agendamento_id' => $agendamento->id,
                            'pagamento_id' => $agendamento->pagamento->id,
                        ]);
                    }
                } catch (\Exception $e) {
                    $erros++;
                    Log::error('❌ [VERIFICAÇÃO] Erro ao verificar agendamento', [
                        'agendamento_id' => $agendamento->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            return [
                'success' => true,
                'verificados' => $agendamentosPendentes->count(),
                'confirmados' => $confirmados,
                'erros' => $erros,
                'message' => "Verificação concluída: {$confirmados} agendamentos confirmados de {$agendamentosPendentes->count()} verificados."
            ];

        } catch (\Exception $e) {
            Log::error('💥 [VERIFICAÇÃO] Erro ao verificar pagamentos pendentes', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Erro ao verificar pagamentos pendentes.',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Cancela um agendamento pendente e seu pagamento
     */
    public function cancelarAgendamentoPendente($agendamentoId)
    {
        try {
            DB::beginTransaction();

            $agendamento = Agendamento::with('pagamento')->findOrFail($agendamentoId);

            // Verificar se o agendamento está pendente
            if ($agendamento->status !== 'pendente') {
                return [
                    'success' => false,
                    'message' => 'Apenas agendamentos pendentes podem ser cancelados.'
                ];
            }

            // Se houver pagamento, tentar cancelar no Mercado Pago se necessário
            if ($agendamento->hasPayment() && $agendamento->pagamento->preference_id) {
                // Aqui você pode adicionar lógica para cancelar a preferência no Mercado Pago
                // Por enquanto, apenas atualizamos o status localmente
                $agendamento->pagamento->update([
                    'status' => 'cancelado',
                    'notes' => ($agendamento->pagamento->notes ?? '') . ' - Cancelado pelo usuário',
                ]);
            }

            // Cancelar o agendamento
            $agendamento->update([
                'status' => 'cancelado',
                'cancellation_reason' => 'Cancelado pelo usuário',
            ]);

            DB::commit();

            Log::info('✅ [CANCELAMENTO] Agendamento pendente cancelado', [
                'agendamento_id' => $agendamento->id,
                'pagamento_id' => $agendamento->pagamento->id ?? null,
            ]);

            return [
                'success' => true,
                'message' => 'Agendamento cancelado com sucesso.'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('💥 [CANCELAMENTO] Erro ao cancelar agendamento pendente', [
                'agendamento_id' => $agendamentoId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Erro ao cancelar agendamento. Tente novamente.',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Reenvia link de pagamento para um agendamento
     */
    public function reenviarLinkPagamento($agendamentoId)
    {
        try {
            $agendamento = Agendamento::with('pagamento')->findOrFail($agendamentoId);

            if (!$agendamento->hasPayment()) {
                return [
                    'success' => false,
                    'message' => 'Este agendamento não possui pagamento associado.'
                ];
            }

            if ($agendamento->pagamento->status === 'pago') {
                return [
                    'success' => false,
                    'message' => 'Este agendamento já possui pagamento confirmado.'
                ];
            }

            // Obter o link de pagamento
            $paymentLink = $agendamento->getPaymentLink();

            if (!$paymentLink) {
                return [
                    'success' => false,
                    'message' => 'Não foi possível gerar o link de pagamento.'
                ];
            }

            return [
                'success' => true,
                'message' => 'Link de pagamento gerado com sucesso.',
                'payment_link' => $paymentLink,
                'agendamento' => $agendamento,
                'pagamento' => $agendamento->pagamento,
            ];

        } catch (\Exception $e) {
            Log::error('💥 [REENVIO] Erro ao reenviar link de pagamento', [
                'agendamento_id' => $agendamentoId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Erro ao gerar link de pagamento. Tente novamente.',
                'error' => $e->getMessage()
            ];
        }
    }
}
