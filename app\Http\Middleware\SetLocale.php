<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Session;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Verificar se o idioma está na sessão
        if (Session::has('locale')) {
            $locale = Session::get('locale');
        } else {
            // Usar o idioma do navegador se disponível
            $locale = $request->getPreferredLanguage(['pt_BR', 'pt', 'en']);
            
            // Garantir que o idioma padrão seja pt_BR
            $locale = in_array($locale, ['pt_BR', 'pt', 'en']) ? $locale : 'pt_BR';
            
            // Salvar na sessão para futuras requisições
            Session::put('locale', $locale);
        }
        
        // Definir o locale globalmente
        App::setLocale($locale);
        Config::set('app.locale', $locale);
        setlocale(LC_TIME, 'pt_BR', 'pt_BR.utf-8', 'pt_BR.UTF-8', 'portuguese');
        \Carbon\Carbon::setLocale('pt_BR');
        
        return $next($request);
    }
}
