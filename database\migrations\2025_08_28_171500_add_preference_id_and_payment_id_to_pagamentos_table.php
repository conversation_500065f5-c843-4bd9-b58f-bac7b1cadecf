<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pagamentos', function (Blueprint $table) {
            // Add preference_id field to store Mercado Pago preference ID
            $table->string('preference_id', 255)->nullable()->after('transaction_id');
            
            // Add payment_id field to store Mercado Pago payment ID (only exists after payment is made)
            $table->string('payment_id', 255)->nullable()->after('preference_id');
            
            // Add indexes for better performance
            $table->index('preference_id', 'pagamentos_preference_id_index');
            $table->index('payment_id', 'pagamentos_payment_id_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pagamentos', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex('pagamentos_preference_id_index');
            $table->dropIndex('pagamentos_payment_id_index');
            
            // Drop columns
            $table->dropColumn(['preference_id', 'payment_id']);
        });
    }
};
