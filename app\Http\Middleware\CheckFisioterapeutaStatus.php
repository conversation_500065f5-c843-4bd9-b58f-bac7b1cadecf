<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckFisioterapeutaStatus
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        
        // Verifica se o usuário é um fisioterapeuta
        if ($user && $user->tipo === 'fisioterapeuta' && $user->fisioterapeuta) {
            $fisioterapeuta = $user->fisioterapeuta;
            
            // Se o status for 'pending', redireciona para a tela de análise
            if ($fisioterapeuta->status === 'pending' && !$request->is('fisioterapeuta/analise')) {
                return redirect()->route('fisioterapeuta.analise');
            }
            
            // Se o status for 'rejected', redireciona para a tela de rejeição
            if ($fisioterapeuta->status === 'rejected' && !$request->is('fisioterapeuta/conta-rejeitada')) {
                return redirect()->route('fisioterapeuta.conta-rejeitada');
            }
        }

        return $next($request);
    }
}
