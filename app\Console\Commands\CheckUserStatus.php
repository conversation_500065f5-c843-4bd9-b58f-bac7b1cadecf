<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckUserStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:check {email : Email do usuário a ser verificado}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verifica o status de um usuário no banco de dados';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $user = \App\Models\User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("Usuário com email {$email} não encontrado.");
            return 1;
        }
        
        $this->info("Usuário encontrado:");
        $this->line("ID: " . $user->id);
        $this->line("Nome: " . $user->name);
        $this->line("Email: " . $user->email);
        $this->line("Ativo: " . ($user->active ? 'Sim' : 'N<PERSON>'));
        $this->line("Função: " . $user->role);
        $this->line("Email verificado: " . ($user->email_verified_at ? 'Sim' : 'Não'));
        
        return 0;
    }
}
