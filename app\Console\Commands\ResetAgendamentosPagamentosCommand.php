<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ResetAgendamentosPagamentosCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reset:agendamentos-pagamentos {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset all agendamentos, pagamentos and related commission data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!$this->option('force') && !$this->confirm('Isso irá deletar TODOS os dados de agendamentos, pagamentos e comissões. Tem certeza?')) {
            $this->info('Operação cancelada.');
            return;
        }

        $this->info('Iniciando reset das tabelas de agendamentos e pagamentos...');

        try {
            $this->info('Desabilitando verificações de foreign keys...');
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            // Resetar pagamentos de comissão (depende de vendas_afiliado)
            if (Schema::hasTable('pagamentos_comissao')) {
                $count = DB::table('pagamentos_comissao')->count();
                DB::table('pagamentos_comissao')->truncate();
                $this->info("✓ {$count} pagamentos de comissão resetados");
            }

            // Resetar vendas de afiliado
            if (Schema::hasTable('vendas_afiliado')) {
                $count = DB::table('vendas_afiliado')->count();
                DB::table('vendas_afiliado')->truncate();
                $this->info("✓ {$count} vendas de afiliado resetadas");
            }

            // Resetar notificações relacionadas a agendamentos
            if (Schema::hasTable('notificacoes')) {
                $count = DB::table('notificacoes')->count();
                DB::table('notificacoes')->truncate();
                $this->info("✓ {$count} notificações resetadas");
            }

            // Resetar logs de atividade relacionado aos usuários
            if (Schema::hasTable('user_activity_logs')) {
                $count = DB::table('user_activity_logs')->count();
                DB::table('user_activity_logs')->truncate();
                $this->info("✓ {$count} logs de atividade resetados");
            }

            // Resetar relatórios de sessão (depende de agendamentos)
            if (Schema::hasTable('relatorios_sessao')) {
                $count = DB::table('relatorios_sessao')->count();
                DB::table('relatorios_sessao')->truncate();
                $this->info("✓ {$count} relatórios de sessão resetados");
            }

            // Resetar avaliações (depende de agendamentos)
            if (Schema::hasTable('avaliacoes')) {
                $count = DB::table('avaliacoes')->count();
                DB::table('avaliacoes')->truncate();
                $this->info("✓ {$count} avaliações resetadas");
            }

            // Resetar prescrições (pode depender de agendamentos)
            if (Schema::hasTable('prescricaos')) {
                $count = DB::table('prescricaos')->count();
                DB::table('prescricaos')->truncate();
                $this->info("✓ {$count} prescrições resetadas");
            }

            // Resetar orientações domiciliares (pode depender de agendamentos)
            if (Schema::hasTable('orientacao_domiciliars')) {
                $count = DB::table('orientacao_domiciliars')->count();
                DB::table('orientacao_domiciliars')->truncate();
                $this->info("✓ {$count} orientações domiciliares resetadas");
            }

            // Resetar anexos de relatório (depende de relatórios_sessao que já foi resetado)
            if (Schema::hasTable('relatorio_anexos')) {
                $count = DB::table('relatorio_anexos')->count();
                DB::table('relatorio_anexos')->truncate();
                $this->info("✓ {$count} anexos de relatório resetados");
            }

            // Resetar mensagens (pode depender de agendamentos)
            if (Schema::hasTable('mensagens')) {
                $count = DB::table('mensagens')->count();
                DB::table('mensagens')->truncate();
                $this->info("✓ {$count} mensagens resetadas");
            }

            // Resetar agendamentos (tabela principal)
            if (Schema::hasTable('agendamentos')) {
                $count = DB::table('agendamentos')->count();
                DB::table('agendamentos')->truncate();
                $this->info("✓ {$count} agendamentos resetados");
            }

            // Resetar pagamentos (tabela principal)
            if (Schema::hasTable('pagamentos')) {
                $count = DB::table('pagamentos')->count();
                DB::table('pagamentos')->truncate();
                $this->info("✓ {$count} pagamentos resetados");
            }

            DB::commit();
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
            $this->info('✅ Reset completo realizado com sucesso!');
            $this->info('📋 Resumo: Tabelas de agendamentos e pagamentos foram resetadas, mantendo usuários, assinaturas e outras configurações.');

        } catch (\Exception $e) {
            DB::rollback();
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
            $this->error('❌ Erro durante o reset: ' . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
