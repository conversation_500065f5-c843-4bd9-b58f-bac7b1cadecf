import { Badge } from '@/components/ui/badge';
import type React from 'react';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { Activity, ArrowRight, Calendar, CheckCircle, CreditCard, FileText, Plus, User } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Início',
        href: '/paciente/dashboard',
    },
];

interface Agendamento {
    id: number;
    scheduled_at: string;
    status: string;
    fisioterapeuta: {
        user: {
            name: string;
        };
    };
}

interface PageProps {
    proximosAgendamentos: Agendamento[];
    historicoRecente: Agendamento[];
    pagamentosPendentes: Array<{
        id: number;
        status: string;
    }>;
    auth: {
        user: {
            name: string;
        };
    };
}

export default function PacienteDashboard() {
    const pageProps = usePage().props as unknown as PageProps;
    const { proximosAgendamentos, historicoRecente, pagamentosPendentes, auth } = pageProps;

    const getGreeting = () => {
        const hour = new Date().getHours();
        if (hour < 12) return 'Bom dia';
        if (hour < 18) return 'Boa tarde';
        return 'Boa noite';
    };

    const renderStatusBadge = (status: string) => {
        const map: Record<string, { label: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' | 'ghost' | 'neutral' | 'gradient'; icon?: React.ReactNode }> = {
            confirmado: { label: 'Confirmado', variant: 'default', icon: <CheckCircle className="h-3.5 w-3.5" /> },
            agendado: { label: 'Agendado', variant: 'secondary', icon: <Calendar className="h-3.5 w-3.5" /> },
            concluido: { label: 'Concluído', variant: 'outline' },
            cancelado: { label: 'Cancelado', variant: 'destructive' },
        };
        const cfg = map[status] ?? { label: status, variant: 'outline' as const };
        return (
            <Badge variant={cfg.variant} size="sm" className="gap-1">
                {cfg.icon}
                {cfg.label}
            </Badge>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Início" />

            {/* Hero Section - Seguindo padrão da homepage */}
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-4 sm:py-6 md:py-8">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-3xl font-medium text-balance sm:text-4xl md:text-5xl lg:text-6xl">
                                {getGreeting()},<span className="block text-primary">{auth.user.name?.split(' ')[0]}!</span>
                            </h1>
                            <p className="mx-auto my-2 max-w-3xl text-lg text-balance text-muted-foreground sm:my-3 sm:text-xl">
                                Como está se sentindo hoje? Vamos cuidar da sua saúde juntos com carinho e dedicação.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Main Content */}
            <div className="mx-auto w-full max-w-7xl px-4 pb-12 sm:px-6 lg:px-8">
                <div className="space-y-6">
                    {/* Ações rápidas - movidas para abaixo do hero */}
                    <section className="-mx-4 bg-muted/30 py-8 sm:-mx-6 lg:-mx-8">
                            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                                    {/* Agendar Consulta */}
                                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                        <div className="mb-4 flex items-center gap-3">
                                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                                <Plus className="h-5 w-5" />
                                            </Badge>
                                            <div>
                                                <p className="text-sm font-medium text-foreground">Agendar Consulta</p>
                                                <p className="text-xs text-muted-foreground">Nova sessão</p>
                                            </div>
                                        </div>
                                        <p className="mb-4 text-sm text-muted-foreground">Marque sua próxima sessão de fisioterapia</p>
                                        <Link href="/paciente/fisioterapeutas">
                                            <Button size="sm" className="w-full">
                                                Agendar Agora
                                            </Button>
                                        </Link>
                                    </div>

                                    {/* Meus Agendamentos */}
                                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                        <div className="mb-4 flex items-center gap-3">
                                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                                <Calendar className="h-5 w-5" />
                                            </Badge>
                                            <div>
                                                <p className="text-sm font-medium text-foreground">Meus Agendamentos</p>
                                                <p className="text-xs text-muted-foreground">Consultas</p>
                                            </div>
                                        </div>
                                        <p className="mb-4 text-sm text-muted-foreground">Visualize e gerencie suas consultas</p>
                                        <Link href="/paciente/agendamentos">
                                            <Button variant="outline" size="sm" className="w-full">
                                                Ver Agendamentos
                                            </Button>
                                        </Link>
                                    </div>

                                    {/* Histórico Médico */}
                                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                        <div className="mb-4 flex items-center gap-3">
                                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                                <FileText className="h-5 w-5" />
                                            </Badge>
                                            <div>
                                                <p className="text-sm font-medium text-foreground">Histórico Médico</p>
                                                <p className="text-xs text-muted-foreground">Progresso</p>
                                            </div>
                                        </div>
                                        <p className="mb-4 text-sm text-muted-foreground">Acompanhe seu progresso e evolução</p>
                                        <Link href="/paciente/historico">
                                            <Button variant="outline" size="sm" className="w-full">
                                                Ver Histórico
                                            </Button>
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </section>

                    {/* Alertas importantes - Seguindo padrão da homepage */}
                    {pagamentosPendentes.length > 0 && (
                        <section className="-mx-4 bg-muted/30 py-8 sm:-mx-6 lg:-mx-8">
                            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                                <div className="space-y-6">
                                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                        <div className="flex items-start gap-4">
                                            <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-orange-100">
                                                <CreditCard className="h-6 w-6 text-orange-600" />
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="text-lg font-medium text-foreground">Atenção aos Pagamentos</h3>
                                                <p className="mt-1 text-base text-muted-foreground">
                                                    Você tem {pagamentosPendentes.length} pagamento(s) pendente(s). Conclua-os para continuar seu
                                                    tratamento.
                                                </p>
                                                <Link href="/paciente/pagamentos" className="mt-3 inline-block">
                                                    <Button variant="outline" size="sm" className="gap-2">
                                                        Ver detalhes <ArrowRight className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    )}

                    

                    {/* Seção de Agendamentos e Histórico - Seguindo padrão da homepage */}
                    <section className="py-8">
                        <div className="grid gap-8 lg:grid-cols-2">
                            {/* Próximos Agendamentos */}
                            <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                <div className="mb-4 flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                            <Calendar className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <h3 className="text-lg font-medium text-foreground">Próximas Consultas</h3>
                                            <p className="text-sm text-muted-foreground">Seus agendamentos para os próximos dias</p>
                                        </div>
                                    </div>
                                    <Link href="/paciente/agendamentos">
                                        <Button variant="outline" size="sm" className="gap-2">
                                            Ver todos <ArrowRight className="h-4 w-4" />
                                        </Button>
                                    </Link>
                                </div>
                                {proximosAgendamentos.length > 0 ? (
                                    <div className="space-y-4">
                                        {proximosAgendamentos.map((agendamento: Agendamento) => (
                                            <div
                                                key={agendamento.id}
                                                className="flex items-center gap-3 rounded-lg border border-transparent bg-muted/30 p-3"
                                            >
                                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                                                    <User className="h-5 w-5 text-primary" />
                                                </div>
                                                <div>
                                                    <p className="font-medium text-foreground">{agendamento.fisioterapeuta.user.name}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {new Date(agendamento.scheduled_at).toLocaleDateString('pt-BR', {
                                                            weekday: 'short',
                                                            day: '2-digit',
                                                            month: 'short',
                                                        })}{' '}
                                                        às{' '}
                                                        {new Date(agendamento.scheduled_at).toLocaleTimeString('pt-BR', {
                                                            hour: '2-digit',
                                                            minute: '2-digit',
                                                        })}
                                                    </p>
                                                </div>
                                                {/* Desktop-only: badge next to text */}
                                                <div className="hidden sm:block ml-1.5">
                                                    {renderStatusBadge(agendamento.status)}
                                                </div>
                                                {/* spacer to push mobile badge to the right */}
                                                <div className="flex-1" />
                                                {/* Mobile-only: badge at far right */}
                                                <div className="sm:hidden">
                                                    {renderStatusBadge(agendamento.status)}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="flex flex-col items-center justify-center py-8 text-center">
                                        <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted/50">
                                            <Calendar className="h-6 w-6 text-muted-foreground" />
                                        </div>
                                        <h3 className="mb-2 font-medium text-foreground">Nenhuma consulta agendada</h3>
                                        <p className="mb-4 text-sm text-muted-foreground">Que tal agendar sua próxima sessão?</p>
                                        <Link href="/paciente/agendamentos/create">
                                            <Button size="sm" className="gap-2">
                                                <Plus className="h-4 w-4" />
                                                Agendar Consulta
                                            </Button>
                                        </Link>
                                    </div>
                                )}
                            </div>

                            {/* Histórico Recente */}
                            <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                <div className="mb-6 flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                            <Activity className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <h3 className="text-lg font-medium text-foreground">Histórico Recente</h3>
                                            <p className="text-sm text-muted-foreground">Suas últimas sessões realizadas</p>
                                        </div>
                                    </div>
                                    <Link href="/paciente/historico">
                                        <Button variant="outline" size="sm" className="gap-2">
                                            Ver histórico <ArrowRight className="h-4 w-4" />
                                        </Button>
                                    </Link>
                                </div>
                                {historicoRecente.length > 0 ? (
                                    <div className="space-y-4">
                                        {historicoRecente.map((agendamento: Agendamento) => (
                                            <div
                                                key={agendamento.id}
                                                className="flex items-center gap-3 rounded-lg border border-transparent bg-muted/30 p-3"
                                            >
                                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                                                    <CheckCircle className="h-5 w-5 text-primary" />
                                                </div>
                                                <div className="flex-1">
                                                    <p className="font-medium text-foreground">{agendamento.fisioterapeuta.user.name}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {new Date(agendamento.scheduled_at).toLocaleDateString('pt-BR', {
                                                            weekday: 'short',
                                                            day: '2-digit',
                                                            month: 'short',
                                                        })}{' '}
                                                        às{' '}
                                                        {new Date(agendamento.scheduled_at).toLocaleTimeString('pt-BR', {
                                                            hour: '2-digit',
                                                            minute: '2-digit',
                                                        })}
                                                    </p>
                                                </div>
                                                <Link href={`/paciente/agendamentos/${agendamento.id}`}>
                                                    <Button variant="ghost" size="sm">
                                                        <FileText className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="flex flex-col items-center justify-center py-8 text-center">
                                        <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted/50">
                                            <Activity className="h-6 w-6 text-muted-foreground" />
                                        </div>
                                        <h3 className="mb-2 font-medium text-foreground">Nenhuma sessão realizada</h3>
                                        <p className="text-sm text-muted-foreground">Suas sessões concluídas aparecerão aqui.</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </section>

                </div>
            </div>
        </AppLayout>
    );
}
