<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MercadoPagoService
{
    private $accessToken;
    private $baseUrl;
    private $webhookSecret;

    public function __construct()
    {
        $this->accessToken = config('mercadopago.access_token');
        $this->baseUrl = config('mercadopago.base_url');
        $this->webhookSecret = config('mercadopago.webhook_secret');
    }

    /**
     * Atualizar valor recorrente de uma assinatura (Preapproval)
     */
    public function updateSubscriptionAmount($subscriptionId, $newAmount)
    {
        if (!$this->accessToken) {
            return [
                'success' => false,
                'message' => 'Mercado Pago não configurado.'
            ];
        }

        try {
            $httpClient = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
            ]);

            $disableSslVerify = app()->environment('local', 'development')
                || (bool) env('MERCADOPAGO_DISABLE_SSL_VERIFY', false);

            if ($disableSslVerify) {
                $httpClient = $httpClient->withOptions(['verify' => false]);
            }

            $payload = [
                'auto_recurring' => [
                    'transaction_amount' => (float) $newAmount,
                ],
            ];

            $response = $httpClient->put($this->baseUrl . '/preapproval/' . $subscriptionId, $payload);

            if ($response->successful()) {
            Log::info('✅ [MP] Assinatura atualizada (valor recorrente)', [
                'subscription_id' => $subscriptionId,
                'new_amount' => $newAmount,
            ]);
                return [
                    'success' => true,
                    'subscription' => $response->json(),
                ];
            }

            Log::error('❌ [MP] Erro ao atualizar assinatura (valor recorrente)', [
                'subscription_id' => $subscriptionId,
                'status' => $response->status(),
                'response' => $response->json(),
            ]);
            return [
                'success' => false,
                'message' => 'Erro ao atualizar assinatura no Mercado Pago.'
            ];
        } catch (\Exception $e) {
            Log::error('💥 [MP] Exceção ao atualizar assinatura (valor recorrente)', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage(),
            ]);
            return [
                'success' => false,
                'message' => 'Erro interno ao atualizar assinatura no Mercado Pago.'
            ];
        }
    }

    /**
     * Criar assinatura recorrente (Preapproval)
     */
    public function createSubscription($data)
    {
        if (!$this->accessToken) {
            Log::error('❌ [MP] Mercado Pago não configurado', [
                'access_token_exists' => !empty($this->accessToken)
            ]);
            
            return [
                'success' => false,
                'message' => 'Mercado Pago não configurado. Entre em contato via WhatsApp.',
                'redirect_whatsapp' => true
            ];
        }

        try {
            $httpClient = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
            ]);

            // Desabilitar verificação SSL em desenvolvimento
            // Necessário para ambientes locais sem cadeia de certificados completa
            $disableSslVerify = app()->environment('local', 'development')
                || (bool) env('MERCADOPAGO_DISABLE_SSL_VERIFY', false);

            if ($disableSslVerify) {
                $httpClient = $httpClient->withOptions([
                    'verify' => false,
                ]);
            }

            $subscriptionData = [
                'reason' => $data['title'],
                'auto_recurring' => [
                    'frequency' => config('mercadopago.subscription.default_frequency', 1),
                    'frequency_type' => config('mercadopago.subscription.default_frequency_type', 'months'),
                    'transaction_amount' => (float) $data['amount'],
                    'currency_id' => config('mercadopago.subscription.default_currency', 'BRL'),
                ],
                'payer_email' => $data['payer']['email'],
                'back_url' => $this->getValidUrl($data['success_url'] ?? '/paciente/checkout/success'),
                'external_reference' => $data['external_reference'] ?? null,
                'notification_url' => $this->getValidUrl(config('mercadopago.webhook.url', '/webhook/mercadopago')),
                'status' => 'pending',
            ];

            Log::info('🔄 [MP] Criando assinatura', [
                'data' => $subscriptionData,
                'access_token_prefix' => substr($this->accessToken, 0, 10) . '...'
            ]);

            $response = $httpClient->post($this->baseUrl . '/preapproval', $subscriptionData);

            if ($response->successful()) {
                $subscription = $response->json();

                Log::info('✅ [MP] Assinatura criada com sucesso', [
                    'subscription_id' => $subscription['id'],
                    'init_point' => $subscription['init_point'] ?? null
                ]);

                return [
                    'success' => true,
                    'subscription_id' => $subscription['id'],
                    'init_point' => $subscription['init_point'],
                    'subscription_data' => $subscription,
                ];
            }

            Log::error('❌ [MP] Erro ao criar assinatura', [
                'response' => $response->json(),
                'status' => $response->status()
            ]);

            return [
                'success' => false,
                'message' => 'Erro ao processar assinatura. Tente novamente.',
            ];

        } catch (\Exception $e) {
            Log::error('💥 [MP] Exceção ao criar assinatura', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno. Entre em contato via WhatsApp.',
                'redirect_whatsapp' => true
            ];
        }
    }

    /**
     * Obter informações de uma assinatura
     */
    public function getSubscription($subscriptionId)
    {
        if (!$this->accessToken) {
            return null;
        }

        try {
            $httpClient = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
            ]);

            $response = $httpClient->get($this->baseUrl . '/preapproval/' . $subscriptionId);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('❌ [MP] Erro ao buscar assinatura', [
                'subscription_id' => $subscriptionId,
                'response' => $response->json(),
                'status' => $response->status()
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('💥 [MP] Exceção ao buscar assinatura', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Cancelar assinatura
     */
    public function cancelSubscription($subscriptionId)
    {
        if (!$this->accessToken) {
            return false;
        }

        try {
            $httpClient = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
            ]);

            $response = $httpClient->put($this->baseUrl . '/preapproval/' . $subscriptionId, [
                'status' => 'cancelled'
            ]);

            if ($response->successful()) {
                Log::info('✅ [MP] Assinatura cancelada', [
                    'subscription_id' => $subscriptionId
                ]);
                return true;
            }

            Log::error('❌ [MP] Erro ao cancelar assinatura', [
                'subscription_id' => $subscriptionId,
                'response' => $response->json(),
                'status' => $response->status()
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error('💥 [MP] Exceção ao cancelar assinatura', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Criar preferência de pagamento
     * @deprecated Use createSubscription() para assinaturas recorrentes
     */
    public function createPreference($data)
    {
        if (!$this->accessToken) {
            return [
                'success' => false,
                'message' => 'Mercado Pago não configurado. Entre em contato via WhatsApp.',
                'redirect_whatsapp' => true
            ];
        }

        try {
            $httpClient = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
            ]);

            // Em desenvolvimento, desabilitar verificação SSL (mesma regra das outras chamadas)
            $disableSslVerify = app()->environment('local', 'development')
                || (bool) env('MERCADOPAGO_DISABLE_SSL_VERIFY', false);

            if ($disableSslVerify) {
                $httpClient = $httpClient->withOptions([
                    'verify' => false,
                ]);
            }

            // Normalizar URLs (Mercado Pago não aceita localhost/127.0.0.1)
            $successUrl = $this->getValidUrl($data['success_url'] ?? url('/checkout/success'));
            $failureUrl = $this->getValidUrl($data['failure_url'] ?? url('/checkout/failure'));
            $pendingUrl = $this->getValidUrl($data['pending_url'] ?? url('/checkout/pagamentos/pending'));
            $notificationUrl = $this->getValidUrl($data['notification_url'] ?? url('/webhook/mercadopago'));

            $payload = [
                'items' => [
                    [
                        'title' => $data['items'][0]['title'] ?? $data['title'] ?? 'Produto',
                        'description' => $data['items'][0]['description'] ?? $data['description'] ?? '',
                        'quantity' => $data['items'][0]['quantity'] ?? 1,
                        'currency_id' => $data['items'][0]['currency_id'] ?? 'BRL',
                        'unit_price' => (float) ($data['items'][0]['unit_price'] ?? $data['amount'] ?? 0),
                    ]
                ],
                'payer' => [
                    'name' => $data['payer']['name'] ?? '',
                    'email' => $data['payer']['email'] ?? '',
                ],
                'back_urls' => [
                    'success' => $successUrl,
                    'failure' => $failureUrl,
                    'pending' => $pendingUrl,
                ],
                'auto_return' => 'approved',
                'external_reference' => $data['external_reference'] ?? null,
                'notification_url' => $notificationUrl,
            ];

            Log::info('🔄 [MP] Criando preferência de pagamento único', [
                'payload' => [
                    'items' => $payload['items'],
                    'payer' => $payload['payer'],
                    'back_urls' => $payload['back_urls'],
                    'external_reference' => $payload['external_reference'],
                    'notification_url' => $payload['notification_url'],
                ],
            ]);

            $response = $httpClient->post($this->baseUrl . '/checkout/preferences', $payload);

            if ($response->successful()) {
                $preference = $response->json();
                return [
                    'success' => true,
                    'preference_id' => $preference['id'],
                    'init_point' => $preference['init_point'],
                ];
            }

            Log::error('Erro ao criar preferência Mercado Pago', [
                'response' => $response->json(),
                'status' => $response->status()
            ]);

            return [
                'success' => false,
                'message' => 'Erro ao processar pagamento. Tente novamente.',
            ];

        } catch (\Exception $e) {
            Log::error('Exceção ao criar preferência Mercado Pago', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno. Entre em contato via WhatsApp.',
                'redirect_whatsapp' => true
            ];
        }
    }

    /**
     * Obter informações de um pagamento
     */
    public function getPayment($paymentId)
    {
        if (!$this->accessToken) {
            return null;
        }

        try {
            $httpClient = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
            ]);

            // Desabilitar verificação SSL em desenvolvimento (mesma regra das outras chamadas)
            $disableSslVerify = app()->environment('local', 'development')
                || (bool) env('MERCADOPAGO_DISABLE_SSL_VERIFY', false);

            if ($disableSslVerify) {
                $httpClient = $httpClient->withOptions([
                    'verify' => false,
                ]);
            }

            $response = $httpClient->get($this->baseUrl . '/v1/payments/' . $paymentId);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('❌ [MP] Erro ao obter pagamento', [
                'payment_id' => $paymentId,
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('💥 [MP] Exceção ao obter pagamento', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Verificar se o Mercado Pago está configurado
     */
    public function isConfigured()
    {
        return !empty($this->accessToken);
    }


    /**
     * Obter webhook secret
     */
    public function getWebhookSecret()
    {
        return $this->webhookSecret;
    }

    /**
     * Processar webhook do Mercado Pago
     */
    public function processWebhook($data)
    {
        try {
            if (isset($data['type']) && $data['type'] === 'payment') {
                $paymentId = $data['data']['id'] ?? null;
                
                if ($paymentId) {
                    $paymentInfo = $this->getPayment($paymentId);
                    
                    if ($paymentInfo) {
                        // Processar atualização do pagamento
                        return $this->updatePaymentStatus($paymentInfo);
                    }
                }
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Erro ao processar webhook Mercado Pago', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Atualizar status do pagamento baseado na resposta do MP
     */
    private function updatePaymentStatus($paymentInfo)
    {
        $externalReference = $paymentInfo['external_reference'] ?? null;
        $status = $paymentInfo['status'] ?? null;
        $paymentId = $paymentInfo['id'] ?? null;

        if (!$externalReference || !$status) {
            return false;
        }

        try {
            // Encontrar o pagamento no banco
            $pagamento = \App\Models\Pagamento::find($externalReference);

            if (!$pagamento) {
                Log::warning('Pagamento não encontrado no banco', [
                    'external_reference' => $externalReference
                ]);
                return false;
            }

            // Mapear status do Mercado Pago
            $newStatus = $this->mapMercadoPagoStatus($status);

            // Preparar dados para atualização
            $updateData = [
                'status' => $newStatus,
                'gateway_response' => $paymentInfo,
            ];

            // Se foi aprovado, definir data de pagamento, método e payment_id
            if ($newStatus === 'pago') {
                $updateData['paid_at'] = now();
                $updateData['method'] = $this->mapPaymentMethod($paymentInfo['payment_method_id'] ?? null);
                $updateData['payment_id'] = $paymentId; // Armazenar o payment_id real
            }

            // Atualizar o pagamento
            $pagamento->update($updateData);

            Log::info('Pagamento atualizado via webhook', [
                'pagamento_id' => $pagamento->id,
                'external_reference' => $externalReference,
                'old_status' => $pagamento->getOriginal('status'),
                'new_status' => $newStatus,
                'mp_payment_id' => $paymentId,
                'preference_id' => $pagamento->preference_id
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Erro ao atualizar pagamento via webhook', [
                'external_reference' => $externalReference,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Mapear status do Mercado Pago para status interno
     */
    private function mapMercadoPagoStatus($mpStatus)
    {
        $statusMap = config('mercadopago.status_mapping.payment', []);
        return $statusMap[$mpStatus] ?? 'pendente';
    }

    /**
     * Mapear método de pagamento do Mercado Pago
     */
    private function mapPaymentMethod($mpMethod)
    {
        if (!$mpMethod) return null;

        $methodMap = [
            'pix' => 'pix',
            'bolbradesco' => 'boleto',
            'visa' => 'cartao_credito',
            'master' => 'cartao_credito',
            'amex' => 'cartao_credito',
            'elo' => 'cartao_credito',
            'hipercard' => 'cartao_credito',
            'debvisa' => 'cartao_debito',
            'debmaster' => 'cartao_debito',
        ];

        return $methodMap[$mpMethod] ?? 'cartao_credito';
    }

    /**
     * Obter URL base da API
     */
    public function getBaseUrl()
    {
        return $this->baseUrl;
    }

    /**
     * Obter access token
     */
    public function getAccessToken()
    {
        return $this->accessToken;
    }

    /**
     * Get a valid URL for Mercado Pago (localhost not allowed)
     */
    private function getValidUrl($path)
    {
        // Normalizar para URL completa
        $url = str_starts_with($path, 'http') ? $path : url($path);

        // Se a URL contiver localhost ou 127.0.0.1, substituir pelo APP_URL
        if (str_contains($url, 'localhost') || str_contains($url, '127.0.0.1')) {
            $appUrl = env('APP_URL', 'http://localhost');
            
            // Garantir que APP_URL tenha protocolo
            if (!str_starts_with($appUrl, 'http://') && !str_starts_with($appUrl, 'https://')) {
                $appUrl = 'https://' . $appUrl;
            }
            
            $parsed = parse_url($url);
            $pathOnly = $parsed['path'] ?? '';
            $query = isset($parsed['query']) ? ('?' . $parsed['query']) : '';
            return rtrim($appUrl, '/') . $pathOnly . $query;
        }

        return $url;
    }

}
