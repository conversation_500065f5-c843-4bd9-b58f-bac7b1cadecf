<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Informações sobre sua conta - F4 Fisio</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background-color: #ffffff;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            width: 120px;
            height: auto;
            margin: 0 auto 20px;
            display: block;
        }
        .title {
            color: #1e293b;
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 10px 0;
        }
        .subtitle {
            color: #64748b;
            font-size: 16px;
            margin: 0;
        }
        .content {
            margin: 30px 0;
        }
        .greeting {
            font-size: 18px;
            color: #1e293b;
            margin-bottom: 20px;
        }
        .message {
            color: #475569;
            margin-bottom: 25px;
            line-height: 1.7;
        }
        .rejection-box {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        .rejection-title {
            color: #dc2626;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .info-list {
            color: #475569;
            margin: 0;
            padding-left: 20px;
        }
        .info-list li {
            margin-bottom: 8px;
        }
        .account-info {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .account-info-title {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .account-detail {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #64748b;
            font-size: 14px;
        }
        .footer-links {
            margin-top: 15px;
        }
        .footer-links a {
            color: #ef4444;
            text-decoration: none;
            margin: 0 10px;
        }
        .rejected-badge {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }
        .next-steps {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        .next-steps-title {
            color: #d97706;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .reason-box {
            background-color: #f0f9ff;
            border-left: 4px solid #0ea5e9;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        .reason-title {
            color: #0369a1;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .reason-content {
            color: #475569;
            line-height: 1.6;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{ config('app.url') }}/images/logo.png" alt="F4 Fisio" class="logo">
            <div class="rejected-badge">❌ Conta Rejeitada</div>
            <h1 class="title">Informações sobre sua conta</h1>
            <p class="subtitle">Atualizações sobre o seu cadastro na plataforma F4 Fisio</p>
        </div>

        <div class="content">
            <p class="greeting">Olá, <strong>{{ $nome }}</strong>!</p>
            
            <p class="message">
                Agradecemos seu interesse em fazer parte da plataforma F4 Fisio como profissional. Após análise detalhada do seu cadastro, informamos que, no momento, não foi possível aprovar sua conta.
            </p>

            <div class="rejection-box">
                <div class="rejection-title">❌ O que isso significa:</div>
                <ul class="info-list">
                    <li>Seu perfil não estará visível para pacientes na plataforma</li>
                    <li>Você não poderá receber agendamentos através do sistema</li>
                    <li>Sua conta permanecerá inativa até nova avaliação</li>
                    <li>Alguns recursos da plataforma estarão limitados</li>
                </ul>
            </div>

            <div class="reason-box">
                <div class="reason-title">📝 Motivo da rejeição:</div>
                <div class="reason-content">
                    "{{ $motivoRejeicao }}"
                </div>
            </div>

            <div class="next-steps">
                <div class="next-steps-title">📋 O que você pode fazer:</div>
                <ul class="info-list">
                    <li>Analise cuidadosamente o motivo informado acima</li>
                    <li>Realize as correções necessárias no seu perfil</li>
                    <li>Reúna a documentação que comprova suas qualificações</li>
                    <li>Entre em contato com nosso suporte para esclarecer dúvidas</li>
                    <li>Após as correções, você pode solicitar uma reavaliação</li>
                </ul>
            </div>

            <p class="message">
                Entendemos que essa notícia pode ser frustrante, mas nossa prioridade é garantir a qualidade e segurança dos profissionais em nossa plataforma. A decisão foi tomada baseada em nossos critérios de qualificação e compliance.
            </p>

            <div class="account-info">
                <div class="account-info-title">📋 Informações da sua conta:</div>
                <div class="account-detail"><strong>Nome:</strong> {{ $nome }}</div>
                <div class="account-detail"><strong>Email:</strong> {{ $email }}</div>
                <div class="account-detail"><strong>CREFITO:</strong> {{ $fisioterapeuta->crefito }}</div>
                <div class="account-detail"><strong>Status:</strong> <span style="color: #ef4444; font-weight: 600;">Rejeitado</span></div>
                <div class="account-detail"><strong>Data da análise:</strong> {{ date('d/m/Y H:i') }}</div>
            </div>

            <p class="message">
                Se você acredita que houve algum equívoco na análise ou se tiver realizado as correções necessárias, por favor, entre em contato com nosso equipe de suporte. Estamos à disposição para ajudar e esclarecer qualquer dúvida.
            </p>

            <p class="message">
                Agradecemos sua compreensão e esperamos poder contar com você em nossa equipe no futuro!
            </p>
        </div>

        <div class="footer">
            <p>Este email foi enviado automaticamente pelo sistema F4 Fisio.</p>
            <p>Estamos aqui para ajudar você. Entre em contato se precisar de suporte.</p>
            
            <div class="footer-links">
                <a href="{{ config('app.url') }}">F4 Fisio</a> |
                <a href="{{ config('app.url') }}/contato">Suporte</a> |
                <a href="{{ config('app.url') }}/politica-privacidade">Privacidade</a>
            </div>
            
            <p style="margin-top: 20px; font-size: 12px;">
                © {{ date('Y') }} F4 Fisio. Todos os direitos reservados.<br>
                <em>Cuidando da sua saúde com excelência e dedicação.</em>
            </p>
        </div>
    </div>
</body>
</html>
