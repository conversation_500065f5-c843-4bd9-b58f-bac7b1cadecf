<?php

namespace App\Http\Controllers\Fisioterapeuta;

use App\Http\Controllers\Controller;
use App\Models\Fisioterapeuta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class PerfilController extends Controller
{
    /**
     * Display the fisioterapeuta profile.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $fisioterapeuta = $user->fisioterapeuta;
        
        // Se não tem perfil de fisioterapeuta, criar um básico
        if (!$fisioterapeuta) {
            $fisioterapeuta = Fisioterapeuta::create([
                'user_id' => $user->id,
                'crefito' => '',
                'specializations' => [],
                'bio' => '',
                'hourly_rate' => 0,
                'available_areas' => [],
                'working_hours' => [],
                'available' => false,
                'rating' => 0,
                'total_reviews' => 0,
            ]);
        }
        
        // Listas para os selects
        $especialidades = [
            'Ortopédica',
            'Neurológica',
            'Respiratória',
            'Cardíaca',
            'Geriátrica',
            'Pediátrica',
            'Esportiva',
            'Dermatofuncional',
            'Uroginecológica',
            'Aquática',
            'Intensiva',
            'Oncológica',
            'Reumatológica',
            'Traumato-Ortopédica',
            'Outras'
        ];
        
        $areas = [
            'São Paulo - Centro',
            'São Paulo - Zona Norte',
            'São Paulo - Zona Sul',
            'São Paulo - Zona Leste',
            'São Paulo - Zona Oeste',
            'ABC Paulista',
            'Guarulhos',
            'Osasco',
            'Barueri',
            'Alphaville',
            'Carapicuíba',
            'Itapevi',
            'Jandira',
            'Cotia',
            'Taboão da Serra',
            'Embu das Artes',
            'São Bernardo do Campo',
            'Santo André',
            'São Caetano do Sul',
            'Diadema',
            'Mauá',
            'Ribeirão Pires',
            'Rio Grande da Serra'
        ];
        
        return Inertia::render('fisioterapeuta/perfil', [
            'fisioterapeuta' => $fisioterapeuta,
            'user' => $user,
            'especialidades' => $especialidades,
            'areas' => $areas,
            // Taxa de serviço global (percentual), mesma origem do setup
            'serviceFeePercent' => (float) config('fees.service_fee_percent', 20),
        ]);
    }
    
    /**
     * Update the fisioterapeuta profile.
     */
    public function update(Request $request)
    {
        $user = auth()->user();
        $fisioterapeuta = $user->fisioterapeuta;
        
        if (!$fisioterapeuta) {
            return redirect()->route('fisioterapeuta.perfil')
                ->with('error', 'Perfil de fisioterapeuta não encontrado.');
        }
        
        $validated = $request->validate([
            'crefito' => 'required|string|max:50|unique:fisioterapeutas,crefito,' . $fisioterapeuta->id,
            'specializations' => 'required|array|min:1',
            'specializations.*' => 'string|max:100',
            'bio' => 'required|string|max:1000',
            'hourly_rate' => 'required|numeric|min:10|max:999.99',
            'session_rate' => 'nullable|numeric|min:10|max:999.99',
            'service_rates' => 'nullable|array',
            'service_rates.avaliacao' => 'nullable|numeric|min:10|max:999.99',
            'service_rates.sessao' => 'nullable|numeric|min:10|max:999.99',
            'service_rates.teleatendimento' => 'nullable|numeric|min:10|max:999.99',
            'available_areas' => 'required|array|min:1',
            'available_areas.*' => 'string|max:100',
            'available' => 'boolean',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ], [
            'hourly_rate.required' => 'O valor da hora é obrigatório.',
            'hourly_rate.min' => 'O valor da hora deve ser pelo menos R$ 10,00.',
            'hourly_rate.max' => 'O valor da hora não pode exceder R$ 999,99.',
            'session_rate.min' => 'O valor por sessão deve ser pelo menos R$ 10,00.',
            'service_rates.*.min' => 'Cada serviço deve ter no mínimo R$ 10,00 quando informado.',
            'service_rates.*.max' => 'Cada serviço não pode exceder R$ 999,99.',
        ]);
        
        // Validar dados do usuário
        $userValidated = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
        ]);
        
        // Forçar o modo de cobrança por hora
        $validated['pricing_mode'] = 'por_hora';

        DB::transaction(function () use ($validated, $userValidated, $fisioterapeuta, $user, $request) {
            // Atualizar dados do usuário
            $user->update($userValidated);
            
            // Upload de avatar se fornecido
            if ($request->hasFile('avatar')) {
                // Deletar avatar anterior se existir
                if ($user->avatar) {
                    Storage::disk('public')->delete($user->avatar);
                }
                
                $avatarPath = $request->file('avatar')->store('avatars', 'public');
                $user->update(['avatar' => $avatarPath]);
            }
            
            // Atualizar dados do fisioterapeuta
            $fisioterapeuta->update($validated);
        });
        
        return redirect()->route('fisioterapeuta.perfil')
            ->with('success', 'Perfil atualizado com sucesso!');
    }
    
    /**
     * Upload avatar
     */
    public function uploadAvatar(Request $request)
    {
        $request->validate([
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);
        
        $user = auth()->user();
        
        // Deletar avatar anterior se existir
        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
        }
        
        $avatarPath = $request->file('avatar')->store('avatars', 'public');
        $user->update(['avatar' => $avatarPath]);
        
        // Para requisições Inertia, devemos retornar um redirect (303) com flash
        return redirect()
            ->route('fisioterapeuta.perfil')
            ->with('success', 'Avatar atualizado com sucesso!')
            ->with('avatar_url', Storage::url($avatarPath));
    }
    
    /**
     * Remove avatar
     */
    public function removeAvatar()
    {
        $user = auth()->user();
        
        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
            $user->update(['avatar' => null]);
        }
        
        // Retornar redirect compatível com Inertia
        return redirect()
            ->route('fisioterapeuta.perfil')
            ->with('success', 'Avatar removido com sucesso!');
    }
}
