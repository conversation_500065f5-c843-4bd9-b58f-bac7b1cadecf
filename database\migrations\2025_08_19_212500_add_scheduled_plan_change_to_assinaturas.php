<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('assinaturas', function (Blueprint $table) {
            if (!Schema::hasColumn('assinaturas', 'scheduled_new_plano_id')) {
                $table->unsignedBigInteger('scheduled_new_plano_id')->nullable()->after('plano_id');
            }
            if (!Schema::hasColumn('assinaturas', 'scheduled_change_date')) {
                $table->date('scheduled_change_date')->nullable()->after('scheduled_new_plano_id');
            }
        });
    }

    public function down(): void
    {
        Schema::table('assinaturas', function (Blueprint $table) {
            $drops = [];
            if (Schema::hasColumn('assinaturas', 'scheduled_new_plano_id')) {
                $drops[] = 'scheduled_new_plano_id';
            }
            if (Schema::hasColumn('assinaturas', 'scheduled_change_date')) {
                $drops[] = 'scheduled_change_date';
            }
            if (!empty($drops)) {
                $table->dropColumn($drops);
            }
        });
    }
};
