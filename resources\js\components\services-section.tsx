import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from '@inertiajs/react';
import { ArrowRight, Brain, Check, Heart, Video, Zap } from 'lucide-react';
import React from 'react';

export default function ServicesSection() {
    return (
        <section id="servicos" className="bg-background py-20">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="mb-16 text-center">
                    <h2 className="text-3xl font-medium text-balance md:text-4xl">Fisioterapia</h2>
                    <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                        Tratamentos de fisioterapia personalizados para reabilitação e prevenção, com atendimento domiciliar e online.
                    </p>
                </div>

                <div className="grid gap-8 lg:grid-cols-4">
                    {/* Fisioterapia Domiciliar */}
                    <ServiceCard
                        icon={Zap}
                        title="Fisioterapia Domiciliar"
                        mode="Presencial"
                        description="Atendimento em casa com avaliação, plano terapêutico e exercícios guiados."
                        features={[
                            'Avaliação funcional completa',
                            'Plano de tratamento personalizado',
                            'Exercícios com acompanhamento',
                        ]}
                        ctaText="Ver Fisioterapeutas"
                        ctaLink="/buscar"
                    />

                    {/* Fisioterapia Respiratória */}
                    <ServiceCard
                        icon={Heart}
                        title="Fisioterapia Respiratória"
                        mode="Presencial"
                        description="Técnicas para melhorar a função pulmonar e a respiração."
                        features={["Higiene brônquica", 'Exercícios ventilatórios', 'Treino de musculatura respiratória']}
                        ctaText="Ver Fisioterapeutas"
                        ctaLink="/buscar"
                    />

                    {/* Fisioterapia Neurológica */}
                    <ServiceCard
                        icon={Brain}
                        title="Fisioterapia Neurológica"
                        mode="Presencial"
                        description="Reabilitação para AVC, Parkinson e outras condições neurológicas."
                        features={["Treino de marcha", 'Equilíbrio e coordenação', 'Estimulação funcional']}
                        ctaText="Ver Fisioterapeutas"
                        ctaLink="/buscar"
                    />

                    {/* Teleconsulta de Fisioterapia */}
                    <ServiceCard
                        icon={Video}
                        title="Teleconsulta de Fisioterapia"
                        mode="Online"
                        description="Orientação remota com exercícios e acompanhamento por vídeo."
                        features={["Avaliação online", 'Exercícios supervisionados', 'Ajustes semanais do plano']}
                        ctaText="Ver Fisioterapeutas"
                        ctaLink="/buscar"
                    />
                </div>
            </div>
        </section>
    );
}

interface ServiceCardProps {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    mode?: string;
    description: string;
    features: string[];
    ctaText: string;
    ctaLink?: string;
    ctaAction?: () => void;
}

const ServiceCard = ({ icon: Icon, title, mode, description, features, ctaText, ctaLink, ctaAction }: ServiceCardProps) => {
    const cardContent = (
        <Card className={`relative h-full transition-all duration-300 hover:shadow-lg`}>

            <CardHeader className="text-center">
                <div className="mx-auto mb-4">
                    <Badge variant="gradient" size="icon-lg" className="h-16 w-16">
                        <Icon className="h-8 w-8" />
                    </Badge>
                </div>
                <CardTitle className="text-xl font-medium">{title}</CardTitle>
                {mode && (
                    <div className="mx-auto mt-3 inline-flex rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary">
                        {mode}
                    </div>
                )}
                <CardDescription className="mt-3 text-base leading-relaxed">{description}</CardDescription>
            </CardHeader>

            <CardContent className="flex flex-grow flex-col">
                <ul className="mb-8 flex-grow space-y-3">
                    {features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-3">
                            <Check className="h-4 w-4 text-primary" />
                            <span className="text-sm text-muted-foreground">{feature}</span>
                        </li>
                    ))}
                </ul>

                {ctaLink ? (
                    <Link href={ctaLink}>
                        <Button className={`w-full`} variant={'outline'}>
                            {ctaText}
                            <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                    </Link>
                ) : (
                    <Button
                        onClick={ctaAction}
                        className={`w-full`}
                        variant={'outline'}
                    >
                        {ctaText}
                        <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                )}
            </CardContent>
        </Card>
    );

    return cardContent;
};
