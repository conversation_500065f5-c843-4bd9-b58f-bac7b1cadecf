import { Button } from '@/components/ui/button';
import { ToastProvider } from '@/components/ui/toast-notification';
import { TooltipProvider } from '@/components/ui/tooltip';
import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { ReactNode } from 'react';

interface PublicLayoutProps {
    children: ReactNode;
    title?: string;
    description?: string;
}

export default function PublicLayout({ children, title, description }: PublicLayoutProps) {
    const { auth } = usePage<SharedData>().props;

    return (
        <>
            <Head title={title}>
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />
                {description && <meta name="description" content={description} />}
            </Head>

            <ToastProvider />
            <TooltipProvider>
                <header className="sticky top-0 z-50 border-b bg-background shadow-sm">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex h-16 items-center justify-between">
                            <div className="flex items-center">
                                <Link href={route('home')} className="flex items-center">
                                    <img src="/images/logo.png" alt="F4 Fisio" className="h-8 w-auto" loading="eager" />
                                </Link>
                            </div>

                            <div className="md:hidden">
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => {
                                        const mobileMenu = document.getElementById('mobile-menu');
                                        if (mobileMenu) {
                                            mobileMenu.classList.toggle('hidden');
                                        }
                                    }}
                                >
                                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                    </svg>
                                </Button>
                            </div>
                            <nav className="hidden space-x-8 md:flex">
                                <Link href={route('home')} className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary">
                                    Início
                                </Link>
                                <Link
                                    href={route('buscar')}
                                    className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
                                >
                                    Buscar Fisioterapeutas
                                </Link>
                                <Link
                                    href={route('sobre')}
                                    className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
                                >
                                    Sobre Nós
                                </Link>
                                <Link
                                    href={route('servicos')}
                                    className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
                                >
                                    Serviços
                                </Link>
                                <Link
                                    href={route('afiliados')}
                                    className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
                                >
                                    Afiliados
                                </Link>
                                <Link
                                    href={route('contato')}
                                    className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
                                >
                                    Contato
                                </Link>
                                <Link
                                    href="/empresa"
                                    className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
                                >
                                    Empresa
                                </Link>
                            </nav>
                            <div className="flex items-center gap-3">
                                {auth.user ? (
                                    <Button asChild>
                                        <Link href={route('dashboard')}>Dashboard</Link>
                                    </Button>
                                ) : (
                                    <>
                                        <Button variant="outline" asChild>
                                            <Link href={route('login')}>Entrar</Link>
                                        </Button>
                                        <Button asChild>
                                            <Link href={route('register')}>Cadastrar</Link>
                                        </Button>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>

                    <div id="mobile-menu" className="hidden border-t bg-background md:hidden">
                        <div className="space-y-1 px-4 pt-2 pb-3">
                            <Link
                                href={route('home')}
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Início
                            </Link>
                            <Link
                                href={route('buscar')}
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Buscar Fisioterapeutas
                            </Link>
                            <Link
                                href={route('sobre')}
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Sobre
                            </Link>
                            <Link
                                href={route('servicos')}
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Serviços
                            </Link>
                            <Link
                                href={route('afiliados')}
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Afiliados
                            </Link>
                            <Link
                                href={route('contato')}
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Contato
                            </Link>
                            <Link
                                href="/empresa"
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Empresa
                            </Link>
                            <div className="border-t pt-4">
                                {auth.user ? (
                                    <Button asChild className="w-full justify-start">
                                        <Link href={route('dashboard')}>Dashboard</Link>
                                    </Button>
                                ) : (
                                    <div className="space-y-2">
                                        <Button variant="ghost" asChild className="w-full justify-start">
                                            <Link href={route('login')}>Entrar</Link>
                                        </Button>
                                        <Button asChild className="w-full justify-start">
                                            <Link href={route('register')}>Cadastrar</Link>
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </header>

                <main className="min-h-screen">{children}</main>

                <footer className="bg-primary text-primary-foreground">
                    <div className="mx-auto max-w-7xl px-4 py-20 sm:px-6 lg:px-8">
                        <div className="grid gap-8 md:grid-cols-4">
                            <div>
                                <h3 className="mb-4 text-2xl font-medium">F4 Fisio</h3>
                                <p className="text-base leading-relaxed text-primary-foreground/80">
                                    Fisioterapia domiciliar de qualidade. Cuidado personalizado no conforto da sua casa.
                                </p>
                            </div>
                            <div>
                                <h4 className="mb-4 text-base font-semibold text-primary-foreground">Navegação</h4>
                                <ul className="space-y-3 text-sm text-primary-foreground/80">
                                    <li><Link href="/" className="transition-colors hover:text-primary-foreground">Início</Link></li>
                                    <li><Link href="/buscar" className="transition-colors hover:text-primary-foreground">Buscar Fisioterapeutas</Link></li>
                                    <li><Link href="/sobre" className="transition-colors hover:text-primary-foreground">Sobre Nós</Link></li>
                                    <li><Link href="/servicos" className="transition-colors hover:text-primary-foreground">Serviços</Link></li>
                                    <li><Link href="/afiliados" className="transition-colors hover:text-primary-foreground">Afiliados</Link></li>
                                    <li><Link href="/contato" className="transition-colors hover:text-primary-foreground">Contato</Link></li>
                                    <li><Link href="/empresa" className="transition-colors hover:text-primary-foreground">Empresa</Link></li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="mb-4 text-base font-semibold text-primary-foreground">Contato</h4>
                                <ul className="space-y-3 text-sm text-primary-foreground/80">
                                    <li>(11) 97819-6207</li>
                                    <li><EMAIL></li>
                                    <li>São Paulo, SP</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="mb-4 text-base font-semibold text-primary-foreground">Links Úteis</h4>
                                <ul className="space-y-3 text-sm text-primary-foreground/80">
                                    <li><Link href="/servicos" className="transition-colors hover:text-primary-foreground">Serviços</Link></li>
                                    <li><a href="/#faq" className="transition-colors hover:text-primary-foreground">FAQ</a></li>
                                    <li><Link href="/politica-privacidade" className="transition-colors hover:text-primary-foreground">Política de Privacidade</Link></li>
                                    <li><Link href="/termos-uso" className="transition-colors hover:text-primary-foreground">Termos de Uso</Link></li>
                                </ul>
                            </div>
                        </div>
                        <div className="mt-12 border-t border-primary-foreground/20 pt-8 text-center text-sm text-primary-foreground/70">
                            <p>&copy; 2025 F4 Fisio. Todos os direitos reservados.</p>
                        </div>
                    </div>
                </footer>
            </TooltipProvider>
        </>
    );
}
