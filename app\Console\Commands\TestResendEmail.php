<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Resend\Laravel\Facades\Resend;

class TestResendEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'resend:test {email=rafael<PERSON><PERSON><PERSON>@gmail.com : Email address to send test email to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test email using Resend';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("Enviando email de teste para: {$email}");
        
        try {
            $result = Resend::emails()->send([
                'from' => 'F4 Fisio <<EMAIL>>',
                'to' => [$email],
                'subject' => 'Teste do Resend - F4 Fisio',
                'html' => '
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h1 style="color: #2563eb;">🎉 Resend configurado com sucesso!</h1>
                        <p>Este é um email de teste do sistema F4 Fisio.</p>
                        <p>Se você está recebendo este email, significa que o Resend está funcionando perfeitamente!</p>
                        <hr style="margin: 20px 0;">
                        <p style="color: #6b7280; font-size: 14px;">
                            Enviado em: ' . now()->format('d/m/Y H:i:s') . '<br>
                            Sistema: F4 Fisio
                        </p>
                    </div>
                '
            ]);

            $this->info("✅ Email enviado com sucesso!");
            $this->info("ID do email: " . $result->id);
            
        } catch (\Exception $e) {
            $this->error("❌ Erro ao enviar email: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
