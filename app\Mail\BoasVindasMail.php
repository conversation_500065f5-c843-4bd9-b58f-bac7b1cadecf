<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class BoasVindasMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public string $nome,
        public string $email,
        public string $tipoUsuario,
        public ?string $loginUrl = null
    ) {}

    public function envelope(): Envelope
    {
        $assuntos = [
            'paciente' => 'Bem-vindo(a) à F4 Fisio! 🎉',
            'fisioterapeuta' => 'Bem-vindo(a) à equipe F4 Fisio! 👨‍⚕️',
            'empresa' => 'Bem-vindo(a) à F4 Fisio! 🏢',
        ];

        return new Envelope(
            subject: $assuntos[$this->tipoUsuario] ?? 'Bem-vindo(a) à F4 Fisio!',
            from: new Address(config('mail.from.address'), config('mail.from.name'))
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.boas-vindas',
            with: [
                'nome' => $this->nome,
                'email' => $this->email,
                'tipoUsuario' => $this->tipoUsuario,
                'loginUrl' => $this->loginUrl ?? \App\Helpers\EmailUrlHelper::generateEmailUrl('login'),
            ],
        );
    }

    public function attachments(): array
    {
        return [];
    }
}
