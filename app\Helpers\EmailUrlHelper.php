<?php

namespace App\Helpers;

use Illuminate\Support\Facades\URL;

class EmailUrlHelper
{
    /**
     * Gera uma URL para uso em emails com base no APP_URL
     * 
     * @param string $route Nome da rota
     * @param array $parameters Parâmetros da rota
     * @param bool $absolute Se deve gerar URL absoluta
     * @return string URL formatada para emails
     */
    public static function generateEmailUrl($route, $parameters = [], $absolute = true)
    {
        $appUrl = config('app.url');
        
        // Se APP_URL for http://localhost, manter comportamento atual (http://127.0.0.1:8000)
        if ($appUrl === 'http://localhost') {
            // Em desenvolvimento, usar a URL local
            $baseUrl = 'http://127.0.0.1:8000';
        } 
        // Para qualquer outro domínio, usar HTTPS com o domínio especificado
        else {
            // Extrair o domínio sem o protocolo
            $domain = str_replace(['http://', 'https://'], '', $appUrl);
            $baseUrl = 'https://' . $domain;
        }
        
        // Gerar a URL com o baseUrl personalizado
        if ($absolute) {
            $url = URL::route($route, $parameters, false);
            return $baseUrl . $url;
        }
        
        return URL::route($route, $parameters, false);
    }
    
    /**
     * Gera uma URL temporária assinada para uso em emails
     * 
     * @param string $route Nome da rota
     * @param \DateTimeInterface $expiration Data de expiração
     * @param array $parameters Parâmetros da rota
     * @return string URL temporária assinada
     */
    public static function generateTemporarySignedRoute($route, $expiration, $parameters = [])
    {
        $appUrl = config('app.url');
        
        // Se APP_URL for http://localhost, manter comportamento atual (http://127.0.0.1:8000)
        if ($appUrl === 'http://localhost') {
            // Em desenvolvimento, usar a URL local
            $baseUrl = 'http://127.0.0.1:8000';
        } 
        // Para qualquer outro domínio, usar HTTPS com o domínio especificado
        else {
            // Extrair o domínio sem o protocolo
            $domain = str_replace(['http://', 'https://'], '', $appUrl);
            $baseUrl = 'https://' . $domain;
        }
        
        // Gerar a URL temporária assinada
        $url = URL::temporarySignedRoute($route, $expiration, $parameters, false);
        
        return $baseUrl . $url;
    }
}
