<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Agendamento;
use App\Models\Pagamento;
use App\Services\UnifiedPaymentService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

try {
    // Inicializar <PERSON>
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    echo "=== DEBUG: Tentativa de Criar Agendamento ===\n\n";

    // Pegar paciente (usuário logado)
    $paciente = User::where('role', 'paciente')->first();
    if (!$paciente) {
        echo "❌ Nenhum paciente encontrado!\n";
        exit;
    }

    // Pegar fisioterapeuta ID 4
    $fisioterapeuta = User::with('fisioterapeuta')
        ->where('id', 4)
        ->where('role', 'fisioterapeuta')
        ->first();

    if (!$fisioterapeuta) {
        echo "❌ Fisioterapeuta ID 4 não encontrado!\n";
        exit;
    }

    echo "✅ Usuários encontrados:\n";
    echo "   Paciente: {$paciente->name} (ID: {$paciente->id})\n";
    echo "   Fisioterapeuta: {$fisioterapeuta->name} (ID: {$fisioterapeuta->id})\n\n";

    // Simular dados de entrada
    $dataHora = Carbon::now()->addDays(1)->setTime(14, 0); // Amanhã às 14h
    $observacoes = "Sessão de teste para debug";

    echo "📝 Dados simulados:\n";
    echo "   Data/Hora: {$dataHora->format('d/m/Y H:i:s')}\n";
    echo "   Observações: {$observacoes}\n\n";

    // Testar validações uma por uma
    echo "🔍 Executando validações...\n";

    // 1. Validações iniciais (simples)
    echo "   ✅ Validação de campos presentes\n";

    // 2. Verificar se não é domingo
    if ($dataHora->dayOfWeek === 0) {
        echo "   ❌ Validação falhada: Não pode ser domingo\n";
    } else {
        echo "   ✅ Validação: Não é domingo\n";
    }

    // 3. Verificar horário comercial (8h às 18h)
    $hora = $dataHora->hour;
    if ($hora < 8 || $hora >= 18) {
        echo "   ❌ Validação falhada: Horário deve ser entre 8h e 18h (hora atual: {$hora}h)\n";
    } else {
        echo "   ✅ Validação: Horário comercial ({$hora}h)\n";
    }

    // 4. Verificar se o fisioterapeuta está disponível
    $fisioterapeutaValido = User::with('fisioterapeuta')
        ->where('id', $fisioterapeuta->id)
        ->whereHas('fisioterapeuta', function($q) {
            $q->where('available', true);
        })
        ->first();

    if (!$fisioterapeutaValido) {
        echo "   ❌ Validação falhada: Fisioterapeuta não disponível\n";
    } else {
        echo "   ✅ Validação: Fisioterapeuta disponível\n";
    }

    // 5. Verificar conflito de horário
    $conflito = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
        ->where('scheduled_at', $dataHora->format('Y-m-d H:i:s'))
        ->where('status', '!=', 'cancelado')
        ->exists();

    if ($conflito) {
        echo "   ❌ Validação falhada: Horário já ocupado\n";
    } else {
        echo "   ✅ Validação: Horário livre\n";
    }

    // 6. Verificar conflito paciente
    $conflitoPaciente = Agendamento::where('paciente_id', $paciente->id)
        ->where('scheduled_at', $dataHora->format('Y-m-d H:i:s'))
        ->where('status', '!=', 'cancelado')
        ->exists();

    if ($conflitoPaciente) {
        echo "   ❌ Validação falhada: Paciente já tem agendamento neste horário\n";
    } else {
        echo "   ✅ Validação: Paciente livre neste horário\n";
    }

    // 7. Verificar limite diário
    $inicioDia = $dataHora->copy()->startOfDay();
    $fimDia = $dataHora->copy()->endOfDay();
    $agendamentosDia = Agendamento::where('paciente_id', $paciente->id)
        ->whereBetween('scheduled_at', [$inicioDia, $fimDia])
        ->where('status', '!=', 'cancelado')
        ->count();

    if ($agendamentosDia >= 3) {
        echo "   ❌ Validação falhada: Limite de 3 agendamentos por dia atingido ({$agendamentosDia})\n";
    } else {
        echo "   ✅ Validação: Dentro do limite diário ({$agendamentosDia}/3)\n";
    }

    echo "\n🚀 Tentando criar agendamento e pagamento...\n";

    // Tentar criar de fato (usando a lógica do AgendamentoController)
    try {
        DB::beginTransaction();

        // Obter preço do fisioterapeuta
        $duration = 60;
        $price = $fisioterapeuta->fisioterapeuta->hourly_rate ?? 90.00;

        echo "   💰 Valor: R$ " . number_format($price, 2, ',', '.') . "\n";

        // 1) Criar agendamento
        $agendamento = Agendamento::create([
            'paciente_id' => $paciente->id,
            'fisioterapeuta_id' => $fisioterapeuta->id,
            'scheduled_at' => $dataHora,
            'duration' => $duration,
            'status' => 'pendente',
            'service_type' => 'Fisioterapia',
            'notes' => $observacoes,
            'address' => $paciente->address ?? 'Endereço não informado',
            'price' => $price,
        ]);

        echo "   📅 Agendamento criado (ID: {$agendamento->id})\n";

        // 2) Criar pagamento
        $pagamento = Pagamento::create([
            'assinatura_id' => null,
            'agendamento_id' => $agendamento->id,
            'amount' => $agendamento->price,
            'status' => 'pendente',
            'method' => null,
            'transaction_id' => null,
            'due_date' => Carbon::now()->toDateString(),
            'notes' => 'Pagamento de sessão (agendamento #' . $agendamento->id . ')',
            'user_id' => $paciente->id,
        ]);

        echo "   💳 Pagamento criado (ID: {$pagamento->id})\n";

        // 3) Criar preferência MP
        /** @var UnifiedPaymentService $paymentService */
        $paymentService = app(UnifiedPaymentService::class);

        $preference = $paymentService->createUnifiedPayment([
            'title' => 'Sessão de Fisioterapia',
            'description' => 'Pagamento do agendamento #' . $agendamento->id,
            'amount' => (float) $agendamento->price,
            'payer' => [
                'name' => $paciente->name,
                'email' => $paciente->email
            ],
            'payment_methods' => 'all',
            'success_url' => route('paciente.pagamentos.success'),
            'failure_url' => route('paciente.pagamentos.failure'),
            'pending_url' => route('paciente.pagamentos.pending'),
            'external_reference' => (string) $pagamento->id,
            'notification_url' => route('mercadopago.webhook'),
        ]);

        echo "   🔄 MercadoPago Preference criada: " . ($preference['success'] ?? 'FALHA') . "\n";

        if (!($preference['success'] ?? false)) {
            echo "   ❌ ERRO na criação da preferência: " . ($preference['message'] ?? 'Erro desconhecido') . "\n";
            DB::rollBack();

            // Mostrar detalhes do erro se disponível
            if (isset($preference['error'])) {
                echo "   📋 Detalhes do erro: {$preference['error']}\n";
            }
        } else {
            // Sucesso - persistir e commit
            $pagamento->update([
                'preference_id' => $preference['preference_id'] ?? null,
            ]);

            DB::commit();
            echo "   ✅ AGENDAMENTO CRIADO COM SUCESSO!\n";
            echo "   🔗 Checkout URL: " . ($preference['init_point'] ?? 'N/A') . "\n";
        }

    } catch (\Exception $e) {
        DB::rollBack();
        echo "   ❌ EXCEPTION durante criação: {$e->getMessage()}\n";
        echo "   📍 Arquivo: {$e->getFile()}:{$e->getLine()}\n";
    }

} catch (\Exception $e) {
    echo "❌ ERRO GERAL: {$e->getMessage()}\n";
    echo "📍 Trace: {$e->getTraceAsString()}\n";
}
