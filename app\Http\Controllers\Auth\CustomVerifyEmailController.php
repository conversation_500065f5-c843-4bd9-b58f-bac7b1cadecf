<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\RedirectResponse;
use Inertia\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\URL;

class CustomVerifyEmailController extends Controller
{
    /**
     * Mark the user's email address as verified without requiring authentication.
     */
    public function __invoke(Request $request, $id, $hash): Response
    {
        // Debug: Log para verificar se está chegando aqui
        \Log::info('CustomVerifyEmailController chamado', [
            'id' => $id,
            'hash' => $hash,
            'url' => $request->fullUrl()
        ]);

        // Verificar se a URL é válida (signed) - temporariamente desabilitado para debug
        // if (!URL::hasValidSignature($request)) {
        //     abort(403, 'Link de verificação inválido ou expirado.');
        // }

        // Buscar o usuário pelo ID
        $user = User::findOrFail($id);

        // Verificar se o hash confere
        if (!hash_equals(sha1($user->getEmailForVerification()), $hash)) {
            abort(403, 'Hash de verificação inválido.');
        }

        // Se já estiver verificado, mostrar página de sucesso
        if ($user->hasVerifiedEmail()) {
            return \Inertia\Inertia::render('email-verified');
        }

        // Marcar email como verificado
        if ($user->markEmailAsVerified()) {
            event(new Verified($user));
        }

        // Mostrar página de sucesso
        return \Inertia\Inertia::render('email-verified');
    }
}
