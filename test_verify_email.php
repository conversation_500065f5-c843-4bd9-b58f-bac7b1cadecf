<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Notifications\VerifyEmailFisioterapeuta;
use Illuminate\Support\Facades\Notification;

try {
    echo "=== Testando Email de Verificação com SSL Fix ===\n";
    
    // Buscar ou criar usuário fisioterapeuta para teste
    $email = '<EMAIL>';
    $user = User::firstOrCreate(
        ['email' => $email],
        [
            'name' => 'Teste Fisioterapeuta SSL',
            'password' => bcrypt('SenhaTemporaria123!'),
            'role' => 'fisioterapeuta',
            'email_verified_at' => null, // Garantir que não está verificado
        ]
    );

    // Resetar verificação se já estiver verificado
    if ($user->hasVerifiedEmail()) {
        $user->email_verified_at = null;
        $user->save();
        echo "✓ Email de verificação resetado para teste\n";
    }

    echo "✓ Usuário: {$user->name} ({$user->email})\n";
    echo "✓ Role: {$user->role}\n";
    echo "✓ Email verificado: " . ($user->hasVerifiedEmail() ? 'Sim' : 'Não') . "\n";
    
    // Enviar notificação de verificação
    echo "\n--- Enviando email de verificação ---\n";
    Notification::sendNow($user, new VerifyEmailFisioterapeuta());
    
    echo "✅ Email de verificação enviado com sucesso!\n";
    echo "✓ Destinatário: {$email}\n";
    echo "✓ Verifique sua caixa de entrada.\n";
    
    // Gerar URL de verificação para teste manual
    $verificationUrl = \Illuminate\Support\Facades\URL::temporarySignedRoute(
        'verification.verify',
        \Illuminate\Support\Carbon::now()->addMinutes(60),
        [
            'id' => $user->getKey(),
            'hash' => sha1($user->getEmailForVerification()),
        ]
    );
    
    echo "\n--- URL de Verificação (para teste manual) ---\n";
    echo $verificationUrl . "\n";
    
} catch (Exception $e) {
    echo "❌ Erro ao enviar email de verificação: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}