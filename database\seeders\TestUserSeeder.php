<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class TestUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Criar usuário de teste para o fluxo de onboarding
        $email = '<EMAIL>';

        // Deletar se já existir
        User::where('email', $email)->delete();

        $user = User::create([
            'name' => 'Novo Teste Paciente',
            'email' => $email,
            'password' => Hash::make('password123'),
            'role' => 'paciente',
            'active' => true,
            'has_subscription' => false,
            'onboarding_completed' => false,
            'plan_selected' => false,
            'checkout_completed' => false,
        ]);

        $this->command->info('Usuário de teste criado: ' . $user->email);
    }
}
