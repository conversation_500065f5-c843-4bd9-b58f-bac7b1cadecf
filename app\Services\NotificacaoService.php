<?php

namespace App\Services;

use App\Models\Agendamento;
use App\Models\Notificacao;
use App\Models\User;
use App\Jobs\EnviarEmailAgendamento;
use App\Mail\BoasVindasMail;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class NotificacaoService
{
    /**
     * Notificar fisioterapeuta sobre novo agendamento
     */
    public function notificarNovoAgendamento(Agendamento $agendamento)
    {
        // Criar notificação no banco
        $notificacao = Notificacao::criarNotificacao(
            $agendamento->fisioterapeuta_id,
            $agendamento->id,
            Notificacao::TIPO_NOVO_AGENDAMENTO,
            'Novo Agendamento',
            "Você tem um novo agendamento com {$agendamento->paciente->name} para " . 
            Carbon::parse($agendamento->scheduled_at)->format('d/m/Y H:i')
        );

        return $notificacao;
    }

    /**
     * Notificar paciente sobre agendamento confirmado
     */
    public function notificarAgendamentoConfirmado(Agendamento $agendamento)
    {
        // Criar notificação no banco
        $notificacao = Notificacao::criarNotificacao(
            $agendamento->paciente_id,
            $agendamento->id,
            Notificacao::TIPO_AGENDAMENTO_CONFIRMADO,
            'Agendamento Confirmado',
            "Seu agendamento foi confirmado para " .
            Carbon::parse($agendamento->scheduled_at)->format('d/m/Y H:i')
        );

        // Enviar email via Resend
        EnviarEmailAgendamento::dispatch($agendamento, 'confirmado', $agendamento->paciente_id);

        return $notificacao;
    }

    /**
     * Notificar sobre agendamento cancelado
     */
    public function notificarAgendamentoCancelado(Agendamento $agendamento, string $motivo = '')
    {
        // Notificar paciente
        $notificacaoPaciente = Notificacao::criarNotificacao(
            $agendamento->paciente_id,
            $agendamento->id,
            Notificacao::TIPO_AGENDAMENTO_CANCELADO,
            'Agendamento Cancelado',
            "Seu agendamento foi cancelado." . ($motivo ? " Motivo: {$motivo}" : '')
        );

        // Notificar fisioterapeuta
        $notificacaoFisio = Notificacao::criarNotificacao(
            $agendamento->fisioterapeuta_id,
            $agendamento->id,
            Notificacao::TIPO_AGENDAMENTO_CANCELADO,
            'Agendamento Cancelado',
            "O agendamento foi cancelado." . ($motivo ? " Motivo: {$motivo}" : '')
        );

        return [$notificacaoPaciente, $notificacaoFisio];
    }

    /**
     * Enviar lembretes de agendamento
     */
    public function enviarLembreteAgendamento(Agendamento $agendamento, int $userId)
    {
        // Criar notificação no banco
        $notificacao = Notificacao::criarNotificacao(
            $userId,
            $agendamento->id,
            Notificacao::TIPO_LEMBRETE_SESSAO,
            'Lembrete de Sessão',
            "Você tem uma sessão agendada para " . 
            Carbon::parse($agendamento->scheduled_at)->format('d/m/Y H:i')
        );

        return $notificacao;
    }

    /**
     * Notificar início de sessão
     */
    public function notificarSessaoIniciada(Agendamento $agendamento)
    {
        return Notificacao::criarNotificacao(
            $agendamento->paciente_id,
            $agendamento->id,
            Notificacao::TIPO_SESSAO_INICIADA,
            'Sessão Iniciada',
            'Sua sessão de fisioterapia foi iniciada.'
        );
    }

    /**
     * Notificar finalização de sessão
     */
    public function notificarSessaoFinalizada(Agendamento $agendamento)
    {
        return Notificacao::criarNotificacao(
            $agendamento->paciente_id,
            $agendamento->id,
            Notificacao::TIPO_SESSAO_FINALIZADA,
            'Sessão Finalizada',
            'Sua sessão de fisioterapia foi finalizada. Não se esqueça de avaliar!'
        );
    }

    /**
     * Notificar profissional a caminho
     */
    public function notificarProfissionalACaminho(Agendamento $agendamento)
    {
        return Notificacao::criarNotificacao(
            $agendamento->paciente_id,
            $agendamento->id,
            Notificacao::TIPO_PROFISSIONAL_A_CAMINHO,
            'Profissional a Caminho',
            'Seu profissional está a caminho para a sessão.'
        );
    }

    /**
     * Marcar todas as notificações como lidas para um usuário
     */
    public function marcarTodasComoLidas(int $userId)
    {
        return Notificacao::where('user_id', $userId)
            ->where('lida', false)
            ->update([
                'lida' => true,
                'data_leitura' => now(),
            ]);
    }

    /**
     * Obter notificações não lidas de um usuário
     */
    public function obterNotificacaoesNaoLidas(int $userId, int $limite = 10)
    {
        return Notificacao::where('user_id', $userId)
            ->naoLidas()
            ->with('agendamento')
            ->orderBy('created_at', 'desc')
            ->limit($limite)
            ->get();
    }

    /**
     * Contar notificações não lidas
     */
    public function contarNotificacaoesNaoLidas(int $userId): int
    {
        return Notificacao::where('user_id', $userId)
            ->naoLidas()
            ->count();
    }

    /**
     * Notificar reagendamento
     */
    public function notificarAgendamentoReagendado(Agendamento $agendamento, $dataHoraAntiga, $motivo = null)
    {
        $dataAntiga = Carbon::parse($dataHoraAntiga)->format('d/m/Y H:i');
        $dataNova = Carbon::parse($agendamento->scheduled_at)->format('d/m/Y H:i');

        $mensagemMotivo = $motivo ? " Motivo: {$motivo}" : "";

        // Notificar fisioterapeuta
        $notificacaoFisio = Notificacao::criarNotificacao(
            $agendamento->fisioterapeuta_id,
            $agendamento->id,
            Notificacao::TIPO_AGENDAMENTO_REAGENDADO,
            'Agendamento Reagendado',
            "O agendamento foi reagendado de {$dataAntiga} para {$dataNova}.{$mensagemMotivo}"
        );

        // Notificar paciente
        $notificacaoPaciente = Notificacao::criarNotificacao(
            $agendamento->paciente_id,
            $agendamento->id,
            Notificacao::TIPO_AGENDAMENTO_REAGENDADO,
            'Agendamento Reagendado',
            "Sua sessão foi reagendada de {$dataAntiga} para {$dataNova}.{$mensagemMotivo}"
        );

        return [$notificacaoFisio, $notificacaoPaciente];
    }

    /**
     * Enviar email de boas-vindas para novo usuário
     */
    public function enviarEmailBoasVindas(User $user)
    {
        try {
            // Verificar se já foi enviado recentemente (evitar duplicação)
            $cacheKey = "welcome_email_sent_{$user->id}";
            
            if (Cache::has($cacheKey)) {
                Log::info('Email de boas-vindas já foi enviado recentemente', [
                    'user_id' => $user->id,
                    'email' => $user->email
                ]);
                return true;
            }

            // Determinar tipo de usuário
            $tipoUsuario = 'paciente'; // padrão

            if ($user->isFisioterapeuta()) {
                $tipoUsuario = 'fisioterapeuta';
            } elseif ($user->isEmpresa()) {
                $tipoUsuario = 'empresa';
            }

            // Enviar email de boas-vindas
            Mail::to($user->email)->send(new BoasVindasMail(
                $user->name,
                $user->email,
                $tipoUsuario
            ));

            // Marcar como enviado por 10 minutos para evitar duplicação
            Cache::put($cacheKey, true, 600);

            Log::info('Email de boas-vindas enviado com sucesso', [
                'user_id' => $user->id,
                'email' => $user->email,
                'tipo_usuario' => $tipoUsuario
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Erro ao enviar email de boas-vindas', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }
}
