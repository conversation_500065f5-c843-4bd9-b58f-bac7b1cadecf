import GaleriaFotos from '@/components/galeria-fotos';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import PublicLayout from '@/layouts/public-layout';
import { Head, <PERSON> } from '@inertiajs/react';
import { Clock, ExternalLink, Facebook, Globe, Instagram, MapPin, MessageCircle, Phone, Star } from 'lucide-react';

interface ImagemEstabelecimento {
    id: number;
    nome_arquivo: string;
    nome_original: string;
    descricao?: string;
    principal: boolean;
    url: string;
}

interface Estabelecimento {
    id: number;
    nome: string;
    slug: string;
    categoria: string;
    descricao: string;
    telefone?: string;
    whatsapp: string;
    whatsapp_link: string;
    email?: string;
    endereco_completo: string;
    endereco: string;
    cidade: string;
    estado: string;
    cep: string;
    horario_funcionamento?: any;
    servicos_oferecidos?: string;
    site?: string;
    instagram?: string;
    facebook?: string;
    avaliacao_media: number;
    total_avaliacoes: number;
    latitude?: number;
    longitude?: number;
    imagens?: ImagemEstabelecimento[];
}

interface EstabelecimentoRelacionado {
    id: number;
    nome: string;
    slug: string;
    categoria: string;
    descricao: string;
    endereco_completo: string;
    avaliacao_media: number;
    total_avaliacoes: number;
}

interface Props {
    estabelecimento: Estabelecimento;
    relacionados: EstabelecimentoRelacionado[];
    structuredData: any;
    meta: {
        title: string;
        description: string;
        canonical: string;
        og_title: string;
        og_description: string;
        og_image: string;
        og_url: string;
    };
}

const categoriaLabels = {
    dentista: 'Dentista',
    farmacia: 'Farmácia',
    fisioterapia: 'Fisioterapia',
};

const categoriaEmojis = {
    dentista: '🦷',
    farmacia: '💊',
    fisioterapia: '💪',
};

export default function EstabelecimentoShow({ estabelecimento, relacionados, structuredData, meta }: Props) {
    const formatarHorario = (horario: any) => {
        if (!horario) return 'Horário não informado';

        const dias = {
            segunda: 'Segunda',
            terca: 'Terça',
            quarta: 'Quarta',
            quinta: 'Quinta',
            sexta: 'Sexta',
            sabado: 'Sábado',
            domingo: 'Domingo',
        };

        return Object.entries(horario).map(([dia, info]: [string, any]) => (
            <div key={dia} className="flex justify-between">
                <span>{dias[dia as keyof typeof dias]}:</span>
                <span>{info?.abertura && info?.fechamento ? `${info.abertura} - ${info.fechamento}` : 'Fechado'}</span>
            </div>
        ));
    };

    return (
        <PublicLayout title={meta.title} description={meta.description}>
            <Head>
                <link rel="canonical" href={meta.canonical} />
                <meta property="og:title" content={meta.og_title} />
                <meta property="og:description" content={meta.og_description} />
                <meta property="og:image" content={meta.og_image} />
                <meta property="og:url" content={meta.og_url} />
                <meta property="og:type" content="business.business" />
                <meta name="twitter:card" content="summary_large_image" />
                <meta name="twitter:title" content={meta.og_title} />
                <meta name="twitter:description" content={meta.og_description} />
                <meta name="twitter:image" content={meta.og_image} />
                <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }} />
            </Head>

            {/* Breadcrumbs */}
            <div className="bg-muted/30 py-4">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <Link href="/" className="hover:text-foreground">
                            Início
                        </Link>
                        <span>/</span>
                        <Link href="/buscar" className="hover:text-foreground">
                            Buscar Profissionais
                        </Link>
                        <span>/</span>
                        <span className="text-foreground">{estabelecimento.nome}</span>
                    </nav>
                </div>
            </div>

            <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
                <div className="grid gap-8 lg:grid-cols-3">
                    {/* Conteúdo Principal */}
                    <div className="lg:col-span-2">
                        {/* Header do Estabelecimento */}
                        <div className="mb-8">
                            <div className="flex items-start gap-4">
                                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 text-3xl">
                                    {categoriaEmojis[estabelecimento.categoria as keyof typeof categoriaEmojis]}
                                </div>
                                <div className="flex-1">
                                    <h1 className="text-3xl font-bold text-foreground">{estabelecimento.nome}</h1>
                                    <div className="mt-2 flex items-center gap-4">
                                        <Badge variant="secondary">
                                            {categoriaLabels[estabelecimento.categoria as keyof typeof categoriaLabels]}
                                        </Badge>
                                        {estabelecimento.avaliacao_media > 0 && (
                                            <div className="flex items-center gap-1">
                                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                                <span className="font-medium">{estabelecimento.avaliacao_media}</span>
                                                <span className="text-muted-foreground">({estabelecimento.total_avaliacoes} avaliações)</span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Descrição */}
                        {estabelecimento.descricao && (
                            <Card className="mb-8">
                                <CardHeader>
                                    <CardTitle>Sobre</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground">{estabelecimento.descricao}</p>
                                </CardContent>
                            </Card>
                        )}

                        {/* Galeria de Fotos */}
                        {estabelecimento.imagens && estabelecimento.imagens.length > 0 && (
                            <Card className="mb-8">
                                <CardHeader>
                                    <CardTitle>Galeria de Fotos</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <GaleriaFotos imagens={estabelecimento.imagens} nomeEstabelecimento={estabelecimento.nome} autoPlay={false} />
                                </CardContent>
                            </Card>
                        )}

                        {/* Serviços Oferecidos */}
                        {estabelecimento.servicos_oferecidos && (
                            <Card className="mb-8">
                                <CardHeader>
                                    <CardTitle>Serviços Oferecidos</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground">{estabelecimento.servicos_oferecidos}</p>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Contato */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Contato</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-3">
                                    <Button asChild className="w-full" size="lg">
                                        <a href={estabelecimento.whatsapp_link} target="_blank" rel="noopener noreferrer">
                                            <MessageCircle className="mr-2 h-4 w-4" />
                                            WhatsApp
                                        </a>
                                    </Button>

                                    {estabelecimento.telefone && (
                                        <Button asChild variant="outline" className="w-full">
                                            <a href={`tel:${estabelecimento.telefone}`}>
                                                <Phone className="mr-2 h-4 w-4" />
                                                {estabelecimento.telefone}
                                            </a>
                                        </Button>
                                    )}
                                </div>

                                {/* Redes Sociais */}
                                <div className="flex gap-2">
                                    {estabelecimento.site && (
                                        <Button asChild variant="outline" size="icon">
                                            <a href={estabelecimento.site} target="_blank" rel="noopener noreferrer">
                                                <Globe className="h-4 w-4" />
                                            </a>
                                        </Button>
                                    )}
                                    {estabelecimento.instagram && (
                                        <Button asChild variant="outline" size="icon">
                                            <a href={`https://instagram.com/${estabelecimento.instagram}`} target="_blank" rel="noopener noreferrer">
                                                <Instagram className="h-4 w-4" />
                                            </a>
                                        </Button>
                                    )}
                                    {estabelecimento.facebook && (
                                        <Button asChild variant="outline" size="icon">
                                            <a href={`https://facebook.com/${estabelecimento.facebook}`} target="_blank" rel="noopener noreferrer">
                                                <Facebook className="h-4 w-4" />
                                            </a>
                                        </Button>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Localização */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <MapPin className="h-4 w-4" />
                                    Localização
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm text-muted-foreground">{estabelecimento.endereco_completo}</p>
                                {estabelecimento.latitude && estabelecimento.longitude && (
                                    <Button asChild variant="outline" className="mt-3 w-full" size="sm">
                                        <a
                                            href={`https://maps.google.com/?q=${estabelecimento.latitude},${estabelecimento.longitude}`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            <ExternalLink className="mr-2 h-3 w-3" />
                                            Ver no Google Maps
                                        </a>
                                    </Button>
                                )}
                            </CardContent>
                        </Card>

                        {/* Horário de Funcionamento */}
                        {estabelecimento.horario_funcionamento && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Clock className="h-4 w-4" />
                                        Horário de Funcionamento
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-1 text-sm">{formatarHorario(estabelecimento.horario_funcionamento)}</div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>

                {/* Estabelecimentos Relacionados */}
                {relacionados.length > 0 && (
                    <div className="mt-12">
                        <h2 className="mb-6 text-2xl font-bold">
                            Outros {categoriaLabels[estabelecimento.categoria as keyof typeof categoriaLabels]}s em {estabelecimento.cidade}
                        </h2>
                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                            {relacionados.map((item) => (
                                <Card key={item.id} className="transition-shadow hover:shadow-md">
                                    <CardContent className="p-6">
                                        <div className="flex items-start gap-3">
                                            <div className="text-2xl">{categoriaEmojis[item.categoria as keyof typeof categoriaEmojis]}</div>
                                            <div className="flex-1">
                                                <h3 className="font-semibold">
                                                    <Link href={`/estabelecimento/${item.slug}`} className="hover:text-primary">
                                                        {item.nome}
                                                    </Link>
                                                </h3>
                                                <p className="mt-1 line-clamp-2 text-sm text-muted-foreground">{item.descricao}</p>
                                                <p className="mt-2 text-xs text-muted-foreground">{item.endereco_completo}</p>
                                                {item.avaliacao_media > 0 && (
                                                    <div className="mt-2 flex items-center gap-1">
                                                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                                        <span className="text-xs font-medium">{item.avaliacao_media}</span>
                                                        <span className="text-xs text-muted-foreground">({item.total_avaliacoes})</span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </PublicLayout>
    );
}
