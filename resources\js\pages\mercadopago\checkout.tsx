import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { CreditCard, Lock, Shield, ArrowLeft, CheckCircle } from 'lucide-react';
import { usePage } from '@inertiajs/react';

interface PlanoInfo {
    nome: string;
    preco: number;
    periodo: string;
    descricao: string;
}

export default function MercadoPagoCheckout() {
    const { url } = usePage();
    const [cardData, setCardData] = useState({
        number: '',
        holder: 'TESTUSER9211',
        expiry: '11/30',
        cvv: '123'
    });
    const [isProcessing, setIsProcessing] = useState(false);
    const [paymentMethod, setPaymentMethod] = useState<'credit' | 'debit'>('credit');

    // Extrair parâmetros da URL
    const urlParams = new URLSearchParams(url.split('?')[1] || '');
    
    // Simular dados do plano vindos da URL
    const planoInfo: PlanoInfo = {
        nome: urlParams.get('plano') || 'Plano Pessoal',
        preco: parseFloat(urlParams.get('preco') || '180.00'),
        periodo: urlParams.get('periodo') || 'mês',
        descricao: urlParams.get('descricao') || 'Atendimento fisioterapêutico domiciliar personalizado'
    };

    const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        let value = e.target.value.replace(/\s/g, '');
        if (value.length > 16) value = value.slice(0, 16);
        
        // Formatar número do cartão
        const formatted = value.replace(/(\d{4})/g, '$1 ').trim();
        setCardData(prev => ({ ...prev, number: formatted }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsProcessing(true);

        // Simular processamento do pagamento
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Gerar ID de transação único
        const transactionId = 'MP' + Math.random().toString(36).substr(2, 9).toUpperCase();
        
        // Redirecionar para página de sucesso com dados da transação
        const params = new URLSearchParams({
            status: 'approved',
            transaction_id: transactionId,
            external_reference: `plano_${planoInfo.nome.toLowerCase().replace(/\s+/g, '_')}`,
            payment_method: paymentMethod === 'credit' ? 'credit_card' : 'debit_card',
            card_last_four: cardData.number.slice(-4).replace(/\s/g, ''),
            amount: planoInfo.preco.toString(),
            currency: 'BRL',
            installments: '1',
            plan_name: planoInfo.nome,
            plan_price: planoInfo.preco.toString(),
            plan_period: planoInfo.periodo
        });

        window.location.href = `/success?${params.toString()}`;
    };

    const suggestedCards = [
        { type: 'Mastercard', number: '5031 4332 1540 6351', cvv: '123' },
        { type: 'Visa', number: '4235 6477 2802 5682', cvv: '123' }
    ];

    const fillCardData = (card: typeof suggestedCards[0]) => {
        setCardData(prev => ({
            ...prev,
            number: card.number,
            cvv: card.cvv
        }));
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
            <div className="mx-auto max-w-4xl px-4">
                {/* Header */}
                <div className="mb-8 text-center">
                    <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                        <CreditCard className="h-8 w-8 text-blue-600" />
                    </div>
                    <h1 className="text-3xl font-bold text-gray-900">Checkout Seguro</h1>
                    <p className="text-lg text-gray-600">Mercado Pago - Pagamento Seguro</p>
                </div>

                <div className="grid gap-8 lg:grid-cols-3">
                    {/* Formulário de Pagamento */}
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Lock className="h-5 w-5 text-green-600" />
                                    Dados do Cartão
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    {/* Número do Cartão */}
                                    <div>
                                        <Label htmlFor="cardNumber">Número do Cartão</Label>
                                        <Input
                                            id="cardNumber"
                                            value={cardData.number}
                                            onChange={handleCardNumberChange}
                                            placeholder="0000 0000 0000 0000"
                                            className="font-mono text-lg"
                                            required
                                        />
                                    </div>

                                    {/* Nome do Titular */}
                                    <div>
                                        <Label htmlFor="cardHolder">Nome do Titular</Label>
                                        <Input
                                            id="cardHolder"
                                            value={cardData.holder}
                                            onChange={(e) => setCardData(prev => ({ ...prev, holder: e.target.value }))}
                                            placeholder="Nome como está no cartão"
                                            required
                                        />
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        {/* Data de Vencimento */}
                                        <div>
                                            <Label htmlFor="expiry">Data de Vencimento</Label>
                                            <Input
                                                id="expiry"
                                                value={cardData.expiry}
                                                onChange={(e) => setCardData(prev => ({ ...prev, expiry: e.target.value }))}
                                                placeholder="MM/AA"
                                                required
                                            />
                                        </div>

                                        {/* CVV */}
                                        <div>
                                            <Label htmlFor="cvv">CVV</Label>
                                            <Input
                                                id="cvv"
                                                value={cardData.cvv}
                                                onChange={(e) => setCardData(prev => ({ ...prev, cvv: e.target.value }))}
                                                placeholder="123"
                                                maxLength={4}
                                                required
                                            />
                                        </div>
                                    </div>

                                    {/* Tipo de Cartão */}
                                    <div>
                                        <Label>Tipo de Cartão</Label>
                                        <div className="mt-2 flex gap-4">
                                            <label className="flex items-center gap-2">
                                                <input
                                                    type="radio"
                                                    name="cardType"
                                                    value="credit"
                                                    checked={paymentMethod === 'credit'}
                                                    onChange={() => setPaymentMethod('credit')}
                                                />
                                                <span>Crédito</span>
                                            </label>
                                            <label className="flex items-center gap-2">
                                                <input
                                                    type="radio"
                                                    name="cardType"
                                                    value="debit"
                                                    checked={paymentMethod === 'debit'}
                                                    onChange={() => setPaymentMethod('debit')}
                                                />
                                                <span>Débito</span>
                                            </label>
                                        </div>
                                    </div>

                                    {/* Botão de Pagamento */}
                                    <Button
                                        type="submit"
                                        className="w-full bg-blue-600 hover:bg-blue-700"
                                        size="lg"
                                        disabled={isProcessing || !cardData.number || !cardData.holder || !cardData.expiry || !cardData.cvv}
                                    >
                                        {isProcessing ? (
                                            <div className="flex items-center gap-2">
                                                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                                Processando...
                                            </div>
                                        ) : (
                                            <div className="flex items-center gap-2">
                                                <Shield className="h-4 w-4" />
                                                Pagar R$ {planoInfo.preco.toFixed(2).replace('.', ',')}
                                            </div>
                                        )}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Resumo do Pedido */}
                    <div>
                        <Card>
                            <CardHeader>
                                <CardTitle>Resumo do Pedido</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center gap-3">
                                    <div className="h-12 w-12 rounded-full bg-green-100 p-2">
                                        <CreditCard className="h-8 w-8 text-green-600" />
                                    </div>
                                    <div>
                                        <h3 className="font-semibold">{planoInfo.nome}</h3>
                                        <p className="text-sm text-gray-600">{planoInfo.descricao}</p>
                                    </div>
                                </div>

                                <div className="border-t pt-4">
                                    <div className="flex justify-between">
                                        <span>Valor do Plano:</span>
                                        <span className="font-semibold">R$ {planoInfo.preco.toFixed(2).replace('.', ',')}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Período:</span>
                                        <span>{planoInfo.periodo}</span>
                                    </div>
                                </div>

                                <div className="border-t pt-4">
                                    <div className="flex justify-between text-lg font-bold">
                                        <span>Total:</span>
                                        <span>R$ {planoInfo.preco.toFixed(2).replace('.', ',')}</span>
                                    </div>
                                </div>

                                {/* Cartões de Teste */}
                                <div className="mt-6 rounded-lg bg-yellow-50 p-4">
                                    <h4 className="mb-3 font-semibold text-yellow-800">🧪 Cartões de Teste</h4>
                                    <div className="space-y-2">
                                        {suggestedCards.map((card, index) => (
                                            <button
                                                key={index}
                                                onClick={() => fillCardData(card)}
                                                className="w-full rounded border border-yellow-200 bg-white p-2 text-left text-sm hover:bg-yellow-100"
                                            >
                                                <div className="font-medium">{card.type}</div>
                                                <div className="font-mono text-xs text-gray-600">{card.number}</div>
                                            </button>
                                        ))}
                                    </div>
                                    <p className="mt-2 text-xs text-yellow-700">
                                        Clique em um cartão para preencher automaticamente
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Informações de Segurança */}
                        <Card className="mt-4">
                            <CardContent className="pt-6">
                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                    <Shield className="h-4 w-4" />
                                    <span>Pagamento 100% seguro</span>
                                </div>
                                <div className="mt-2 flex items-center gap-2 text-sm text-gray-600">
                                    <Lock className="h-4 w-4" />
                                    <span>Criptografia SSL</span>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Botão Voltar */}
                <div className="mt-8 text-center">
                    <Button
                        variant="outline"
                        onClick={() => window.history.back()}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Voltar
                    </Button>
                </div>
            </div>
        </div>
    );
}
