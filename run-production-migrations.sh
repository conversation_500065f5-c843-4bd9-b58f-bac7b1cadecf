#!/bin/bash

# Script para executar migrações para produção
# Este script carrega as variáveis do .env.production e executa as migrações

echo "🚀 Iniciando migrações para produção..."

# Verifica se o arquivo .env.production existe
if [ ! -f ".env.production" ]; then
    echo "❌ Erro: Arquivo .env.production não encontrado!"
    exit 1
fi

# Faz backup do .env atual se existir
if [ -f ".env" ]; then
    echo "📦 Fazendo backup do .env atual..."
    cp .env .env.backup
fi

# Copia o .env.production para .env
echo "📋 Carregando configurações de produção..."
cp .env.production .env

# Executa as migrações
echo "🔄 Executando migrações do banco de dados..."
php artisan migrate --force

# Verifica se as migrações foram executadas com sucesso
if [ $? -eq 0 ]; then
    echo "✅ Migrações executadas com sucesso!"
    
    # Opcional: Executar seeder de produção se existir
    if [ -f "database/seeders/ProductionDatabaseSeeder.php" ]; then
        echo "🌱 Executando seeders de produção..."
        php artisan db:seed --class=ProductionDatabaseSeeder --force
    fi
    
    # Opcional: Otimizar o Laravel após migrações
    echo "⚡ Otimizando aplicação..."
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    php artisan cache:clear
    
    echo "🎉 Processo de migração para produção concluído com sucesso!"
else
    echo "❌ Erro ao executar migrações!"
    
    # Restaura o .env original em caso de erro
    if [ -f ".env.backup" ]; then
        echo "🔄 Restaurando .env original..."
        cp .env.backup .env
        rm .env.backup
    fi
    
    exit 1
fi

# Pergunta se deseja restaurar o .env original
echo ""
read -p "Deseja restaurar o .env original? (s/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Ss]$ ]]; then
    if [ -f ".env.backup" ]; then
        echo "🔄 Restaurando .env original..."
        cp .env.backup .env
        rm .env.backup
        echo "✅ .env original restaurado!"
    else
        echo "ℹ️  Nenhum backup do .env encontrado para restaurar."
    fi
else
    echo "ℹ️  Mantendo .env de produção ativo."
    rm -f .env.backup
fi

echo "👋 Script finalizado!"
