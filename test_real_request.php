<?php

require_once 'vendor/autoload.php';

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

try {
    // Inicializar <PERSON>
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    echo "=== Simulando acesso real à rota ==\n\n";

    // Simular o request para a rota específica
    $request = Request::create(
        'http://127.0.0.1:8000/paciente/agendamentos/create?fisioterapeuta=4',
        'GET'
    );

    // Tentar resolver a rota e o controller
    try {
        $router = app('router');
        $routes = $router->getRoutes();

        // Procurar rota correspondente
        foreach ($routes as $route) {
            if ($route->matches($request)) {
                echo "✅ Rota encontrada!\n";
                echo "   URI: " . $route->uri() . "\n";
                echo "   Método: " . $route->methods()[0] . "\n";
                echo "   Nome: " . ($route->getName() ?? 'sem nome') . "\n";
                echo "   Controller: " . ($route->getAction('controller') ?? 'sem controller') . "\n";
                echo "   Middlewares: " . implode(', ', $route->gatherMiddleware()) . "\n";

                // Extra: tentar executar a rota
                try {
                    $response = $router->dispatch($request);
                    echo "   Status Response: " . $response->getStatusCode() . "\n";

                    // Mostrar conteúdo da resposta se for erro
                    if ($response->getStatusCode() >= 400) {
                        $content = $response->getContent();
                        echo "   Erro Content: " . substr($content, 0, 200) . "...\n";
                    }

                } catch (\Exception $e) {
                    echo "❌ Erro ao executar rota: " . $e->getMessage() . "\n";
                    echo "   Trace: " . substr($e->getTraceAsString(), 0, 500) . "...\n";
                }

                break;
            }
        }

    } catch (\Exception $e) {
        echo "❌ Erro ao encontrar rota: " . $e->getMessage() . "\n";
    }

} catch (\Exception $e) {
    echo "ERRO GERAL: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
