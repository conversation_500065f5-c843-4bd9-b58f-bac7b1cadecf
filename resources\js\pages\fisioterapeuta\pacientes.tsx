import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Calendar, Clock, Eye, Filter, MapPin, MessageCircle, Phone, Search, User } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/fisioterapeuta/dashboard',
    },
    {
        title: 'Pacientes',
        href: '/fisioterapeuta/pacientes',
    },
];

interface Paciente {
    id: number;
    name: string;
    email: string;
    phone?: string;
    avatar?: string;
    birth_date?: string;
    gender?: string;
    address?: string;
    medical_history?: string;
    emergency_contact?: string;
    created_at: string;
    stats: {
        total_sessions: number;
        completed_sessions: number;
        cancelled_sessions: number;
        last_session?: string;
        next_session?: string;
        total_spent: number;
    };
    assinatura?: {
        id: number;
        status: string;
        plano: {
            name: string;
        };
    };
}

interface Props {
    pacientes: {
        data: Paciente[];
        links: any[];
        meta: any;
    };
    filters: {
        search?: string;
        status?: string;
        has_subscription?: string;
    };
    stats: {
        total: number;
        ativos: number;
        com_assinatura: number;
        sessoes_mes: number;
    };
}

export default function FisioterapeutaPacientes({ pacientes, filters, stats }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters?.search || '');
    const [selectedStatus, setSelectedStatus] = useState(filters?.status || '');
    const [hasSubscription, setHasSubscription] = useState(filters?.has_subscription || '');

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR');
    };

    const getAge = (birthDate: string) => {
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }

        return age;
    };

    const handleSearch = () => {
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (selectedStatus && selectedStatus !== 'todos') params.append('status', selectedStatus);
        if (hasSubscription && hasSubscription !== 'todos') params.append('has_subscription', hasSubscription);

        window.location.href = `/fisioterapeuta/pacientes?${params.toString()}`;
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedStatus('todos');
        setHasSubscription('todos');
        window.location.href = '/fisioterapeuta/pacientes';
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Pacientes" />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold">Pacientes</h1>
                        <p className="text-muted-foreground">Gerencie seus pacientes e histórico de atendimentos</p>
                    </div>
                </div>

                {/* Estatísticas */}
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total de Pacientes</CardTitle>
                            <User className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total}</div>
                            <p className="text-xs text-muted-foreground">pacientes cadastrados</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pacientes Ativos</CardTitle>
                            <User className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.ativos}</div>
                            <p className="text-xs text-muted-foreground">com sessões recentes</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Com Assinatura</CardTitle>
                            <Badge className="h-4 w-4" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.com_assinatura}</div>
                            <p className="text-xs text-muted-foreground">planos ativos</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Sessões este Mês</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.sessoes_mes}</div>
                            <p className="text-xs text-muted-foreground">sessões realizadas</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Filtros */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filtros
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-4">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Buscar paciente</label>
                                <div className="relative">
                                    <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                                    <Input
                                        placeholder="Nome ou email..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Status</label>
                                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Todos os status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="todos">Todos os status</SelectItem>
                                        <SelectItem value="ativo">Ativo</SelectItem>
                                        <SelectItem value="inativo">Inativo</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Assinatura</label>
                                <Select value={hasSubscription} onValueChange={setHasSubscription}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Todos" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="todos">Todos</SelectItem>
                                        <SelectItem value="sim">Com assinatura</SelectItem>
                                        <SelectItem value="nao">Sem assinatura</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex items-end gap-2">
                                <Button onClick={handleSearch} className="flex-1">
                                    Filtrar
                                </Button>
                                <Button variant="outline" onClick={clearFilters}>
                                    Limpar
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Lista de Pacientes */}
                {pacientes?.data?.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <User className="h-12 w-12 text-muted-foreground" />
                            <h3 className="mt-4 text-lg font-semibold">Nenhum paciente encontrado</h3>
                            <p className="text-muted-foreground">
                                {filters?.search || filters?.status || filters?.has_subscription
                                    ? 'Tente ajustar os filtros para encontrar pacientes.'
                                    : 'Você ainda não possui pacientes. Eles aparecerão aqui quando agendarem consultas.'}
                            </p>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="space-y-4">
                        {pacientes?.data?.map((paciente) => (
                            <Card key={paciente.id} className="transition-shadow hover:shadow-md">
                                <CardContent className="p-6">
                                    <div className="flex flex-col gap-4 lg:flex-row lg:items-start lg:justify-between">
                                        <div className="flex-1 space-y-3">
                                            <div className="flex items-start justify-between">
                                                <div className="flex items-center gap-3">
                                                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                                                        {paciente.avatar ? (
                                                            <img
                                                                src={paciente.avatar}
                                                                alt={paciente.name}
                                                                className="h-12 w-12 rounded-full object-cover"
                                                            />
                                                        ) : (
                                                            <User className="h-6 w-6 text-primary" />
                                                        )}
                                                    </div>
                                                    <div>
                                                        <h3 className="font-semibold">{paciente.name}</h3>
                                                        <p className="text-sm text-muted-foreground">{paciente.email}</p>
                                                        {paciente.birth_date && (
                                                            <p className="text-sm text-muted-foreground">
                                                                {getAge(paciente.birth_date)} anos
                                                                {paciente.gender && ` • ${paciente.gender}`}
                                                            </p>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="flex gap-2">
                                                    {paciente.assinatura && <Badge variant="default">{paciente.assinatura.plano.name}</Badge>}
                                                    <Badge variant={paciente.stats?.total_sessions > 0 ? 'default' : 'secondary'}>
                                                        {paciente.stats?.total_sessions > 0 ? 'Ativo' : 'Novo'}
                                                    </Badge>
                                                </div>
                                            </div>

                                            <div className="grid gap-2 sm:grid-cols-2 lg:grid-cols-4">
                                                <div className="flex items-center gap-2 text-sm">
                                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                                    <span>{paciente.stats?.total_sessions || 0} sessões</span>
                                                </div>
                                                {paciente.phone && (
                                                    <div className="flex items-center gap-2 text-sm">
                                                        <Phone className="h-4 w-4 text-muted-foreground" />
                                                        <span>{paciente.phone}</span>
                                                    </div>
                                                )}
                                                {paciente.stats?.last_session && (
                                                    <div className="flex items-center gap-2 text-sm">
                                                        <Clock className="h-4 w-4 text-muted-foreground" />
                                                        <span>Última: {formatDate(paciente.stats.last_session)}</span>
                                                    </div>
                                                )}
                                                {paciente.address && (
                                                    <div className="flex items-center gap-2 text-sm">
                                                        <MapPin className="h-4 w-4 text-muted-foreground" />
                                                        <span className="truncate">{paciente.address}</span>
                                                    </div>
                                                )}
                                            </div>

                                            {paciente.stats?.next_session && (
                                                <Alert>
                                                    <Calendar className="h-4 w-4" />
                                                    <AlertDescription>Próxima sessão: {formatDate(paciente.stats.next_session)}</AlertDescription>
                                                </Alert>
                                            )}
                                        </div>

                                        <div className="flex flex-col gap-2 lg:items-end">
                                            <div className="text-lg font-semibold">{formatCurrency(paciente.stats?.total_spent || 0)}</div>
                                            <p className="text-sm text-muted-foreground">total gasto</p>
                                            <div className="flex gap-2">
                                                <Button asChild size="sm" variant="outline">
                                                    <Link href={`/fisioterapeuta/pacientes/${paciente.id}`}>
                                                        <Eye className="mr-2 h-4 w-4" />
                                                        Ver Perfil
                                                    </Link>
                                                </Button>
                                                {paciente.phone && (
                                                    <Button asChild size="sm" variant="outline">
                                                        <a
                                                            href={`https://wa.me/55${paciente.phone.replace(/\D/g, '')}`}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                        >
                                                            <MessageCircle className="mr-2 h-4 w-4" />
                                                            WhatsApp
                                                        </a>
                                                    </Button>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}

                {/* Paginação */}
                {pacientes?.links && pacientes.links.length > 3 && (
                    <div className="flex justify-center">
                        <div className="flex gap-2">
                            {pacientes.links.map((link: any, index: number) => (
                                <Button key={index} variant={link.active ? 'default' : 'outline'} size="sm" asChild={!!link.url} disabled={!link.url}>
                                    {link.url ? (
                                        <Link href={link.url} dangerouslySetInnerHTML={{ __html: link.label }} />
                                    ) : (
                                        <span dangerouslySetInnerHTML={{ __html: link.label }} />
                                    )}
                                </Button>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
