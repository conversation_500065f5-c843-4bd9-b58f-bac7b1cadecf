import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useInitials } from '@/hooks/use-initials';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import React from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { Calendar, MessageCircle, Star, ThumbsUp, Filter, Search } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/paciente/dashboard',
    },
    {
        title: '<PERSON><PERSON>',
        href: '/paciente/avaliacoes',
    },
];

interface Avaliacao {
    id: number;
    nota_geral: number;
    nota_pontualidade?: number;
    nota_profissionalismo?: number;
    nota_eficacia?: number;
    comentario?: string;
    recomendaria: boolean;
    aprovada: boolean;
    created_at: string;
    fisioterapeuta: {
        id: number;
        name: string;
        avatar?: string;
    };
    agendamento: {
        id: number;
        scheduled_at: string;
    };
}

interface Props {
    avaliacoes: {
        data: Avaliacao[];
        links: any[];
        meta: any;
    };
}

export default function PacienteAvaliacoes({ avaliacoes }: Props) {
    const getInitials = useInitials();
    const [search, setSearch] = React.useState<string>('');
    const [status, setStatus] = React.useState<string>('');
    const [notaMinima, setNotaMinima] = React.useState<string>('');

    const onSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(
            route('paciente.avaliacoes.index'),
            {
                search: search || undefined,
                status: status || undefined,
                nota_minima: notaMinima || undefined,
            },
            { preserveState: true, replace: true },
        );
    };

    const renderStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, index) => (
            <Star
                key={index}
                className={`h-4 w-4 ${
                    index < rating
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300'
                }`}
            />
        ));
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
        });
    };

    const getStatusBadge = (aprovada: boolean) => {
        if (aprovada) {
            return <Badge variant="default" className="bg-green-100 text-green-800">Aprovada</Badge>;
        }
        return <Badge variant="secondary">Pendente</Badge>;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Minhas Avaliações" />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold">Minhas Avaliações</h1>
                        <p className="text-muted-foreground">
                            Veja todas as avaliações que você fez para fisioterapeutas
                        </p>
                    </div>
                </div>

                {/* Busca e Filtros (padrão Agendamentos) */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <Filter className="mr-2 h-5 w-5" />
                            Buscar e Filtrar
                        </CardTitle>
                        <CardDescription>Refine a lista de avaliações</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={onSubmit} className="grid grid-cols-1 gap-4 sm:grid-cols-4">
                            <div className="sm:col-span-2">
                                <label className="mb-1 block text-sm font-medium">Busca</label>
                                <Input
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    placeholder="Buscar por fisioterapeuta ou comentário"
                                />
                            </div>
                            <div>
                                <label className="mb-1 block text-sm font-medium">Status</label>
                                <Select value={status || '#'} onValueChange={(v) => setStatus(v === '#' ? '' : v)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Todos" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="#">Todos</SelectItem>
                                        <SelectItem value="aprovadas">Aprovadas</SelectItem>
                                        <SelectItem value="pendentes">Pendentes</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <label className="mb-1 block text-sm font-medium">Nota mínima</label>
                                <Select value={notaMinima || '#'} onValueChange={(v) => setNotaMinima(v === '#' ? '' : v)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Qualquer" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="#">Qualquer</SelectItem>
                                        <SelectItem value="1">1+</SelectItem>
                                        <SelectItem value="2">2+</SelectItem>
                                        <SelectItem value="3">3+</SelectItem>
                                        <SelectItem value="4">4+</SelectItem>
                                        <SelectItem value="5">5</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="sm:col-span-4 flex justify-end">
                                <Button type="submit" className="bg-green-500 text-black hover:bg-green-600">
                                    <Search className="mr-2 h-4 w-4" />
                                    Pesquisar
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Lista de Avaliações */}
                {avaliacoes.data.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <MessageCircle className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">Nenhuma avaliação encontrada</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                Você ainda não fez nenhuma avaliação. Após suas consultas, você poderá avaliar os fisioterapeutas.
                            </p>
                            <Button asChild>
                                <Link href={route('paciente.agendamentos.index')}>
                                    Ver Agendamentos
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="space-y-4">
                        {avaliacoes.data.map((avaliacao) => (
                            <Card key={avaliacao.id}>
                                <CardHeader>
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center gap-3">
                                            <Avatar className="h-12 w-12">
                                                <AvatarImage src={avaliacao.fisioterapeuta.avatar} />
                                                <AvatarFallback>
                                                    {getInitials(avaliacao.fisioterapeuta.name)}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <CardTitle className="text-lg">
                                                    {avaliacao.fisioterapeuta.name}
                                                </CardTitle>
                                                <CardDescription className="flex items-center gap-2">
                                                    <Calendar className="h-4 w-4" />
                                                    Consulta em {formatDate(avaliacao.agendamento.scheduled_at)}
                                                </CardDescription>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {getStatusBadge(avaliacao.aprovada)}
                                            <div className="text-sm text-muted-foreground">
                                                {formatDate(avaliacao.created_at)}
                                            </div>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {/* Nota Geral */}
                                    <div className="flex items-center gap-2">
                                        <span className="font-medium">Nota Geral:</span>
                                        <div className="flex items-center gap-1">
                                            {renderStars(avaliacao.nota_geral)}
                                            <span className="ml-2 text-sm text-muted-foreground">
                                                ({avaliacao.nota_geral}/5)
                                            </span>
                                        </div>
                                    </div>

                                    {/* Notas Específicas */}
                                    {(avaliacao.nota_pontualidade || avaliacao.nota_profissionalismo || avaliacao.nota_eficacia) && (
                                        <div className="grid gap-2 sm:grid-cols-3">
                                            {avaliacao.nota_pontualidade && (
                                                <div className="text-sm">
                                                    <span className="font-medium">Pontualidade:</span>
                                                    <div className="flex items-center gap-1 mt-1">
                                                        {renderStars(avaliacao.nota_pontualidade)}
                                                    </div>
                                                </div>
                                            )}
                                            {avaliacao.nota_profissionalismo && (
                                                <div className="text-sm">
                                                    <span className="font-medium">Profissionalismo:</span>
                                                    <div className="flex items-center gap-1 mt-1">
                                                        {renderStars(avaliacao.nota_profissionalismo)}
                                                    </div>
                                                </div>
                                            )}
                                            {avaliacao.nota_eficacia && (
                                                <div className="text-sm">
                                                    <span className="font-medium">Eficácia:</span>
                                                    <div className="flex items-center gap-1 mt-1">
                                                        {renderStars(avaliacao.nota_eficacia)}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    {/* Comentário */}
                                    {avaliacao.comentario && (
                                        <div className="bg-muted/50 rounded-lg p-3">
                                            <p className="text-sm">{avaliacao.comentario}</p>
                                        </div>
                                    )}

                                    {/* Recomendação */}
                                    <div className="flex items-center gap-2">
                                        <ThumbsUp className={`h-4 w-4 ${avaliacao.recomendaria ? 'text-green-600' : 'text-gray-400'}`} />
                                        <span className="text-sm">
                                            {avaliacao.recomendaria ? 'Recomendaria este fisioterapeuta' : 'Não recomendaria este fisioterapeuta'}
                                        </span>
                                    </div>

                                    {/* Status */}
                                    {!avaliacao.aprovada && (
                                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                                            <p className="text-sm text-yellow-800">
                                                <strong>Avaliação pendente:</strong> Sua avaliação está sendo analisada e será publicada em breve.
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        ))}

                        {/* Paginação */}
                        {avaliacoes.meta.last_page > 1 && (
                            <div className="flex justify-center gap-2">
                                {avaliacoes.links.map((link, index) => (
                                    <Button
                                        key={index}
                                        variant={link.active ? "default" : "outline"}
                                        size="sm"
                                        asChild={!!link.url}
                                        disabled={!link.url}
                                    >
                                        {link.url ? (
                                            <Link href={link.url} dangerouslySetInnerHTML={{ __html: link.label }} />
                                        ) : (
                                            <span dangerouslySetInnerHTML={{ __html: link.label }} />
                                        )}
                                    </Button>
                                ))}
                            </div>
                        )}
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
