<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Notifications\CustomVerifyEmail;
use App\Notifications\ResetPasswordNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Cache;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'phone',
        'avatar',
        'birth_date',
        'gender',
        'address',
        'city',
        'state',
        'zip_code',
        'medical_history',
        'emergency_contact',
        'active',
        'has_subscription',
        'onboarding_completed',
        'plan_selected',
        'checkout_completed',
        'onboarding_completed_at',
        'plan_selected_at',
        'checkout_completed_at',
        'privacy_profile_visible',
        'privacy_contact_visible',
        'privacy_medical_visible',
        'privacy_allow_marketing',
        'main_objective',
        'pain_level',
        'specific_areas',
        'treatment_goals',
        'preferred_time',
        'preferred_days',
        'communication_preference',
        'reminder_frequency',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'birth_date' => 'date',
            'active' => 'boolean',
            'has_subscription' => 'boolean',
            'onboarding_completed' => 'boolean',
            'plan_selected' => 'boolean',
            'checkout_completed' => 'boolean',
            'onboarding_completed_at' => 'datetime',
            'plan_selected_at' => 'datetime',
            'checkout_completed_at' => 'datetime',
            'privacy_profile_visible' => 'boolean',
            'privacy_contact_visible' => 'boolean',
            'privacy_medical_visible' => 'boolean',
            'privacy_allow_marketing' => 'boolean',
            'specific_areas' => 'array',
            'preferred_days' => 'array',
        ];
    }

    // Relacionamentos
    public function fisioterapeuta()
    {
        return $this->hasOne(Fisioterapeuta::class);
    }

    public function afiliado()
    {
        return $this->hasOne(Afiliado::class);
    }

    public function estabelecimentos()
    {
        return $this->hasMany(Estabelecimento::class);
    }

    public function estabelecimento()
    {
        return $this->hasOne(Estabelecimento::class);
    }

    public function assinaturas()
    {
        return $this->hasMany(Assinatura::class);
    }

    public function agendamentosComoPaciente()
    {
        return $this->hasMany(Agendamento::class, 'paciente_id');
    }

    public function agendamentosComoFisioterapeuta()
    {
        return $this->hasMany(Agendamento::class, 'fisioterapeuta_id');
    }

    public function avaliacoesRecebidas()
    {
        return $this->hasMany(Avaliacao::class, 'fisioterapeuta_id');
    }

    public function avaliacoesFeitas()
    {
        return $this->hasMany(Avaliacao::class, 'paciente_id');
    }

    // Relacionamentos para sistema de horários customizados
    public function horariosBase()
    {
        return $this->hasMany(HorarioBase::class, 'fisioterapeuta_id');
    }

    public function horariosExcecoes()
    {
        return $this->hasMany(HorarioExcecao::class, 'fisioterapeuta_id');
    }

    public function configFeriados()
    {
        return $this->hasOne(MedicoFeriadosConfig::class, 'fisioterapeuta_id');
    }

    // Scopes
    public function scopeAtivos($query)
    {
        return $query->where('active', true);
    }

    public function scopePorRole($query, $role)
    {
        return $query->where('role', $role);
    }

    // Métodos auxiliares
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    public function isFisioterapeuta()
    {
        return $this->role === 'fisioterapeuta';
    }

    public function isPaciente()
    {
        return $this->role === 'paciente';
    }

    public function isAfiliado()
    {
        return $this->role === 'afiliado';
    }

    public function isEmpresa()
    {
        return $this->role === 'empresa';
    }

    public function hasAfiliadoProfile()
    {
        return $this->afiliado()->exists();
    }

    public function getAfiliadoProfile()
    {
        return $this->afiliado;
    }

    public function canSwitchToAfiliadoMode()
    {
        return $this->hasAfiliadoProfile() &&
               $this->afiliado->status === 'aprovado' &&
               $this->afiliado->ativo;
    }

    /**
     * Send the password reset notification.
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new ResetPasswordNotification($token));
    }

    /**
     * Send the email verification notification with throttling.
     */
    public function sendEmailVerificationNotification()
    {
        // Se for durante o registro (usuário recém criado), não enviar automaticamente
        // O email de boas-vindas será enviado pelo SendWelcomeEmail listener
        if ($this->wasRecentlyCreated) {
            \Illuminate\Support\Facades\Log::info('Email verification skipped during registration', [
                'user_id' => $this->id,
                'email' => $this->email,
                'reason' => 'recently_created'
            ]);
            return false;
        }

        // Em ambiente local, desabilitar throttling para permitir testes repetidos
        if (app()->environment('local')) {
            try {
                // Enviar notificação customizada para todos os usuários
                $this->notify(new CustomVerifyEmail);
                return true;
            } catch (\Exception $e) {
                // Log do erro mas não lança exceção
                \Illuminate\Support\Facades\Log::error('Erro ao enviar email de verificação', [
                    'user_id' => $this->id,
                    'email' => $this->email,
                    'error' => $e->getMessage()
                ]);
                return false;
            }
        }

        // Verificar throttling - 30 segundos entre envios
        $throttleKey = "email_verification_throttle_{$this->id}";
        if (Cache::has($throttleKey)) {
            // Em vez de lançar exceção, retornar false para indicar que não foi enviado
            return false;
        }

        // Verificar limite por hora - máximo 3 emails por hora
        $hourlyKey = "email_verification_hourly_{$this->id}";
        $hourlyCount = Cache::get($hourlyKey, 0);
        if ($hourlyCount >= 3) {
            // Em vez de lançar exceção, retornar false para indicar que não foi enviado
            return false;
        }

        try {
            // Enviar notificação customizada para todos os usuários
            $this->notify(new CustomVerifyEmail);

            // Definir throttling de 30 segundos
            Cache::put($throttleKey, true, 30);

            // Incrementar contador por hora
            Cache::put($hourlyKey, $hourlyCount + 1, 3600);

            return true;
        } catch (\Exception $e) {
            // Log do erro mas não lança exceção
            \Illuminate\Support\Facades\Log::error('Erro ao enviar email de verificação', [
                'user_id' => $this->id,
                'email' => $this->email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if user can request email verification.
     */
    public function canRequestEmailVerification(): bool
    {
        // Em ambiente local, sempre permitir solicitações para testes
        if (app()->environment('local')) {
            return true;
        }

        $throttleKey = "email_verification_throttle_{$this->id}";
        $hourlyKey = "email_verification_hourly_{$this->id}";
        $hourlyCount = Cache::get($hourlyKey, 0);

        return !Cache::has($throttleKey) && $hourlyCount < 3;
    }

    /**
     * Get remaining time for next email verification request.
     */
    public function getEmailVerificationThrottleTime(): int
    {
        // Em ambiente local, sempre retornar 0 para permitir testes imediatos
        if (app()->environment('local')) {
            return 0;
        }

        $throttleKey = "email_verification_throttle_{$this->id}";
        
        // Verificar se a chave existe
        if (!Cache::has($throttleKey)) {
            return 0;
        }
        
        // Para drivers que não suportam TTL, usar uma abordagem alternativa
        try {
            if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
                $ttl = Cache::store('default')->getRedis()->ttl(Cache::getPrefix() . $throttleKey);
                return max(0, $ttl);
            } else {
                // Para outros drivers, assumir 30 segundos se a chave existir
                return 30;
            }
        } catch (\Exception $e) {
            // Fallback: se houver erro, assumir que ainda há throttling
            return 30;
        }
    }

    // Relacionamentos para prescrições e orientações
    public function prescricoes()
    {
        return $this->hasMany(Prescricao::class, 'paciente_id');
    }

    public function orientacoesDomiciliares()
    {
        return $this->hasMany(OrientacaoDomiciliar::class, 'paciente_id');
    }

    /**
     * Get the avatar URL attribute.
     */
    public function getAvatarAttribute($value)
    {
        if (!$value) {
            return null;
        }
        
        // Se já é uma URL completa, retorna como está
        if (str_starts_with($value, 'http')) {
            return $value;
        }
        
        // Converte o caminho relativo para URL completa
        return \Illuminate\Support\Facades\Storage::url($value);
    }
}
