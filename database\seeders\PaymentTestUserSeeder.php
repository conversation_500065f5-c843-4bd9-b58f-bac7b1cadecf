<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class PaymentTestUserSeeder extends Seeder
{
    public function run(): void
    {
        $email = '<EMAIL>';
        
        // Delete if exists
        User::where('email', $email)->delete();

        $user = User::create([
            'name' => 'Test Payment User',
            'email' => $email,
            'password' => Hash::make('Password123'),
            'role' => 'paciente',
            'active' => true,
            'has_subscription' => false,
            'onboarding_completed' => true, // Skip onboarding
            'plan_selected' => false,
            'checkout_completed' => false,
            'email_verified_at' => now(),
        ]);

        $this->command->info('Payment test user created: ' . $user->email);
    }
}
