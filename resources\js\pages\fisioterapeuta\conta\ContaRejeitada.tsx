import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';

interface ContaRejeitadaProps {
    motivo?: string;
}

export default function ContaRejeitada({ motivo }: ContaRejeitadaProps) {
    return (
        <>
            <Head title="Conta Rejeitada" />
            
            <div className="container mx-auto px-4 py-8 max-w-4xl">
                <Card className="border-0 shadow-lg">
                    <CardHeader className="text-center">
                        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100 mb-4">
                            <AlertCircle className="h-8 w-8 text-red-600" />
                        </div>
                        <CardTitle className="text-2xl font-bold text-gray-900">Sua conta foi rejeitada</CardTitle>
                        <CardDescription className="mt-2 text-gray-600">
                            Infelizmente, não foi possível aprovar seu cadastro no momento.
                        </CardDescription>
                    </CardHeader>
                    
                    <CardContent className="text-center space-y-6">
                        <div className="space-y-4 text-gray-700">
                            {motivo ? (
                                <div className="bg-red-50 border-l-4 border-red-400 p-4 text-left">
                                    <div className="flex">
                                        <div className="flex-shrink-0">
                                            <AlertCircle className="h-5 w-5 text-red-400" />
                                        </div>
                                        <div className="ml-3">
                                            <p className="text-sm text-red-700">
                                                <strong>Motivo da rejeição:</strong> {motivo}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <p>
                                    Identificamos algumas inconsistências nas informações fornecidas que não estão de acordo com nossos critérios de aprovação.
                                </p>
                            )}
                            
                            <p>
                                Se você acredita que houve um engano ou gostaria de mais informações, por favor, entre em contato com nosso suporte.
                            </p>
                        </div>
                        
                        <div className="flex justify-center space-x-4 pt-4">
                            <Button 
                                variant="outline" 
                                onClick={() => window.location.href = '/contato'}
                            >
                                Entrar em Contato
                            </Button>
                            <Button 
                                onClick={() => {
                                    // Fazer logout
                                    const form = document.createElement('form');
                                    form.method = 'POST';
                                    form.action = '/logout';
                                    const csrf = document.createElement('input');
                                    csrf.type = 'hidden';
                                    csrf.name = '_token';
                                    csrf.value = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
                                    form.appendChild(csrf);
                                    document.body.appendChild(form);
                                    form.submit();
                                }}
                                variant="ghost"
                            >
                                Sair
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </>
    );
}
