<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Http;

try {
    echo "=== Testando Resend com configuração SSL ===\n";
    
    // Configurar HTTP client para ignorar SSL em desenvolvimento
    $response = Http::withHeaders([
        'Authorization' => 'Bearer ' . config('services.resend.key'),
        'Content-Type' => 'application/json',
    ])
    ->withOptions([
        'verify' => false, // Desabilitar verificação SSL
        'timeout' => 30,
    ])
    ->post('https://api.resend.com/emails', [
        'from' => 'F4 Fisio <<EMAIL>>',
        'to' => ['<EMAIL>'],
        'subject' => 'Teste SSL Fix - F4 Fisio - ' . now()->format('d/m/Y H:i:s'),
        'html' => '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h1 style="color: #2563eb;">🎉 SSL Fix aplicado com sucesso!</h1>
                <p>Este é um email de teste do sistema F4 Fisio após correção do SSL.</p>
                <p>Se você está recebendo este email, significa que o problema de SSL foi resolvido!</p>
                <hr style="margin: 20px 0;">
                <p style="color: #6b7280; font-size: 14px;">
                    Enviado em: ' . now()->format('d/m/Y H:i:s') . '<br>
                    Sistema: F4 Fisio<br>
                    Usuário: <EMAIL>
                </p>
            </div>
        '
    ]);

    if ($response->successful()) {
        $data = $response->json();
        echo "✅ Email enviado com sucesso!\n";
        echo "Email ID: " . $data['id'] . "\n";
        echo "Destinatário: <EMAIL>\n";
        echo "Verifique sua caixa de entrada.\n";
    } else {
        echo "❌ Erro na resposta da API:\n";
        echo "Status: " . $response->status() . "\n";
        echo "Resposta: " . $response->body() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erro ao enviar email: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
