import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { safeRoute } from '@/utils/route-helper';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { Check, Edit, Eye, Plus, Search, Trash2, X } from 'lucide-react';
import { useState } from 'react';
import Pagination from '@/components/Pagination';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Fisioterapeutas',
        href: '/admin/fisioterapeutas',
    },
];

interface Fisioterapeuta {
    id: number;
    crefito: string;
    specializations: string[];
    bio?: string;
    hourly_rate: number;
    available_areas: string[];
    working_hours: any;
    created_at: string;
    status: 'pending' | 'approved' | 'rejected';
    rejection_reason?: string;
    user: {
        id: number;
        name: string;
        email: string;
        active: boolean;
        phone?: string;
    };
}

interface Props {
    fisioterapeutas: {
        data: Fisioterapeuta[];
        links: any[];
        meta: any;
    };
    filters: {
        active?: boolean;
        status?: string;
        search?: string;
    };
}

export default function AdminFisioterapeutas({ fisioterapeutas, filters }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
    const [rejectionReason, setRejectionReason] = useState('');
    const [pendingRejection, setPendingRejection] = useState<{id: number; callback: (reason: string) => void} | null>(null);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [pendingDeleteId, setPendingDeleteId] = useState<number | null>(null);
    
    const { data, setData, get } = useForm({
        active: filters.active?.toString() || undefined,
        status: filters.status || 'pending', // Filtro padrão para mostrar pendentes
        search: filters.search || '',
    });

    const handleFilter = () => {
        try {
            const routeUrl = safeRoute('admin.fisioterapeutas.index');
            if (routeUrl !== '#') {
                get(routeUrl, {
                    preserveState: true,
                    replace: true,
                });
            }
        } catch (error) {
            console.error('Erro ao filtrar fisioterapeutas:', error);
        }
    };

    const handleDelete = (id: number) => {
        setPendingDeleteId(id);
        setDeleteDialogOpen(true);
    };

    const confirmDelete = async () => {
        if (!pendingDeleteId) return;
        
        try {
            const routeUrl = safeRoute('admin.fisioterapeutas.destroy', pendingDeleteId);
            if (routeUrl !== '#') {
                await router.delete(routeUrl);
                toast.success('Fisioterapeuta removido com sucesso!');
            }
        } catch (error) {
            console.error('Erro ao deletar fisioterapeuta:', error);
            toast.error('Ocorreu um erro ao remover o fisioterapeuta.');
        } finally {
            setDeleteDialogOpen(false);
            setPendingDeleteId(null);
        }
    };

    const handleStatusUpdate = async (id: number, status: 'approved' | 'rejected', reason?: string) => {
        // Se for rejeitar e não tiver motivo, mostra o diálogo
        if (status === 'rejected' && !reason) {
            setPendingRejection({
                id,
                callback: (rejectionReason) => {
                    if (rejectionReason.trim()) {
                        processStatusUpdate(id, 'rejected', rejectionReason);
                    } else {
                        toast.error('O motivo da rejeição é obrigatório.');
                    }
                }
            });
            setRejectionReason('');
            setRejectionDialogOpen(true);
            return;
        }
        
        await processStatusUpdate(id, status, reason);
    };

    const processStatusUpdate = async (id: number, status: 'approved' | 'rejected', reason?: string) => {
        let toastId: string | number | undefined;
        
        try {

                // Determina a rota com base no status
                const routeName = status === 'approved' 
                    ? 'admin.fisioterapeutas.approve' 
                    : 'admin.fisioterapeutas.reject';
                    
                const routeUrl = safeRoute(routeName, id);
                
                if (routeUrl === '#') {
                    throw new Error('Rota inválida');
                }

                // Mostrar loading
                toastId = toast.loading(status === 'approved' 
                    ? 'Aprovando fisioterapeuta...' 
                    : 'Rejeitando fisioterapeuta...');

                const requestBody: Record<string, any> = {};
                
                // Adiciona o motivo da rejeição se for o caso
                if (status === 'rejected' && reason) {
                    requestBody.rejection_reason = reason;
                }

                const response = await fetch(routeUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: Object.keys(requestBody).length > 0 ? JSON.stringify(requestBody) : undefined
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(
                        data.message || 
                        data.errors?.rejection_reason?.[0] || 
                        `Erro ao ${status === 'approved' ? 'aprovar' : 'rejeitar'} o fisioterapeuta`
                    );
                }

                // Atualiza a lista de fisioterapeutas após a atualização
                router.reload();
                
                if (toastId !== undefined) {
                    toast.success(`Fisioterapeuta ${status === 'approved' ? 'aprovado' : 'rejeitado'} com sucesso!`, {
                        id: toastId
                    });
                }

        } catch (error) {
            console.error(`Erro ao ${status === 'approved' ? 'aprovar' : 'rejeitar'} o fisioterapeuta:`, error);
            if (toastId !== undefined) {
                toast.error(error instanceof Error ? error.message : `Ocorreu um erro ao ${status === 'approved' ? 'aprovar' : 'rejeitar'} o fisioterapeuta.`, {
                    id: toastId
                });
            } else {
                toast.error(error instanceof Error ? error.message : `Ocorreu um erro ao ${status === 'approved' ? 'aprovar' : 'rejeitar'} o fisioterapeuta.`);
            }
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Fisioterapeutas" />
            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                {/* Diálogo de confirmação de exclusão */}
                <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
                            <AlertDialogDescription>
                                Tem certeza que deseja remover este fisioterapeuta? Esta ação não pode ser desfeita.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                            <AlertDialogAction 
                                onClick={confirmDelete}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                                Confirmar Exclusão
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>

                {/* Diálogo de motivo de rejeição */}
                <AlertDialog open={rejectionDialogOpen} onOpenChange={setRejectionDialogOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>Motivo da Rejeição</AlertDialogTitle>
                            <AlertDialogDescription>
                                Por favor, informe o motivo pelo qual você está rejeitando este fisioterapeuta.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <div className="grid gap-4 py-4">
                            <div className="grid gap-2">
                                <Label htmlFor="rejectionReason">Motivo</Label>
                                <Textarea
                                    id="rejectionReason"
                                    value={rejectionReason}
                                    onChange={(e) => setRejectionReason(e.target.value)}
                                    placeholder="Digite o motivo da rejeição..."
                                    className="min-h-[100px]"
                                />
                            </div>
                        </div>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                            <AlertDialogAction 
                                onClick={(e) => {
                                    e.preventDefault();
                                    if (rejectionReason.trim()) {
                                        setRejectionDialogOpen(false);
                                        if (pendingRejection) {
                                            pendingRejection.callback(rejectionReason);
                                            setPendingRejection(null);
                                        }
                                    } else {
                                        toast.error('Por favor, informe o motivo da rejeição.');
                                    }
                                }}
                                disabled={!rejectionReason.trim()}
                            >
                                Confirmar Rejeição
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Fisioterapeutas</h1>
                        <p className="text-gray-600">Gerencie os fisioterapeutas cadastrados</p>
                    </div>
                </div>

                {/* Filtros */}
                <div className="flex items-end gap-4">
                    <div className="flex-1">
                        <Input
                            placeholder="Buscar por nome ou CREFITO..."
                            value={search}
                            onChange={(e) => {
                                setSearch(e.target.value);
                                setData('search', e.target.value);
                            }}
                            className="max-w-sm"
                        />
                    </div>
                    <Select 
                        value={data.status || 'pending'} 
                        onValueChange={(value) => setData('status', value)}
                    >
                        <SelectTrigger className="w-48">
                            <SelectValue placeholder="Status de aprovação" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Todos</SelectItem>
                            <SelectItem value="pending">Pendentes</SelectItem>
                            <SelectItem value="approved">Aprovados</SelectItem>
                            <SelectItem value="rejected">Rejeitados</SelectItem>
                        </SelectContent>
                    </Select>
                    <Select 
                        value={data.active || 'all'} 
                        onValueChange={(value) => setData('active', value === 'all' ? undefined : value)}
                    >
                        <SelectTrigger className="w-48">
                            <SelectValue placeholder="Status de ativação" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Todos os status</SelectItem>
                            <SelectItem value="true">Ativo</SelectItem>
                            <SelectItem value="false">Inativo</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button 
                        onClick={handleFilter}
                        className="bg-primary hover:bg-primary/90"
                    >
                        <Search className="mr-2 h-4 w-4" />
                        Filtrar
                    </Button>
                </div>

                {/* Cards dos Fisioterapeutas */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {fisioterapeutas.data.map((fisioterapeuta) => (
                        <div key={fisioterapeuta.id} className="rounded-lg border bg-card p-6">
                            <div className="mb-4 flex items-start justify-between">
                                <div className="flex-1">
                                    <div className="flex items-center gap-2">
                                        <h3 className="text-lg font-semibold">{fisioterapeuta.user.name}</h3>
                                        <span
                                            className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                fisioterapeuta.status === 'approved' 
                                                    ? 'bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20' 
                                                    : fisioterapeuta.status === 'rejected'
                                                        ? 'bg-red-50 text-red-700 ring-1 ring-inset ring-red-600/20'
                                                        : 'bg-yellow-50 text-yellow-700 ring-1 ring-inset ring-yellow-600/20'
                                            }`}
                                        >
                                            {fisioterapeuta.status === 'approved' 
                                                ? 'Aprovado' 
                                                : fisioterapeuta.status === 'rejected'
                                                    ? 'Rejeitado'
                                                    : 'Pendente'}
                                        </span>
                                    </div>
                                    <p className="text-sm text-muted-foreground">CREFITO: {fisioterapeuta.crefito}</p>
                                    <p className="text-sm text-muted-foreground">{fisioterapeuta.user.email}</p>
                                    {fisioterapeuta.user.phone && <p className="text-sm text-muted-foreground">{fisioterapeuta.user.phone}</p>}
                                </div>
                                <span
                                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                        fisioterapeuta.user.active 
                                            ? 'bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20' 
                                            : 'bg-gray-50 text-gray-700 ring-1 ring-inset ring-gray-600/20'
                                    }`}
                                >
                                    {fisioterapeuta.user.active ? 'Ativo' : 'Inativo'}
                                </span>
                            </div>

                            <div className="space-y-3">
                                <div>
                                    <p className="text-sm font-medium text-gray-700">Especializações:</p>
                                    <div className="mt-1 flex flex-wrap gap-1">
                                        {fisioterapeuta.specializations.map((spec, index) => (
                                            <span 
                                                key={index} 
                                                className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10"
                                            >
                                                {spec}
                                            </span>
                                        ))}
                                    </div>
                                </div>

                                <div>
                                    <p className="text-sm font-medium text-gray-700">Valor por hora:</p>
                                    <p className="text-lg font-bold text-green-600">
                                        {new Intl.NumberFormat('pt-BR', {
                                            style: 'currency',
                                            currency: 'BRL',
                                        }).format(fisioterapeuta.hourly_rate)}
                                    </p>
                                </div>

                                <div>
                                    <p className="text-sm font-medium text-gray-700">Áreas de atendimento:</p>
                                    <p className="text-sm text-muted-foreground">{fisioterapeuta.available_areas.join(', ')}</p>
                                </div>

                                {fisioterapeuta.bio && (
                                    <div>
                                        <p className="text-sm font-medium text-gray-700">Bio:</p>
                                        <p className="line-clamp-2 text-sm text-muted-foreground">{fisioterapeuta.bio}</p>
                                    </div>
                                )}
                            </div>

                            <div className="mt-4 flex items-center justify-end gap-2">
                                <div className="flex items-center gap-2">
                                    {fisioterapeuta.status === 'pending' && (
                                        <>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="border-green-500 text-green-600 hover:bg-green-50 hover:text-green-700"
                                                onClick={() => handleStatusUpdate(fisioterapeuta.id, 'approved')}
                                            >
                                                <Check className="mr-1 h-4 w-4" />
                                                Aprovar
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="border-red-500 text-red-600 hover:bg-red-50 hover:text-red-700"
                                                onClick={() => {
                                                    setPendingRejection({
                                                        id: fisioterapeuta.id,
                                                        callback: (rejectionReason) => {
                                                            if (rejectionReason.trim()) {
                                                                handleStatusUpdate(fisioterapeuta.id, 'rejected', rejectionReason);
                                                            } else {
                                                                toast.error('O motivo da rejeição é obrigatório.');
                                                            }
                                                        }
                                                    });
                                                    setRejectionReason('');
                                                    setRejectionDialogOpen(true);
                                                }}
                                            >
                                                <X className="mr-1 h-4 w-4" />
                                                Rejeitar
                                            </Button>
                                        </>
                                    )}
                                    <Link
                                        href={safeRoute('admin.fisioterapeutas.show', fisioterapeuta.id)}
                                    >
                                        <Button variant="outline" size="icon" className="h-8 w-8">
                                            <Eye className="h-4 w-4" />
                                            <span className="sr-only">Visualizar</span>
                                        </Button>
                                    </Link>
                                    <Link
                                        href={safeRoute('admin.fisioterapeutas.edit', fisioterapeuta.id)}
                                    >
                                        <Button variant="outline" size="icon" className="h-8 w-8">
                                            <Edit className="h-4 w-4" />
                                            <span className="sr-only">Editar</span>
                                        </Button>
                                    </Link>
                                    <Button
                                        variant="outline"
                                        size="icon"
                                        className="h-8 w-8 text-red-600 hover:bg-red-50 hover:text-red-700 border-red-200"
                                        onClick={() => handleDelete(fisioterapeuta.id)}
                                    >
                                        <Trash2 className="h-4 w-4" />
                                        <span className="sr-only">Excluir</span>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    {fisioterapeutas.data.length === 0 && (
                        <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
                            <p className="text-muted-foreground mb-4">Nenhum fisioterapeuta encontrado</p>
                            <Link href={safeRoute('admin.fisioterapeutas.create')}>
                                <Button className="bg-primary hover:bg-primary/90">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Cadastrar Primeiro Fisioterapeuta
                                </Button>
                            </Link>
                        </div>
                    )}

                </div>

                {/* Paginação */}
                {fisioterapeutas.links && fisioterapeutas.data.length > 0 && (
                    <Pagination 
                        links={fisioterapeutas.links} 
                        meta={fisioterapeutas.meta} 
                    />
                )}
            </div>
        </AppLayout>
    );
}
