<?php

namespace App\Providers;

use App\Events\UserRegistered;
use App\Listeners\CustomRegisteredListener;
use App\Listeners\SendPasswordChangedNotification;
use App\Listeners\SendWelcomeEmail;
use App\Listeners\SetupFisioterapeutaStatus;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Auth\Events\Registered;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        PasswordReset::class => [
            SendPasswordChangedNotification::class,
        ],
        Registered::class => [
            CustomRegisteredListener::class,
            SendWelcomeEmail::class,
        ],
        UserRegistered::class => [
            SetupFisioterapeutaStatus::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
