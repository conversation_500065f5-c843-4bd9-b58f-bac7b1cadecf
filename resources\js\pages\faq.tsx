import FAQsSection from '@/components/faqs-section';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import PublicLayout from '@/layouts/public-layout';
import { Link } from '@inertiajs/react';

export default function FAQ() {
    const faqCategories = [
        {
            title: 'Sobre os Serviços',
            items: [
                {
                    id: 'servicos-1',
                    question: 'Quais tipos de fisioterapia vocês oferecem?',
                    answer: 'Oferecemos fisioterapia ortopédica, neurológica, respiratória, geriátrica, pediátrica e pós-operatória. Todos os tratamentos são personalizados conforme a necessidade de cada paciente.',
                },
                {
                    id: 'servicos-2',
                    question: 'Os fisioterapeutas são credenciados?',
                    answer: 'Sim, todos os nossos fisioterapeutas possuem registro no CREFITO e são devidamente credenciados. Passam por processo de seleção rigoroso e avaliação contínua.',
                },
                {
                    id: 'servicos-3',
                    question: 'Que equipamentos são utilizados?',
                    answer: 'Utilizamos equipamentos profissionais portáteis como ultrassom, TENS, FES, laser terapêutico, além de materiais para exercícios terapêuticos e mobilização.',
                },
            ],
        },
        {
            title: 'Agendamento e Atendimento',
            items: [
                {
                    id: 'agendamento-1',
                    question: 'Como funciona o agendamento?',
                    answer: 'O agendamento pode ser feito pelo WhatsApp, telefone ou através da nossa plataforma online. Oferecemos horários flexíveis, incluindo fins de semana.',
                },
                {
                    id: 'agendamento-2',
                    question: 'Vocês atendem em qual região?',
                    answer: 'Atendemos na Grande São Paulo, incluindo São Paulo capital, Barueri, Alphaville, Osasco, Carapicuíba e Itapevi. Consulte disponibilidade para outras regiões.',
                },
                {
                    id: 'agendamento-3',
                    question: 'Qual o tempo de duração das sessões?',
                    answer: 'As sessões têm duração de 60 minutos, incluindo avaliação, tratamento e orientações. A primeira consulta pode durar até 90 minutos para avaliação completa.',
                },
            ],
        },
        {
            title: 'Pagamentos',
            items: [
                {
                    id: 'pagamento-1',
                    question: 'Como funciona o pagamento?',
                    answer: 'Aceitamos PIX, cartão de crédito, débito e dinheiro. Em atendimentos recorrentes, o pagamento é antecipado conforme combinado. Sessões avulsas são pagas no ato do atendimento.',
                },
                {
                    id: 'pagamento-2',
                    question: 'Posso cancelar meu atendimento a qualquer momento?',
                    answer: 'Sim, você pode solicitar o cancelamento do atendimento a qualquer momento, sem multa ou burocracia. Caso haja um pacote contratado, o cancelamento ocorre ao final do período já pago.',
                },
                {
                    id: 'pagamento-3',
                    question: 'E se eu não usar todas as sessões do mês?',
                    answer: 'As sessões não utilizadas podem ser reagendadas dentro do período contratado. Oferecemos flexibilidade para sua agenda, porém não há acúmulo para períodos seguintes.',
                },
                {
                    id: 'pagamento-4',
                    question: 'Há taxa de adesão ou outros custos?',
                    answer: 'Não cobramos taxa de adesão. O valor do atendimento inclui sessão, equipamentos e deslocamento do fisioterapeuta. Não há custos adicionais.',
                },
            ],
        },
        {
            title: 'Para Profissionais',
            items: [
                {
                    id: 'profissionais-1',
                    question: 'Como posso trabalhar com a F4 Fisio?',
                    answer: 'Entre em contato pelo WhatsApp ou e-mail enviando seu currículo e registro no CREFITO. Avaliamos perfil, experiência e disponibilidade.',
                },
                {
                    id: 'profissionais-2',
                    question: 'Como funciona o programa de afiliados?',
                    answer: 'Nosso programa permite que você indique pacientes e receba comissão por cada indicação convertida. Cadastre-se na área de afiliados para mais detalhes.',
                },
                {
                    id: 'profissionais-3',
                    question: 'Posso cadastrar minha clínica na plataforma?',
                    answer: 'Sim! É possível cadastrar sua clínica para aparecer na busca, com perfil completo e contato direto via WhatsApp.',
                },
            ],
        },
    ];

    return (
        <PublicLayout
            title="Perguntas Frequentes - F4 Fisio"
            description="Encontre respostas para as principais dúvidas sobre nossos serviços de fisioterapia domiciliar, agendamentos, pagamentos e muito mais."
        >
            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-20 md:py-32">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-4xl font-medium text-balance md:text-5xl">Perguntas Frequentes</h1>
                            <p className="mx-auto my-8 max-w-3xl text-xl text-balance text-muted-foreground">
                                Encontre respostas rápidas e completas sobre nossos serviços de fisioterapia domiciliar, agendamentos, pagamentos e muito
                                mais.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* FAQ por Categorias */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                    <div className="space-y-12">
                        {faqCategories.map((category, categoryIndex) => (
                            <div key={categoryIndex}>
                                <h2 className="mb-6 text-center text-2xl font-semibold">{category.title}</h2>
                                <Accordion type="single" collapsible className="w-full space-y-4">
                                    {category.items.map((item) => (
                                        <AccordionItem key={item.id} value={item.id} className="rounded-lg border border-border px-6 py-2">
                                            <AccordionTrigger className="text-left hover:no-underline">
                                                <span className="font-medium">{item.question}</span>
                                            </AccordionTrigger>
                                            <AccordionContent className="leading-relaxed text-muted-foreground">{item.answer}</AccordionContent>
                                        </AccordionItem>
                                    ))}
                                </Accordion>
                            </div>
                        ))}
                    </div>

                    {/* Contato para mais dúvidas */}
                    <div className="mt-16 text-center">
                        <div className="rounded-lg bg-muted/50 p-8">
                            <h3 className="mb-4 text-xl font-semibold">Não encontrou sua resposta?</h3>
                            <p className="mb-6 text-muted-foreground">
                                Nossa equipe está pronta para esclarecer todas as suas dúvidas sobre fisioterapia domiciliar.
                            </p>
                            <div className="flex flex-col justify-center gap-4 sm:flex-row">
                                <Button asChild>
                                    <Link href="/contato">Falar com Atendimento</Link>
                                </Button>
                                <Button variant="outline" asChild>
                                    <a
                                        href="https://wa.me/5511978196207?text=Olá! Tenho uma dúvida sobre os serviços da F4 Fisio."
                                        target="_blank"
                                        rel="noopener noreferrer"
                                    >
                                        WhatsApp
                                    </a>
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* FAQ Section Existente (para manter consistência) */}
            <FAQsSection />
        </PublicLayout>
    );
}
