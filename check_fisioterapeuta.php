<?php

require_once 'vendor/autoload.php';

use App\Models\User;

try {
    // Inicializar <PERSON>
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    echo "=== Verificação do Fisioterapeuta ID 4 ===\n\n";

    $fisioterapeuta = User::with('fisioterapeuta')
        ->where('id', 4)
        ->where('role', 'fisioterapeuta')
        ->first();

    if ($fisioterapeuta) {
        echo "✅ Fisioterapeuta encontrado!\n";
        echo "   ID: " . $fisioterapeuta->id . "\n";
        echo "   Nome: " . $fisioterapeuta->name . "\n";
        echo "   Ativo: " . ($fisioterapeuta->active ? 'Sim' : 'Não') . "\n";
        echo "   Status conta: " . ($fisioterapeuta->suspended ? 'Suspenso' : 'Normal') . "\n";
        echo "   Banido: " . ($fisioterapeuta->banned ? 'Sim' : 'Não') . "\n";

        if ($fisioterapeuta->fisioterapeuta) {
            echo "   Status fisioterapeuta: " . ($fisioterapeuta->fisioterapeuta->status ?? 'N/A') . "\n";
            echo "   Preço/hora: R$ " . number_format($fisioterapeuta->fisioterapeuta->hourly_rate ?? 0, 2, ',', '.') . "\n";
        } else {
            echo "   ❌ Não tem perfil de fisioterapeuta!\n";
        }
    } else {
        echo "❌ Fisioterapeuta ID 4 não encontrado!\n";
    }

} catch (\Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
}
