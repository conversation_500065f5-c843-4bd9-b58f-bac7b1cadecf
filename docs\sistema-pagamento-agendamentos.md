# Sistema de Pagamento e Verificação de Agendamentos

## Visão Geral

Este sistema implementa um fluxo completo de pagamento para agendamentos, onde:

1. **Agendamentos são criados com status "pendente"**
2. **Um pagamento é associado ao agendamento via preference_id**
3. **O pagamento pode ser confirmado via webhook ou verificação manual**
4. **Após confirmação, o agendamento muda para status "confirmado"**

## Componentes Implementados

### 1. Modelos Atualizados

#### `Agendamento` (`app/Models/Agendamento.php`)
- Adicionado relacionamento com `Pagamento`
- Adicionado scopes para `pendentes` e `confirmados`
- Métodos para verificação de pagamento:
  - `isPendente()` - Verifica se status é pendente
  - `isConfirmado()` - Verifica se status é confirmado
  - `hasPayment()` - Verifica se tem pagamento associado
  - `isPaymentConfirmed()` - Verifica se pagamento foi aprovado
  - `getPaymentLink()` - Obtém link de pagamento do Mercado Pago
  - `verificarEConfirmarPagamento()` - Verifica pagamento e atualiza status

#### `Pagamento` (`app/Models/Pagamento.php`)
- Já existente com suporte a preference_id e payment_id
- Relacionamento com agendamento já implementado

### 2. Eventos

#### `AgendamentoConfirmado` (`app/Events/AgendamentoConfirmado.php`)
- Evento disparado quando um agendamento é confirmado
- Pode ser usado para enviar notificações, emails, etc.

### 3. Controllers

#### `AgendamentoPagamentoController` (`app/Http/Controllers/AgendamentoPagamentoController.php`)
- `verificarPagamento()` - Verifica status de pagamento de um agendamento
- `webhookMercadoPago()` - Processa webhooks do Mercado Pago
- `reenviarLinkPagamento()` - Reenvia link de pagamento
- `listarPendentes()` - Lista agendamentos pendentes do usuário

### 4. Services

#### `AgendamentoPaymentService` (`app/Services/AgendamentoPaymentService.php`)
- `criarAgendamentoPendente()` - Cria agendamento com pagamento pendente
- `verificarPagamentosPendentes()` - Verificação em massa de pagamentos
- `cancelarAgendamentoPendente()` - Cancela agendamento pendente
- `reenviarLinkPagamento()` - Gera novo link de pagamento

### 5. Rotas

```php
// Rotas para verificação de pagamento de agendamentos
Route::middleware(['auth', 'verified'])->prefix('agendamentos')->name('agendamentos.')->group(function () {
    Route::post('{agendamento}/verificar-pagamento', [App\Http\Controllers\AgendamentoPagamentoController::class, 'verificarPagamento'])->name('verificar-pagamento');
    Route::get('{agendamento}/reenviar-link-pagamento', [App\Http\Controllers\AgendamentoPagamentoController::class, 'reenviarLinkPagamento'])->name('reenviar-link-pagamento');
    Route::get('/pendentes', [App\Http\Controllers\AgendamentoPagamentoController::class, 'listarPendentes'])->name('pendentes');
});

// Webhook específico para agendamentos
Route::post('/webhook/agendamento-pagamento', [App\Http\Controllers\AgendamentoPagamentoController::class, 'webhookMercadoPago'])->name('webhook.agendamento-pagamento');
```

## Fluxo de Funcionamento

### 1. Criação de Agendamento

```php
// Exemplo de uso
$agendamentoPaymentService = app(AgendamentoPaymentService::class);

$resultado = $agendamentoPaymentService->criarAgendamentoPendente([
    'paciente_id' => 1,
    'fisioterapeuta_id' => 2,
    'scheduled_at' => '2025-08-30 14:00:00',
    'duration' => 60,
    'service_type' => 'consulta',
    'notes' => 'Consulta inicial',
], [
    'amount' => 150.00,
    'title' => 'Consulta Fisioterapia',
    'description' => 'Pagamento para agendamento de consulta',
]);

if ($resultado['success']) {
    $agendamento = $resultado['agendamento'];
    $paymentLink = $resultado['payment_data']['init_point'];
    // Redirecionar usuário para pagamento
}
```

### 2. Verificação Manual (Quando usuário clica no agendamento)

```javascript
// Frontend - quando usuário clica em agendamento pendente
async function verificarPagamento(agendamentoId) {
    try {
        const response = await fetch(`/agendamentos/${agendamentoId}/verificar-pagamento`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Pagamento confirmado
            alert('Pagamento confirmado! Agendamento atualizado.');
            // Atualizar interface
        } else {
            // Mostrar modal com link de pagamento
            if (data.show_modal) {
                showModal(data.modal_data);
            }
        }
    } catch (error) {
        console.error('Erro:', error);
    }
}
```

### 3. Webhook (Confirmação automática)

O webhook é acionado automaticamente quando o Mercado Pago notifica sobre um pagamento:

```php
// O webhook processa:
// 1. Recebe notificação do Mercado Pago
// 2. Busca pagamento pelo external_reference
// 3. Atualiza status do pagamento
// 4. Se pagamento aprovado, atualiza status do agendamento para "confirmado"
// 5. Dispara evento AgendamentoConfirmado
```

### 4. Listar Agendamentos Pendentes

```javascript
// Frontend - listar agendamentos pendentes do usuário
async function carregarPendentes() {
    try {
        const response = await fetch('/agendamentos/pendentes');
        const data = await response.json();
        
        if (data.success) {
            // Exibir lista de agendamentos pendentes
            renderizarPendentes(data.agendamentos);
        }
    } catch (error) {
        console.error('Erro:', error);
    }
}
```

## Respostas da API

### Verificar Pagamento

#### Sucesso (Pagamento confirmado)
```json
{
    "success": true,
    "message": "Pagamento confirmado! Agendamento atualizado para status 'confirmado'.",
    "agendamento": {
        "id": 123,
        "status": "confirmado",
        "payment_status": "pago",
        "payment_method": "pix",
        "paid_at": "28/08/2025 20:15:30"
    }
}
```

#### Pagamento ainda pendente (mostrar modal)
```json
{
    "success": false,
    "message": "Pagamento ainda não confirmado.",
    "show_modal": true,
    "modal_data": {
        "title": "Pagamento Pendente",
        "message": "O pagamento para este agendamento ainda não foi confirmado. Por favor, realize o pagamento para confirmar sua sessão.",
        "payment_link": "https://www.mercadopago.com.br/checkout/v1/redirect?pref_id=123456789",
        "payment_status": "pendente",
        "payment_method": null,
        "amount": "R$ 150,00",
        "created_at": "28/08/2025 20:10:00"
    },
    "agendamento": {
        "id": 123,
        "status": "pendente",
        "payment_status": "pendente"
    }
}
```

### Listar Pendentes
```json
{
    "success": true,
    "agendamentos": [
        {
            "id": 123,
            "scheduled_at": "30/08/2025 14:00",
            "fisioterapeuta": "Dr. João Silva",
            "service_type": "consulta",
            "price": "R$ 150,00",
            "payment_status": "pendente",
            "payment_method": null,
            "created_at": "28/08/2025 20:10:00"
        }
    ]
}
```

## Configuração do Webhook

Para que o sistema funcione corretamente, configure o webhook no Mercado Pago:

1. Acesse sua conta Mercado Pago
2. Vá para "Integrações" > "Webhooks"
3. Adicione a URL: `https://seu-dominio.com/webhook/agendamento-pagamento`
4. Selecione os eventos: "payment" e "merchant_order"

## Segurança

- Todas as rotas exigem autenticação
- Verificação de permissão (paciente só pode verificar seus próprios agendamentos)
- Webhook processa notificações de forma segura
- Logs detalhados para auditoria

## Logs

O sistema gera logs detalhados para monitoramento:

```
✅ [AGENDAMENTO] Agendamento criado com status pendente
✅ [PAGAMENTO] Registro de pagamento criado
✅ [WEBHOOK] Agendamento confirmado via webhook
✅ [VERIFICAÇÃO] Agendamento confirmado automaticamente
❌ [ERRO] Mensagens de erro detalhadas
```

## Testes

Use os seguintes endpoints para testar:

```bash
# Criar agendamento de teste (via controller existente)
POST /teste-pagamento/criar

# Verificar pagamento manualmente
POST /agendamentos/{id}/verificar-pagamento

# Listar pendentes
GET /agendamentos/pendentes

# Reenviar link
GET /agendamentos/{id}/reenviar-link-pagamento
```

## Melhorias Futuras

1. **Notificações por email** quando pagamento é confirmado
2. **Lembretes automáticos** para pagamentos pendentes
3. **Cancelamento automático** de agendamentos após X dias sem pagamento
4. **Múltiplos métodos de pagamento** com fluxos diferentes
5. **Relatórios** de pagamentos e confirmações
