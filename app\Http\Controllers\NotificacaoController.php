<?php

namespace App\Http\Controllers;

use App\Models\Notificacao;
use App\Services\NotificacaoService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class NotificacaoController extends Controller
{
    protected $notificacaoService;

    public function __construct(NotificacaoService $notificacaoService)
    {
        $this->notificacaoService = $notificacaoService;
    }

    /**
     * Listar todas as notificações do usuário
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        $query = Notificacao::where('user_id', $user->id)
            ->with('agendamento.paciente', 'agendamento.fisioterapeuta')
            ->orderBy('data_envio', 'desc');

        // Filtro por status (lida/não lida)
        if ($request->filled('status')) {
            if ($request->status === 'nao_lidas') {
                $query->naoLidas();
            } elseif ($request->status === 'lidas') {
                $query->lidas();
            }
        }

        // Filtro por tipo
        if ($request->filled('tipo')) {
            $query->porTipo($request->tipo);
        }

        $notificacoes = $query->paginate(20)->withQueryString();

        // Formatar notificações para o frontend
        $notificacoes->getCollection()->transform(function ($notificacao) {
            return [
                'id' => $notificacao->id,
                'tipo' => $notificacao->tipo,
                'titulo' => $notificacao->titulo,
                'mensagem' => $notificacao->mensagem,
                'lida' => $notificacao->lida,
                'data_envio' => $notificacao->data_envio->format('d/m/Y H:i'),
                'data_leitura' => $notificacao->data_leitura?->format('d/m/Y H:i'),
                'agendamento' => $notificacao->agendamento ? [
                    'id' => $notificacao->agendamento->id,
                    'data_hora' => $notificacao->agendamento->scheduled_at,
                    'status' => $notificacao->agendamento->status,
                ] : null,
            ];
        });

        $stats = [
            'total' => Notificacao::where('user_id', $user->id)->count(),
            'nao_lidas' => $this->notificacaoService->contarNotificacaoesNaoLidas($user->id),
            'lidas' => Notificacao::where('user_id', $user->id)->lidas()->count(),
        ];

        return Inertia::render('notificacoes/index', [
            'notificacoes' => $notificacoes,
            'stats' => $stats,
            'filtros' => $request->only(['status', 'tipo']),
        ]);
    }

    /**
     * Marcar notificação como lida
     */
    public function marcarComoLida(Notificacao $notificacao)
    {
        // Verificar se a notificação pertence ao usuário
        if ($notificacao->user_id !== auth()->id()) {
            abort(403);
        }

        $notificacao->marcarComoLida();

        return response()->json(['success' => true]);
    }

    /**
     * Marcar todas as notificações como lidas
     */
    public function marcarTodasComoLidas()
    {
        $user = auth()->user();
        $this->notificacaoService->marcarTodasComoLidas($user->id);

        return response()->json(['success' => true]);
    }

    /**
     * Buscar notificações não lidas (para o header)
     */
    public function naoLidas()
    {
        $user = auth()->user();
        $notificacoes = $this->notificacaoService->obterNotificacaoesNaoLidas($user->id);

        $notificacoesFormatadas = $notificacoes->map(function ($notificacao) {
            return [
                'id' => $notificacao->id,
                'tipo' => $notificacao->tipo,
                'titulo' => $notificacao->titulo,
                'mensagem' => $notificacao->mensagem,
                'data_envio' => $notificacao->data_envio->diffForHumans(),
                'agendamento_id' => $notificacao->agendamento_id,
            ];
        });

        return response()->json([
            'notificacoes' => $notificacoesFormatadas,
            'total' => $notificacoes->count(),
        ]);
    }

    /**
     * Visualizar notificação específica e marcar como lida
     */
    public function show(Notificacao $notificacao)
    {
        // Verificar se a notificação pertence ao usuário
        if ($notificacao->user_id !== auth()->id()) {
            abort(403);
        }

        // Marcar como lida se ainda não foi
        if (!$notificacao->lida) {
            $notificacao->marcarComoLida();
        }

        // Determinar URL de redirecionamento baseado no tipo
        $redirectUrl = $this->getRedirectUrl($notificacao);

        if ($redirectUrl) {
            return redirect($redirectUrl);
        }

        // Se não há URL específica, redirecionar para a página de notificações
        return redirect()->route('notificacoes.index');
    }

    /**
     * Obter URL de redirecionamento baseado no tipo de notificação
     */
    private function getRedirectUrl(Notificacao $notificacao): ?string
    {
        switch ($notificacao->tipo) {
            case 'novo_agendamento':
            case 'agendamento_confirmado':
            case 'agendamento_reagendado':
            case 'agendamento_cancelado':
            case 'sessao_iniciada':
            case 'sessao_finalizada':
            case 'lembrete_sessao':
                if ($notificacao->agendamento_id) {
                    $user = auth()->user();

                    // Determinar a rota baseado no tipo de usuário
                    if ($user->isAdmin()) {
                        return route('admin.agendamentos.show', $notificacao->agendamento_id);
                    } elseif ($user->isFisioterapeuta()) {
                        return route('fisioterapeuta.agenda.show', $notificacao->agendamento_id);
                    } elseif ($user->isPaciente()) {
                        return route('paciente.agendamentos.show', $notificacao->agendamento_id);
                    }
                }
                break;
        }

        return null;
    }

    /**
     * Deletar notificação
     */
    public function destroy(Notificacao $notificacao)
    {
        // Verificar se a notificação pertence ao usuário
        if ($notificacao->user_id !== auth()->id()) {
            abort(403);
        }

        $notificacao->delete();

        return response()->json(['success' => true]);
    }
}
