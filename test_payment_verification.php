<?php

require_once 'vendor/autoload.php';

use App\Models\Pagamento;
use App\Services\UnifiedPaymentService;
use Illuminate\Support\Facades\Log;

try {
    // Inicializar a aplicação Laravel
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    echo "=== Teste de Verificação de Pagamento ===\n\n";

    // Buscar pagamentos de teste recentes
    $pagamentos = Pagamento::whereNull('assinatura_id')
        ->whereNull('agendamento_id')
        ->where('notes', 'like', '%Pagamento de Teste%')
        ->where('created_at', '>=', now()->subHours(1))
        ->orderBy('created_at', 'desc')
        ->get();

    if ($pagamentos->isEmpty()) {
        echo "Nenhum pagamento de teste encontrado na última hora.\n";
        exit;
    }

    echo "Encontrados " . $pagamentos->count() . " pagamentos de teste:\n\n";

    foreach ($pagamentos as $pagamento) {
        echo "----------------------------------------\n";
        echo "ID: " . $pagamento->id . "\n";
        echo "Status: " . $pagamento->status . "\n";
        echo "Valor: R$ " . number_format($pagamento->amount, 2, ',', '.') . "\n";
        echo "Transaction ID: " . ($pagamento->transaction_id ?? 'N/A') . "\n";
        echo "Payment ID: " . ($pagamento->payment_id ?? 'N/A') . "\n";
        echo "Preference ID: " . ($pagamento->preference_id ?? 'N/A') . "\n";
        echo "Criado em: " . $pagamento->created_at->format('d/m/Y H:i:s') . "\n";
        
        if ($pagamento->paid_at) {
            echo "Pago em: " . $pagamento->paid_at->format('d/m/Y H:i:s') . "\n";
        }
        
        echo "\n";

        // Testar verificação do pagamento
        $paymentService = app(UnifiedPaymentService::class);
        
        // Tentar com payment_id se existir
        if ($pagamento->payment_id) {
            echo "Tentando verificar com payment_id: " . $pagamento->payment_id . "\n";
            $paymentInfo = $paymentService->getPaymentInfo($pagamento->payment_id);
            
            if ($paymentInfo) {
                echo "✅ Pagamento encontrado com payment_id!\n";
                echo "Status MP: " . ($paymentInfo['status'] ?? 'N/A') . "\n";
                echo "Método: " . ($paymentInfo['payment_method_id'] ?? 'N/A') . "\n";
            } else {
                echo "❌ Pagamento não encontrado com payment_id\n";
            }
        }
        
        // Tentar com preference_id (lógica correta)
        if ($pagamento->preference_id) {
            echo "Tentando verificar com preference_id: " . $pagamento->preference_id . "\n";
            
            // Simular uma requisição para o método verificarPagamento
            $request = new \Illuminate\Http\Request(['pagamento_id' => $pagamento->id]);
            $controller = new \App\Http\Controllers\TestePagamentoController();
            
            try {
                $response = $controller->verificarPagamento($request);
                $responseData = $response->getData(true);
                
                if ($responseData['success']) {
                    echo "✅ Pagamento encontrado com preference_id!\n";
                    echo "Payment ID: " . ($responseData['pagamento']['id'] ?? 'N/A') . "\n";
                    echo "Status: " . ($responseData['pagamento']['status'] ?? 'N/A') . "\n";
                    echo "Status Detalhe: " . ($responseData['pagamento']['status_detail'] ?? 'N/A') . "\n";
                } else {
                    echo "❌ Pagamento não encontrado com preference_id\n";
                    echo "Motivo: " . $responseData['message'] . "\n";
                }
            } catch (\Exception $e) {
                echo "❌ Erro ao verificar pagamento: " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n";
    }

} catch (\Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
