import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { CheckCircle, Mail, ArrowRight } from 'lucide-react';

export default function EmailVerified() {
    return (
        <>
            <Head title="Email Verificado" />
            
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
                <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
                    {/* Ícone de sucesso */}
                    <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-6">
                        <CheckCircle className="w-12 h-12 text-green-600" />
                    </div>
                    
                    {/* Título */}
                    <h1 className="text-2xl font-bold text-gray-900 mb-4">
                        Email Verificado com Sucesso!
                    </h1>
                    
                    {/* Descrição */}
                    <div className="space-y-4 mb-8">
                        <div className="flex items-center justify-center space-x-2 text-gray-600">
                            <Mail className="w-5 h-5" />
                            <span>Seu endereço de email foi confirmado</span>
                        </div>
                        
                        <p className="text-gray-600 leading-relaxed">
                            Parabéns! Sua conta foi verificada com sucesso. 
                            Agora você pode fazer login e começar a usar o F4 Fisio.
                        </p>
                    </div>
                    
                    {/* Botão de ação */}
                    <Link
                        href="/login"
                        className="inline-flex items-center justify-center w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 space-x-2"
                    >
                        <span>Fazer Login</span>
                        <ArrowRight className="w-4 h-4" />
                    </Link>
                    
                    {/* Link secundário */}
                    <div className="mt-6 pt-6 border-t border-gray-200">
                        <p className="text-sm text-gray-500 mb-3">
                            Precisa de ajuda?
                        </p>
                        <Link
                            href="/contato"
                            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                        >
                            Entre em contato conosco
                        </Link>
                    </div>
                </div>
            </div>
        </>
    );
}
