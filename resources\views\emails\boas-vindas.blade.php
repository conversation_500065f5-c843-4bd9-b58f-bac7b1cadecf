<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bem-vindo(a) à F4 Fisio</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background-color: #ffffff;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            width: 120px;
            height: auto;
            margin: 0 auto 20px;
            display: block;
        }
        .title {
            color: #1e293b;
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 10px 0;
        }
        .subtitle {
            color: #64748b;
            font-size: 16px;
            margin: 0;
        }
        .content {
            margin: 30px 0;
        }
        .greeting {
            font-size: 18px;
            color: #1e293b;
            margin-bottom: 20px;
        }
        .message {
            color: #475569;
            margin-bottom: 25px;
            line-height: 1.7;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, oklch(0.8542 0.2851 143.0785), oklch(0.7357 0.2444 143.2345));
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .cta-button:hover {
            transform: translateY(-2px);
        }
        .info-box {
            background-color: oklch(0.9834 0.0042 236.4956);
            border-left: 4px solid oklch(0.8542 0.2851 143.0785);
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        .info-title {
            color: oklch(0.7357 0.2444 143.2345);
            font-weight: 600;
            margin-bottom: 10px;
        }
        .info-list {
            color: oklch(0.7357 0.2444 143.2345);
            margin: 0;
            padding-left: 20px;
        }
        .info-list li {
            margin-bottom: 8px;
        }
        .account-info {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .account-info-title {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .account-detail {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #64748b;
            font-size: 14px;
        }
        .footer-links {
            margin-top: 15px;
        }
        .footer-links a {
            color: oklch(0.8542 0.2851 143.0785);
            text-decoration: none;
            margin: 0 10px;
        }
        .welcome-badge {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{ config('app.url') }}/images/logo.png" alt="F4 Fisio" class="logo">
            <div class="welcome-badge">🎉 Bem-vindo(a)!</div>
            <h1 class="title">
                @if($tipoUsuario === 'fisioterapeuta')
                    Bem-vindo(a) à equipe F4 Fisio!
                @elseif($tipoUsuario === 'empresa')
                    Bem-vindo(a) à F4 Fisio!
                @else
                    Bem-vindo(a) à F4 Fisio!
                @endif
            </h1>
            <p class="subtitle">
                @if($tipoUsuario === 'fisioterapeuta')
                    Sua jornada como fisioterapeuta começa agora
                @elseif($tipoUsuario === 'empresa')
                    Sua empresa agora faz parte da nossa rede
                @else
                    Sua saúde em boas mãos
                @endif
            </p>
        </div>

        <div class="content">
            <p class="greeting">Olá, <strong>{{ $nome }}</strong>!</p>
            
            <p class="message">
                É com grande alegria que damos as boas-vindas a você como novo 
                @if($tipoUsuario === 'fisioterapeuta')
                    <strong>fisioterapeuta</strong>
                @elseif($tipoUsuario === 'empresa')
                    <strong>estabelecimento</strong>
                @else
                    <strong>paciente</strong>
                @endif
                da F4 Fisio! Estamos muito felizes em tê-lo(a) conosco.
            </p>

            <div style="text-align: center;">
                <a href="{{ $loginUrl }}" class="cta-button">
                    🚀 Acessar Minha Conta
                </a>
            </div>

            <div class="info-box">
                <div class="info-title">
                    @if($tipoUsuario === 'fisioterapeuta')
                        O que você pode fazer agora:
                    @elseif($tipoUsuario === 'empresa')
                        Próximos passos para sua empresa:
                    @else
                        Como aproveitar a plataforma:
                    @endif
                </div>
                <ul class="info-list">
                    @if($tipoUsuario === 'fisioterapeuta')
                        <li>Complete seu perfil profissional com suas especialidades</li>
                        <li>Configure sua disponibilidade e horários de atendimento</li>
                        <li>Defina seus preços e tipos de serviços oferecidos</li>
                        <li>Comece a receber agendamentos de pacientes</li>
                        <li>Gerencie sua agenda de forma prática e eficiente</li>
                    @elseif($tipoUsuario === 'empresa')
                        <li>Complete as informações do seu estabelecimento</li>
                        <li>Adicione fotos e descrição detalhada dos serviços</li>
                        <li>Configure horários de funcionamento</li>
                        <li>Gerencie profissionais e agendamentos</li>
                        <li>Ative sua visibilidade nas buscas da plataforma</li>
                    @else
                        <li>Explore profissionais qualificados na sua região</li>
                        <li>Agende consultas de forma rápida e segura</li>
                        <li>Acompanhe seu histórico de tratamentos</li>
                        <li>Avalie os serviços recebidos</li>
                        <li>Receba lembretes de consultas por email</li>
                    @endif
                </ul>
            </div>

            <p class="message">
                @if($tipoUsuario === 'fisioterapeuta')
                    Nossa plataforma foi desenvolvida para facilitar sua rotina profissional, conectando você a pacientes que precisam dos seus cuidados especializados.
                @elseif($tipoUsuario === 'empresa')
                    Agora sua empresa pode alcançar mais pacientes e oferecer um atendimento ainda mais organizado através da nossa plataforma.
                @else
                    Agora você tem acesso a uma rede de profissionais qualificados, prontos para cuidar da sua saúde com excelência e dedicação.
                @endif
            </p>

            <div class="account-info">
                <div class="account-info-title">📋 Informações da sua conta:</div>
                <div class="account-detail"><strong>Nome:</strong> {{ $nome }}</div>
                <div class="account-detail"><strong>Email:</strong> {{ $email }}</div>
                <div class="account-detail"><strong>Tipo de conta:</strong> 
                    @if($tipoUsuario === 'fisioterapeuta')
                        Fisioterapeuta
                    @elseif($tipoUsuario === 'empresa')
                        Empresa/Estabelecimento
                    @else
                        Paciente
                    @endif
                </div>
                <div class="account-detail"><strong>Data de cadastro:</strong> {{ date('d/m/Y') }}</div>
            </div>

            <p class="message">
                Se você tiver alguma dúvida ou precisar de ajuda para começar, nossa equipe de suporte está sempre disponível para ajudá-lo(a). Não hesite em entrar em contato conosco!
            </p>

            <p class="message">
                <strong>Mais uma vez, seja muito bem-vindo(a) à família F4 Fisio!</strong> 🎉
            </p>
        </div>

        <div class="footer">
            <p>Este email foi enviado automaticamente pelo sistema F4 Fisio.</p>
            <p>Estamos aqui para ajudar você a ter a melhor experiência possível.</p>
            
            <div class="footer-links">
                <a href="{{ config('app.url') }}">F4 Fisio</a> |
                <a href="{{ config('app.url') }}/contato">Suporte</a> |
                <a href="{{ config('app.url') }}/politica-privacidade">Privacidade</a>
            </div>
            
            <p style="margin-top: 20px; font-size: 12px;">
                © {{ date('Y') }} F4 Fisio. Todos os direitos reservados.<br>
                <em>Cuidando da sua saúde com excelência e dedicação.</em>
            </p>
        </div>
    </div>
</body>
</html>