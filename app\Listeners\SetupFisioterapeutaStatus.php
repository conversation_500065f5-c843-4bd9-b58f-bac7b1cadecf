<?php

namespace App\Listeners;

use App\Events\UserRegistered;
use App\Models\Fisioterapeuta;

class SetupFisioterapeutaStatus
{
    /**
     * Handle the event.
     *
     * @param  \App\Events\UserRegistered  $event
     * @return void
     */
    public function handle(UserRegistered $event)
    {
        $user = $event->user;
        
        // Se o usuário for um fisioterapeuta, cria o perfil com status 'pending'
        if ($user->role === 'fisioterapeuta') {
            $fisioterapeuta = new Fisioterapeuta();
            $fisioterapeuta->user_id = $user->id;
            $fisioterapeuta->status = 'pending'; // Status inicial como 'pendente de aprovação'
            // Campos JSON obrigatórios na migração original (não-null)
            $fisioterapeuta->specializations = [];
            $fisioterapeuta->available_areas = [];
            // Garantir estrutura inicial mínima de horários
            $fisioterapeuta->working_hours = [
                'monday' => [ 'start' => '09:00', 'end' => '18:00', 'available' => true ],
                'tuesday' => [ 'start' => '09:00', 'end' => '18:00', 'available' => true ],
                'wednesday' => [ 'start' => '09:00', 'end' => '18:00', 'available' => true ],
                'thursday' => [ 'start' => '09:00', 'end' => '18:00', 'available' => true ],
                'friday' => [ 'start' => '09:00', 'end' => '18:00', 'available' => true ],
                'saturday' => [ 'start' => '09:00', 'end' => '13:00', 'available' => false ],
                'sunday' => [ 'start' => '09:00', 'end' => '13:00', 'available' => false ],
            ];
            $fisioterapeuta->save();
        }
    }
}
