// Components
import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle, Mail, AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react';
import { FormEventHandler, useState } from 'react';

import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import AuthLayout from '@/layouts/auth-layout';

interface VerifyEmailProps {
    status?: string;
    user?: {
        name: string;
        email: string;
    };
    verificationUrl?: string;
    remaining_time?: number;
    error_message?: string;
    message?: string;
    emailContent?: {
        subject: string;
        greeting: string;
        message: string;
        buttonText: string;
        verificationUrl: string;
        securityNote: string;
    };
}

export default function VerifyEmail({ status, user, verificationUrl, remaining_time, error_message, message, emailContent }: VerifyEmailProps) {
    const { post, processing } = useForm({});
    const [showEmailPreview, setShowEmailPreview] = useState(false);

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('verification.send'));
    };

    return (
        <AuthLayout
            title="Verificar email"
            description="Por favor, verifique seu endereço de email clicando no link que acabamos de enviar para você."
        >
            <Head title="Verificação de email" />

            {status === 'verification-link-sent' && (
                <div className="mb-4 flex items-center justify-center gap-2 rounded-lg bg-green-50 p-4 text-center text-sm font-medium text-green-700">
                    <CheckCircle className="h-4 w-4" />
                    {message || 'Um novo link de verificação foi enviado para o endereço de email que você forneceu durante o cadastro.'}
                </div>
            )}

            {status === 'verification-link-error' && (
                <div className="mb-4 flex items-center justify-center gap-2 rounded-lg bg-red-50 p-4 text-center text-sm font-medium text-red-700">
                    <AlertCircle className="h-4 w-4" />
                    {message || error_message || 'Erro ao enviar email de verificação. Tente novamente em alguns instantes.'}
                </div>
            )}

            {status === 'verification-link-throttled' && (
                <div className="mb-4 flex items-center justify-center gap-2 rounded-lg bg-yellow-50 p-4 text-center text-sm font-medium text-yellow-700">
                    <AlertCircle className="h-4 w-4" />
                    {message || `Aguarde ${remaining_time} segundos antes de solicitar outro email de verificação.`}
                </div>
            )}

            {status === 'verification-link-limit-reached' && (
                <div className="mb-4 flex items-center justify-center gap-2 rounded-lg bg-orange-50 p-4 text-center text-sm font-medium text-orange-700">
                    <AlertCircle className="h-4 w-4" />
                    {message || 'Limite de emails de verificação atingido. Tente novamente em 1 hora.'}
                </div>
            )}

            {user && (
                <div className="mb-6 rounded-lg bg-blue-50 p-4">
                    <div className="flex items-center gap-2 text-blue-700">
                        <Mail className="h-4 w-4" />
                        <span className="text-sm font-medium">Email de destino: {user.email}</span>
                    </div>
                </div>
            )}

            <form onSubmit={submit} className="space-y-6 text-center">
                <Button disabled={processing} variant="secondary" size="lg">
                    {processing && <LoaderCircle className="h-4 w-4 animate-spin" />}
                    Reenviar email de verificação
                </Button>

                {emailContent && (
                    <div className="mt-6">
                        <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setShowEmailPreview(!showEmailPreview)}
                            className="mb-4"
                        >
                            {showEmailPreview ? (
                                <>
                                    <EyeOff className="h-4 w-4 mr-2" />
                                    Ocultar preview do email
                                </>
                            ) : (
                                <>
                                    <Eye className="h-4 w-4 mr-2" />
                                    Ver preview do email
                                </>
                            )}
                        </Button>

                        {showEmailPreview && (
                            <div className="mx-auto max-w-2xl rounded-lg border bg-white p-6 text-left shadow-sm">
                                <div className="mb-4 border-b pb-4">
                                    <h3 className="text-lg font-semibold text-gray-900">
                                        {emailContent.subject}
                                    </h3>
                                    <p className="text-sm text-gray-600">Para: {user?.email}</p>
                                </div>

                                <div className="space-y-4">
                                    <div className="flex items-center justify-center">
                                        <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-blue-600 text-2xl font-bold text-white">
                                            F4
                                        </div>
                                    </div>

                                    <h2 className="text-center text-2xl font-bold text-gray-900">
                                        Verificar Email
                                    </h2>
                                    
                                    <p className="text-center text-gray-600">
                                        Confirme seu email para continuar como fisioterapeuta
                                    </p>

                                    <p className="text-gray-700">{emailContent.greeting}</p>
                                    
                                    <p className="text-gray-700">{emailContent.message}</p>

                                    <div className="text-center">
                                        <div className="inline-block rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 px-8 py-3 text-white font-semibold">
                                            {emailContent.buttonText}
                                        </div>
                                    </div>

                                    <div className="rounded-lg bg-blue-50 p-4">
                                        <h4 className="font-semibold text-blue-900 mb-2">
                                            Por que verificar meu email?
                                        </h4>
                                        <ul className="text-sm text-blue-800 space-y-1">
                                            <li>• Garantir a segurança e autenticidade da sua conta</li>
                                            <li>• Receber notificações importantes sobre agendamentos</li>
                                            <li>• Prosseguir com a análise e aprovação do seu perfil profissional</li>
                                            <li>• Acessar todas as funcionalidades da plataforma</li>
                                        </ul>
                                    </div>

                                    <div className="rounded-lg bg-yellow-50 border border-yellow-200 p-4">
                                        <p className="text-sm text-yellow-800">
                                            <strong>🔒 Nota de Segurança:</strong> {emailContent.securityNote}
                                        </p>
                                    </div>

                                    {verificationUrl && (
                                        <div className="text-xs text-gray-500 break-all">
                                            <strong>Link de verificação:</strong><br />
                                            {verificationUrl}
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                )}

                <TextLink href={route('logout')} method="post" className="mx-auto block text-sm">
                    Sair
                </TextLink>
            </form>
        </AuthLayout>
    );
}
