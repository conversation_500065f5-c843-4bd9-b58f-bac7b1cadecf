<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fisioterapeutas', function (Blueprint $table) {
            $table->decimal('session_rate', 8, 2)->nullable()->after('hourly_rate');
            $table->decimal('travel_fee', 8, 2)->nullable()->after('session_rate');
            $table->json('service_rates')->nullable()->after('travel_fee'); // { avaliacao: 0, sessao: 0, teleatendimento: 0 }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fisioterapeutas', function (Blueprint $table) {
            $table->dropColumn(['session_rate', 'travel_fee', 'service_rates']);
        });
    }
};
