import { test, expect } from '@playwright/test';

test.describe('Email Functionality Test', () => {
  test('<NAME_EMAIL> and test email sending', async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:8000');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Look for login form or login button
    const loginButton = page.locator('text=Login').or(page.locator('text=Entrar')).or(page.locator('[href*="login"]')).first();
    
    if (await loginButton.isVisible()) {
      await loginButton.click();
    }
    
    // Fill in login credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', '<PERSON>@123');
    
    // Submit login form
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForLoadState('networkidle');
    
    // Check if login was successful (look for dashboard or user menu)
    const userMenu = page.locator('text=Dashboard').or(page.locator('text=Perfil')).or(page.locator('text=Configurações'));
    
    if (await userMenu.isVisible()) {
      console.log('✅ Login successful');
    } else {
      // Check for email verification message
      const verificationMessage = page.locator('text=verificação').or(page.locator('text=verification'));
      if (await verificationMessage.isVisible()) {
        console.log('❌ Email verification required');
        
        // Try to find and click "Resend verification email" button
        const resendButton = page.locator('text=Reenviar').or(page.locator('text=Resend'));
        if (await resendButton.isVisible()) {
          await resendButton.click();
          console.log('📧 Clicked resend verification email');
          
          // Wait for response
          await page.waitForTimeout(3000);
          
          // Check for success/error messages
          const successMessage = page.locator('text=enviado').or(page.locator('text=sent'));
          const errorMessage = page.locator('text=erro').or(page.locator('text=error'));
          
          if (await successMessage.isVisible()) {
            console.log('✅ Email sent successfully');
          } else if (await errorMessage.isVisible()) {
            console.log('❌ Error sending email');
            const errorText = await errorMessage.textContent();
            console.log('Error details:', errorText);
          }
        }
      }
    }
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'email-test-result.png', fullPage: true });
    
    // Log current URL and page title
    console.log('Current URL:', page.url());
    console.log('Page title:', await page.title());
  });
  
  test('Direct email sending test', async ({ page }) => {
    // Navigate to any email testing endpoint if available
    const testUrls = [
      'http://localhost:8000/test-email',
      'http://localhost:8000/admin/test-email',
      'http://localhost:8000/debug/email'
    ];
    
    for (const url of testUrls) {
      try {
        await page.goto(url);
        await page.waitForLoadState('networkidle');
        
        if (page.url().includes('test-email') || page.url().includes('email')) {
          console.log(`Found email test page at: ${url}`);
          
          // Look for email test form
          const emailInput = page.locator('input[type="email"]');
          const sendButton = page.locator('button').filter({ hasText: /send|enviar/i });
          
          if (await emailInput.isVisible() && await sendButton.isVisible()) {
            await emailInput.fill('<EMAIL>');
            await sendButton.click();
            
            await page.waitForTimeout(5000);
            
            // Check for results
            const result = await page.textContent('body');
            console.log('Email test result:', result);
          }
          break;
        }
      } catch (error) {
        console.log(`URL ${url} not accessible:`, error.message);
      }
    }
  });
});
