<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agendamentos', function (Blueprint $table) {
            // Adicionar campos para tipo de atendimento e detalhes de endereço
            $table->enum('appointment_type', ['domicilio', 'clinica', 'outro'])->default('domicilio')->after('service_type');
            $table->text('appointment_type_notes')->nullable()->after('appointment_type');

            // Detalhamento do endereço
            $table->text('address_line2')->nullable()->after('address');
            $table->text('address_city')->nullable()->after('address_line2');
            $table->text('address_state')->nullable()->after('address_city');
            $table->text('address_zip_code')->nullable()->after('address_state');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agendamentos', function (Blueprint $table) {
            $table->dropColumn(['appointment_type', 'appointment_type_notes', 'address_line2', 'address_city', 'address_state', 'address_zip_code']);
        });
    }
};
