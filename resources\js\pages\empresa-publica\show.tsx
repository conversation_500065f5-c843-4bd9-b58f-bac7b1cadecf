import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import PublicLayout from '@/layouts/public-layout';
import { Head } from '@inertiajs/react';
import { Facebook, Globe, Instagram, Mail, MapPin, MessageCircle, Phone, Star } from 'lucide-react';

interface Estabelecimento {
    id: number;
    nome: string;
    categoria: string;
    descricao?: string;
    telefone?: string;
    whatsapp: string;
    email?: string;
    endereco: string;
    cidade: string;
    estado: string;
    cep: string;
    horario_funcionamento?: Record<string, any>;
    servicos_oferecidos?: string;
    site?: string;
    instagram?: string;
    facebook?: string;
    avaliacao_media: number;
    total_avaliacoes: number;
}

interface MetaTags {
    title: string;
    description: string;
    keywords: string;
    'og:title': string;
    'og:description': string;
    'og:type': string;
    'og:url': string;
    'twitter:card': string;
    'twitter:title': string;
    'twitter:description': string;
}

interface Props {
    estabelecimento: Estabelecimento;
    structuredData: Record<string, any>;
    metaTags: MetaTags;
}

export default function EmpresaPublicaShow({ estabelecimento, structuredData, metaTags }: Props) {
    const getCategoriaLabel = (categoria: string) => {
        const labels = {
            dentista: 'Dentista',
            farmacia: 'Farmácia',
            fisioterapia: 'Fisioterapia',
            outros: 'Estabelecimento de Saúde',
        };
        return labels[categoria as keyof typeof labels] || 'Estabelecimento';
    };

    const formatWhatsAppLink = (whatsapp: string) => {
        const numero = whatsapp.replace(/\D/g, '');
        return `https://wa.me/55${numero}`;
    };

    const trackContact = async (tipo: 'whatsapp' | 'telefone') => {
        try {
            await fetch(`/api/estabelecimento/${(estabelecimento as any).slug}/contato`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    tipo: tipo,
                }),
            });
        } catch (error) {
            console.error('Erro ao rastrear contato:', error);
        }
    };

    const handleWhatsAppClick = () => {
        trackContact('whatsapp');
        // O link será aberto normalmente pelo navegador
    };

    const handleTelefoneClick = () => {
        trackContact('telefone');
        // O link será aberto normalmente pelo navegador
    };

    const renderStars = (rating: number | string) => {
        const numRating = Number(rating);
        const stars = [];
        const fullStars = Math.floor(numRating);
        const hasHalfStar = numRating % 1 !== 0;

        for (let i = 0; i < fullStars; i++) {
            stars.push(<Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />);
        }

        if (hasHalfStar) {
            stars.push(<Star key="half" className="h-4 w-4 fill-yellow-400/50 text-yellow-400" />);
        }

        const emptyStars = 5 - Math.ceil(numRating);
        for (let i = 0; i < emptyStars; i++) {
            stars.push(<Star key={`empty-${i}`} className="h-4 w-4 text-gray-300" />);
        }

        return stars;
    };

    return (
        <PublicLayout title={metaTags.title} description={metaTags.description}>
            <Head>
                <title>{metaTags.title}</title>
                <meta name="description" content={metaTags.description} />
                <meta name="keywords" content={metaTags.keywords} />

                {/* Open Graph */}
                <meta property="og:title" content={metaTags['og:title']} />
                <meta property="og:description" content={metaTags['og:description']} />
                <meta property="og:type" content={metaTags['og:type']} />
                <meta property="og:url" content={metaTags['og:url']} />

                {/* Twitter */}
                <meta name="twitter:card" content={metaTags['twitter:card']} />
                <meta name="twitter:title" content={metaTags['twitter:title']} />
                <meta name="twitter:description" content={metaTags['twitter:description']} />

                {/* Structured Data */}
                <script type="application/ld+json">{JSON.stringify(structuredData)}</script>
            </Head>

            <div className="min-h-screen bg-background">
                {/* Hero Section */}
                <section className="bg-gradient-to-b from-primary/5 to-background py-12 md:py-20">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="space-y-4 text-center">
                            <Badge variant="secondary" className="mb-4">
                                {getCategoriaLabel(estabelecimento.categoria)}
                            </Badge>
                            <h1 className="text-4xl font-bold tracking-tight md:text-5xl">{estabelecimento.nome}</h1>
                            {estabelecimento.descricao && (
                                <p className="mx-auto max-w-3xl text-xl text-muted-foreground">{estabelecimento.descricao}</p>
                            )}

                            {/* Avaliação */}
                            {estabelecimento.total_avaliacoes > 0 && (
                                <div className="mt-4 flex items-center justify-center gap-2">
                                    <div className="flex items-center gap-1">{renderStars(estabelecimento.avaliacao_media)}</div>
                                    <span className="text-sm text-muted-foreground">
                                        {Number(estabelecimento.avaliacao_media).toFixed(1)} ({estabelecimento.total_avaliacoes} avaliações)
                                    </span>
                                </div>
                            )}

                            {/* Botão de Contato Principal */}
                            <div className="mt-8">
                                <Button size="lg" asChild className="bg-green-600 hover:bg-green-700">
                                    <a
                                        href={formatWhatsAppLink(estabelecimento.whatsapp)}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        onClick={handleWhatsAppClick}
                                    >
                                        <MessageCircle className="mr-2 h-5 w-5" />
                                        Entrar em Contato via WhatsApp
                                    </a>
                                </Button>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Informações Principais */}
                <section className="py-12">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid gap-8 lg:grid-cols-3">
                            {/* Informações de Contato */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Phone className="h-5 w-5" />
                                        Contato
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center gap-3">
                                        <MessageCircle className="h-4 w-4 text-green-600" />
                                        <div>
                                            <p className="text-sm font-medium">WhatsApp</p>
                                            <a
                                                href={formatWhatsAppLink(estabelecimento.whatsapp)}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-sm text-green-600 hover:underline"
                                                onClick={handleWhatsAppClick}
                                            >
                                                {estabelecimento.whatsapp}
                                            </a>
                                        </div>
                                    </div>

                                    {estabelecimento.telefone && (
                                        <div className="flex items-center gap-3">
                                            <Phone className="h-4 w-4 text-muted-foreground" />
                                            <div>
                                                <p className="text-sm font-medium">Telefone</p>
                                                <a
                                                    href={`tel:${estabelecimento.telefone}`}
                                                    className="text-sm text-muted-foreground hover:underline"
                                                    onClick={handleTelefoneClick}
                                                >
                                                    {estabelecimento.telefone}
                                                </a>
                                            </div>
                                        </div>
                                    )}

                                    {estabelecimento.email && (
                                        <div className="flex items-center gap-3">
                                            <Mail className="h-4 w-4 text-muted-foreground" />
                                            <div>
                                                <p className="text-sm font-medium">Email</p>
                                                <a href={`mailto:${estabelecimento.email}`} className="text-sm text-muted-foreground hover:underline">
                                                    {estabelecimento.email}
                                                </a>
                                            </div>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Localização */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <MapPin className="h-5 w-5" />
                                        Localização
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2">
                                        <p className="text-sm font-medium">Endereço</p>
                                        <p className="text-sm text-muted-foreground">{estabelecimento.endereco}</p>
                                        <p className="text-sm text-muted-foreground">
                                            {estabelecimento.cidade} - {estabelecimento.estado}
                                        </p>
                                        <p className="text-sm text-muted-foreground">CEP: {estabelecimento.cep}</p>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Redes Sociais */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Globe className="h-5 w-5" />
                                        Redes Sociais
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {estabelecimento.site && (
                                        <div className="flex items-center gap-3">
                                            <Globe className="h-4 w-4 text-muted-foreground" />
                                            <a
                                                href={estabelecimento.site}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-sm text-primary hover:underline"
                                            >
                                                Site Oficial
                                            </a>
                                        </div>
                                    )}

                                    {estabelecimento.instagram && (
                                        <div className="flex items-center gap-3">
                                            <Instagram className="h-4 w-4 text-pink-600" />
                                            <a
                                                href={`https://instagram.com/${estabelecimento.instagram.replace('@', '')}`}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-sm text-pink-600 hover:underline"
                                            >
                                                {estabelecimento.instagram}
                                            </a>
                                        </div>
                                    )}

                                    {estabelecimento.facebook && (
                                        <div className="flex items-center gap-3">
                                            <Facebook className="h-4 w-4 text-blue-600" />
                                            <a
                                                href={
                                                    estabelecimento.facebook.startsWith('http')
                                                        ? estabelecimento.facebook
                                                        : `https://facebook.com/${estabelecimento.facebook}`
                                                }
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-sm text-blue-600 hover:underline"
                                            >
                                                Facebook
                                            </a>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Serviços Oferecidos */}
                        {estabelecimento.servicos_oferecidos && (
                            <Card className="mt-8">
                                <CardHeader>
                                    <CardTitle>Serviços Oferecidos</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="whitespace-pre-line text-muted-foreground">{estabelecimento.servicos_oferecidos}</p>
                                </CardContent>
                            </Card>
                        )}

                        {/* Call to Action Final */}
                        <div className="mt-12 text-center">
                            <Card className="border-primary/20 bg-primary/5">
                                <CardContent className="py-8">
                                    <h3 className="mb-4 text-2xl font-bold">Entre em Contato Agora</h3>
                                    <p className="mb-6 text-muted-foreground">
                                        Agende seu atendimento ou tire suas dúvidas diretamente pelo WhatsApp
                                    </p>
                                    <Button size="lg" asChild className="bg-green-600 hover:bg-green-700">
                                        <a
                                            href={formatWhatsAppLink(estabelecimento.whatsapp)}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            onClick={handleWhatsAppClick}
                                        >
                                            <MessageCircle className="mr-2 h-5 w-5" />
                                            Conversar no WhatsApp
                                        </a>
                                    </Button>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>
            </div>
        </PublicLayout>
    );
}
