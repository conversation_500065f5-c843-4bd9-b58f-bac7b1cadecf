<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Agendamento;
use App\Models\RelatorioSessao;
use App\Models\Avaliacao;
use App\Models\OrientacaoDomiciliar;
use App\Models\Prescricao;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Carbon\Carbon;

class HistoricoController extends Controller
{
    /**
     * Display the patient's treatment history
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Filtros
        $periodo = $request->get('periodo', '6_meses'); // 1_mes, 3_meses, 6_meses, 1_ano, todos
        $tipo = $request->get('tipo', 'todos'); // sessoes, avaliacoes, todos
        $status = $request->get('status', 'todos'); // concluido, cancelado, todos
        
        // Query base para agendamentos
        $agendamentosQuery = Agendamento::where('paciente_id', $user->id)
            ->with(['fisioterapeuta.user', 'relatorioSessao'])
            ->orderBy('scheduled_at', 'desc');
            
        // Aplicar filtro de período
        if ($periodo !== 'todos') {
            $dataInicio = match($periodo) {
                '1_mes' => Carbon::now()->subMonth(),
                '3_meses' => Carbon::now()->subMonths(3),
                '6_meses' => Carbon::now()->subMonths(6),
                '1_ano' => Carbon::now()->subYear(),
                default => Carbon::now()->subMonths(6)
            };
            
            $agendamentosQuery->where('scheduled_at', '>=', $dataInicio);
        }
        
        // Aplicar filtro de status
        if ($status !== 'todos') {
            $agendamentosQuery->where('status', $status);
        }
        
        // Aplicar filtro de tipo (se necessário, pode ser expandido)
        if ($tipo === 'sessoes') {
            $agendamentosQuery->where('tipo', 'sessao');
        } elseif ($tipo === 'avaliacoes') {
            $agendamentosQuery->where('tipo', 'avaliacao');
        }
        
        $agendamentos = $agendamentosQuery->paginate(15);
        
        // Estatísticas do histórico
        $stats = [
            'totalSessoes' => Agendamento::where('paciente_id', $user->id)
                ->where('status', 'concluido')
                ->count(),
            'sessoesUltimoMes' => Agendamento::where('paciente_id', $user->id)
                ->where('status', 'concluido')
                ->where('scheduled_at', '>=', Carbon::now()->subMonth())
                ->count(),
            'avaliacoesRealizadas' => Avaliacao::where('paciente_id', $user->id)
                ->count(),
            'tempoTratamento' => $this->calcularTempoTratamento($user->id),
        ];
        
        // Evolução mensal (últimos 6 meses)
        $evolucaoMensal = $this->getEvolucaoMensal($user->id);
        
        // Avaliações recentes
        $avaliacoes = Avaliacao::where('paciente_id', $user->id)
            ->with('fisioterapeuta.user')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Prescrições recebidas (dados de exemplo para teste)
        $prescricoes = collect([
            (object) [
                'id' => 1,
                'titulo' => 'Prescrição para Dor nas Costas',
                'descricao' => 'Tratamento para alívio da dor lombar',
                'medicamentos' => 'Ibuprofeno 600mg - 1 comprimido de 8/8h por 5 dias',
                'exercicios' => 'Alongamento da coluna - 3x ao dia, 10 repetições cada',
                'cuidados_especiais' => 'Evitar carregar peso, manter postura correta',
                'data_inicio' => now()->format('Y-m-d'),
                'data_fim' => null,
                'status' => 'ativa',
                'created_at' => now()->format('Y-m-d H:i:s'),
                'fisioterapeuta' => ['name' => 'Dr. João Silva']
            ]
        ]);

        // Orientações domiciliares (dados de exemplo para teste)
        $orientacoes = collect([
            (object) [
                'id' => 1,
                'titulo' => 'Exercícios Domiciliares para Fortalecimento',
                'descricao' => 'Rotina de exercícios para fortalecer a musculatura das costas',
                'exercicios_recomendados' => "Prancha - 30 segundos, 3 séries\nPonte - 15 repetições, 3 séries\nGato-vaca - 10 repetições, 2 séries",
                'cuidados_posturais' => 'Manter coluna ereta ao sentar, usar apoio lombar',
                'atividades_evitar' => 'Levantar peso acima de 5kg, permanecer muito tempo na mesma posição',
                'dicas_gerais' => 'Fazer pausas a cada hora, aplicar calor local por 15 minutos',
                'frequencia_dias' => 3,
                'horario_recomendado' => '08:00',
                'prioridade' => 'alta',
                'created_at' => now()->format('Y-m-d H:i:s'),
                'fisioterapeuta' => ['name' => 'Dr. João Silva']
            ]
        ]);
        
        return Inertia::render('paciente/historico', [
            'agendamentos' => $agendamentos,
            'stats' => $stats,
            'evolucaoMensal' => $evolucaoMensal,
            'avaliacoes' => $avaliacoes,
            'prescricoes' => $prescricoes,
            'orientacoes' => $orientacoes,
            'filtros' => [
                'periodo' => $periodo,
                'tipo' => $tipo,
                'status' => $status,
            ],
        ]);
    }
    
    /**
     * Show detailed session report
     */
    public function show(Agendamento $agendamento)
    {
        $user = Auth::user();

        // Verificar se o agendamento pertence ao usuário
        if ($agendamento->paciente_id !== $user->id) {
            abort(403, 'Acesso negado');
        }

        $agendamento->load([
            'fisioterapeuta.user',
            'relatorioSessao',
            'avaliacao'
        ]);

        // Formatar dados para o frontend
        $agendamentoFormatado = [
            'id' => $agendamento->id,
            'data_agendamento' => $agendamento->scheduled_at ? $agendamento->scheduled_at->format('Y-m-d') : null,
            'horario' => $agendamento->scheduled_at ? $agendamento->scheduled_at->format('H:i:s') : null,
            'status' => $agendamento->status,
            'tipo' => 'sessao', // Assumindo que é sempre sessão por enquanto
            'observacoes' => $agendamento->notes,
            'fisioterapeuta' => [
                'id' => $agendamento->fisioterapeuta->id ?? null,
                'user' => [
                    'name' => $agendamento->fisioterapeuta->user->name ?? 'Fisioterapeuta',
                    'email' => $agendamento->fisioterapeuta->user->email ?? '',
                    'phone' => $agendamento->fisioterapeuta->user->phone ?? '',
                ],
                'especialidades' => ['Ortopédica', 'Esportiva'], // Dados fictícios por enquanto
                'crefito' => $agendamento->fisioterapeuta->crefito ?? 'CREFITO-3/123456-F',
            ],
            'relatorioSessao' => $agendamento->relatorioSessao,
            'avaliacao' => $agendamento->avaliacao,
            'created_at' => $agendamento->created_at->toISOString(),
            'updated_at' => $agendamento->updated_at->toISOString(),
        ];

        return Inertia::render('paciente/historico/show', [
            'agendamento' => $agendamentoFormatado,
        ]);
    }
    
    /**
     * Calculate treatment duration
     */
    private function calcularTempoTratamento($pacienteId)
    {
        $primeiroAgendamento = Agendamento::where('paciente_id', $pacienteId)
            ->where('status', 'concluido')
            ->orderBy('scheduled_at', 'asc')
            ->first();
            
        if (!$primeiroAgendamento) {
            return 0;
        }
        
        return Carbon::parse($primeiroAgendamento->scheduled_at)
            ->diffInMonths(Carbon::now());
    }
    
    /**
     * Get monthly evolution data
     */
    private function getEvolucaoMensal($pacienteId)
    {
        $evolucao = [];
        
        for ($i = 5; $i >= 0; $i--) {
            $mes = Carbon::now()->subMonths($i);
            $inicioMes = $mes->copy()->startOfMonth();
            $fimMes = $mes->copy()->endOfMonth();
            
            $sessoes = Agendamento::where('paciente_id', $pacienteId)
                ->where('status', 'concluido')
                ->whereBetween('scheduled_at', [$inicioMes, $fimMes])
                ->count();
                
            $evolucao[] = [
                'mes' => $mes->format('M/Y'),
                'sessoes' => $sessoes,
            ];
        }
        
        return $evolucao;
    }
    
    /**
     * Export treatment history
     */
    public function export(Request $request)
    {
        $user = Auth::user();

        // Buscar todos os agendamentos do paciente
        $agendamentos = Agendamento::where('paciente_id', $user->id)
            ->with(['fisioterapeuta.user', 'relatorioSessao', 'avaliacao'])
            ->orderBy('scheduled_at', 'desc')
            ->get();

        // Preparar dados para exportação
        $data = [
            ['Data', 'Horário', 'Tipo', 'Fisioterapeuta', 'Status', 'Observações', 'Valor']
        ];

        foreach ($agendamentos as $agendamento) {
            $data[] = [
                \Carbon\Carbon::parse($agendamento->scheduled_at)->format('d/m/Y'),
                \Carbon\Carbon::parse($agendamento->horario)->format('H:i'),
                $agendamento->tipo === 'sessao' ? 'Sessão' : 'Avaliação',
                $agendamento->fisioterapeuta->user->name ?? 'N/A',
                $this->getStatusLabel($agendamento->status),
                $agendamento->relatorioSessao->observacoes ?? 'N/A',
                'R$ ' . number_format($agendamento->valor ?? 0, 2, ',', '.')
            ];
        }

        // Gerar CSV
        $filename = 'historico_tratamento_' . $user->name . '_' . now()->format('Y-m-d') . '.csv';

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // BOM para UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            foreach ($data as $row) {
                fputcsv($file, $row, ';');
            }

            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Get status label for display
     */
    private function getStatusLabel($status)
    {
        $labels = [
            'agendado' => 'Agendado',
            'confirmado' => 'Confirmado',
            'em_andamento' => 'Em Andamento',
            'concluido' => 'Concluído',
            'cancelado' => 'Cancelado',
            'nao_compareceu' => 'Não Compareceu'
        ];

        return $labels[$status] ?? ucfirst($status);
    }
}
