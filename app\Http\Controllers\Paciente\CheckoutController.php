<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Assinatura;
use App\Models\Plano;
use App\Models\Pagamento;
use App\Services\MercadoPagoService;
use Illuminate\Support\Facades\Log;

class CheckoutController extends Controller
{
    protected $mercadoPagoService;

    public function __construct(MercadoPagoService $mercadoPagoService)
    {
        $this->mercadoPagoService = $mercadoPagoService;
    }

    /**
     * Show checkout page
     */
    public function index()
    {
        $user = \Illuminate\Support\Facades\Auth::user();

        // Verificar se o usuário completou as etapas anteriores
        if (!$user->onboarding_completed) {
            return redirect()->route('paciente.onboarding')
                ->with('warning', 'Complete seu perfil médico primeiro.');
        }

        if (!$user->plan_selected) {
            return redirect()->route('paciente.pagamentos.index')
                ->with('warning', 'Selecione e finalize um pagamento primeiro.');
        }

        // Buscar assinatura mais recente do usuário
        $assinatura = Assinatura::where('user_id', $user->id)
            ->with('plano')
            ->latest()
            ->first();

        if (!$assinatura) {
            return redirect()->route('paciente.pagamentos.index')
                ->with('error', 'Nenhuma assinatura encontrada. Inicie um pagamento.');
        }

        return Inertia::render('paciente/checkout', [
            'user' => $user,
            'assinatura' => $assinatura,
            'plano' => $assinatura->plano,
        ]);
    }

    /**
     * Process checkout
     */
    public function process(Request $request)
    {
        $user = \Illuminate\Support\Facades\Auth::user();

        $request->validate([
            'terms_accepted' => 'required|accepted',
        ]);

        // Buscar assinatura
        $assinatura = Assinatura::where('user_id', $user->id)
            ->latest()
            ->first();

        if (!$assinatura) {
            return back()->with('error', 'Assinatura não encontrada.');
        }

        // Processar pagamento via Mercado Pago (sempre real)
        return $this->processRealPayment($user, $assinatura);
    }



    /**
     * Process real payment
     */
    private function processRealPayment($user, $assinatura)
    {
        Log::info('🔄 [CHECKOUT] Iniciando processamento de pagamento', [
            'user_id' => $user->id,
            'assinatura_id' => $assinatura->id,
            'plano_price' => $assinatura->plano->price
        ]);

        // Criar pagamento no banco de dados
        $pagamento = Pagamento::create([
            'assinatura_id' => $assinatura->id,
            'amount' => $assinatura->plano->price,
            'status' => 'pendente',
            'due_date' => now(),
            'method' => null, // Será atualizado pelo webhook com o método correto
        ]);

        // Preparar dados para assinatura recorrente no Mercado Pago
        $subscriptionData = [
            'title' => 'Assinatura ' . $assinatura->plano->name . ' - F4 Fisio',
            'amount' => $assinatura->plano->price,
            'external_reference' => $pagamento->id,
            'payer' => [
                'name' => $user->name,
                'email' => $user->email,
            ],
            'success_url' => route('paciente.checkout.success') . '?payment_id=' . $pagamento->id . '&external_reference=' . $pagamento->id,
            'failure_url' => route('paciente.checkout.success') . '?payment_id=' . $pagamento->id . '&external_reference=' . $pagamento->id . '&status=failed',
            'pending_url' => route('paciente.checkout.success') . '?payment_id=' . $pagamento->id . '&external_reference=' . $pagamento->id . '&status=pending',
            'notification_url' => route('mercadopago.webhook'),
        ];

        // Criar assinatura recorrente no Mercado Pago
        Log::info('🔄 [CHECKOUT] Chamando MercadoPagoService', [
            'subscription_data' => $subscriptionData
        ]);

        $subscription = $this->mercadoPagoService->createSubscription($subscriptionData);

        Log::info('🔄 [CHECKOUT] Resposta do MercadoPagoService', [
            'subscription_response' => $subscription
        ]);

        if (!$subscription['success']) {
            Log::error('❌ [CHECKOUT] Falha na criação da assinatura', [
                'subscription_response' => $subscription
            ]);

            if (isset($subscription['redirect_whatsapp']) && $subscription['redirect_whatsapp']) {
                // Redirecionar para WhatsApp se MP não estiver configurado
                $whatsappUrl = config('app.whatsapp_url', 'https://wa.me/5511978196207');
                $message = urlencode('Olá! Gostaria de realizar o pagamento da minha assinatura.');
                Log::info('🔄 [CHECKOUT] Redirecionando para WhatsApp', [
                    'whatsapp_url' => $whatsappUrl . '?text=' . $message
                ]);
                return redirect($whatsappUrl . '?text=' . $message);
            }

            return back()->with('error', $subscription['message']);
        }

        // Atualizar pagamento e assinatura com dados da assinatura MP
        $pagamento->update([
            'transaction_id' => $subscription['subscription_id'],
            'gateway_response' => $subscription,
        ]);

        // Atualizar assinatura com ID do Mercado Pago
        $assinatura->update([
            'mercadopago_subscription_id' => $subscription['subscription_id'],
        ]);

        // Redirecionar para o checkout de assinatura do Mercado Pago
        $redirectUrl = $subscription['init_point'];

        Log::info('🔄 [CHECKOUT] Redirecionando para assinatura MP', [
            'user_id' => $user->id,
            'assinatura_id' => $assinatura->id,
            'subscription_id' => $subscription['subscription_id'],
            'checkout_url' => $redirectUrl,
        ]);

        // Retornar HTML com JavaScript para redirecionamento externo
        $html = '<!DOCTYPE html>
        <html>
        <head>
            <title>Redirecionando para Mercado Pago...</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                .loader { border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 20px auto; }
                @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
            </style>
        </head>
        <body>
            <h2>Redirecionando para Mercado Pago...</h2>
            <div class="loader"></div>
            <p>Aguarde, você será redirecionado automaticamente.</p>
            <script>
                setTimeout(function() {
                    window.location.href = "' . $redirectUrl . '";
                }, 1000);
            </script>
        </body>
        </html>';

        return response($html)->header('Content-Type', 'text/html');
    }



    /**
     * Show success page
     */
    public function success(Request $request)
    {
        $paymentId = $request->get('payment_id');
        $externalReference = $request->get('external_reference');

        // Se não há usuário logado, mostrar página de sucesso genérica
        if (!\Illuminate\Support\Facades\Auth::check()) {
            return Inertia::render('mercadopago/success', [
                'payment_id' => $paymentId,
                'external_reference' => $externalReference,
                'message' => 'Pagamento aprovado com sucesso!'
            ]);
        }

        $user = \Illuminate\Support\Facades\Auth::user();

        // Validar pagamento via API do Mercado Pago
        if ($paymentId) {
            $this->validatePaymentStatus($paymentId);
        } else {
            // Em desenvolvimento, se não há payment_id específico,
            // considerar como aprovado para facilitar testes
            if (app()->environment('local', 'development')) {
                $this->approveLatestPendingPayment($user);
            }
        }

        // Marcar checkout como concluído
        if ($user) {
            $user->update([
                'checkout_completed' => true,
                'checkout_completed_at' => now(),
            ]);
        }

        return Inertia::render('paciente/checkout-success', [
            'user' => $user,
            'payment_id' => $paymentId,
        ]);
    }

    /**
     * Show failure page
     */
    public function failure(Request $request)
    {
        $paymentId = $request->get('payment_id');
        $externalReference = $request->get('external_reference');

        return Inertia::render('mercadopago/failure', [
            'payment_id' => $paymentId,
            'external_reference' => $externalReference,
            'message' => 'Pagamento não foi aprovado. Tente novamente.'
        ]);
    }

    /**
     * Show pending page
     */
    public function pending(Request $request)
    {
        $paymentId = $request->get('payment_id');
        $externalReference = $request->get('external_reference');

        return Inertia::render('mercadopago/pending', [
            'payment_id' => $paymentId,
            'external_reference' => $externalReference,
            'message' => 'Pagamento está sendo processado. Aguarde a confirmação.'
        ]);
    }

    /**
     * Validate payment status with Mercado Pago API
     */
    private function validatePaymentStatus($paymentId)
    {
        // First try to find by payment ID (our database record)
        $pagamento = Pagamento::find($paymentId);
        
        // If not found, try to find by transaction_id (Mercado Pago ID)
        if (!$pagamento) {
            $pagamento = Pagamento::where('transaction_id', $paymentId)->first();
        }
        
        if (!$pagamento) {
            Log::warning('Payment not found in database', [
                'payment_id' => $paymentId,
                'search_type' => 'both_id_and_transaction_id'
            ]);
            return;
        }

        // Buscar informações do pagamento no Mercado Pago
        $mpPaymentId = $pagamento->transaction_id ?: $paymentId;
        $paymentInfo = $this->mercadoPagoService->getPayment($mpPaymentId);

        if ($paymentInfo && isset($paymentInfo['status'])) {
            $status = $paymentInfo['status'];

            // Mapear status do Mercado Pago para nosso sistema
            $newStatus = match($status) {
                'approved' => 'pago',
                'pending', 'in_process' => 'pendente',
                'rejected', 'cancelled' => 'falhou',
                default => $pagamento->status
            };

            if ($newStatus !== $pagamento->status) {
                $pagamento->update(['status' => $newStatus]);

                // Se foi aprovado, ativar assinatura
                if ($newStatus === 'pago') {
                    $assinatura = $pagamento->assinatura;
                    if ($assinatura) {
                        $assinatura->update([
                            'status' => 'ativa',
                            'start_date' => now(),
                            'current_period_start' => now(),
                            'current_period_end' => now()->addMonth(),
                        ]);

                        // Marcar usuário como tendo assinatura e checkout concluído
                        if ($assinatura->user) {
                            $assinatura->user->update([
                                'has_subscription' => true,
                                'checkout_completed' => true,
                                'checkout_completed_at' => now(),
                            ]);

                            Log::info('✅ [CHECKOUT] Pagamento aprovado e usuário atualizado', [
                                'user_id' => $assinatura->user->id,
                                'pagamento_id' => $pagamento->id,
                                'assinatura_id' => $assinatura->id,
                                'status' => $newStatus
                            ]);
                        }
                    }
                }
            }
        }
    }

    /**
     * Approve latest pending payment for development
     */
    private function approveLatestPendingPayment($user)
    {
        $pagamento = Pagamento::whereHas('assinatura', function ($q) use ($user) {
            $q->where('user_id', $user->id);
        })->where('status', 'pendente')
          ->latest()
          ->first();

        if ($pagamento) {
            $pagamento->update([
                'status' => 'pago',
                'paid_at' => now(),
            ]);

            // Ativar assinatura
            $assinatura = $pagamento->assinatura;
            $assinatura->update([
                'status' => 'ativa',
                'start_date' => now(),
                'current_period_start' => now(),
                'current_period_end' => now()->addMonth(),
            ]);

            // Marcar usuário como tendo assinatura e checkout concluído
            $user->update([
                'has_subscription' => true,
                'checkout_completed' => true,
                'checkout_completed_at' => now(),
            ]);

            Log::info('💳 [DEV] Pagamento aprovado automaticamente', [
                'user_id' => $user->id,
                'pagamento_id' => $pagamento->id,
                'assinatura_id' => $assinatura->id,
                'checkout_completed' => true,
            ]);
        }
    }

    /**
     * Confirm and redirect to dashboard
     */
    public function confirm()
    {
        return redirect()->route('paciente.dashboard')
            ->with('success', 'Bem-vindo à plataforma! Seu plano está ativo.');
    }
}
