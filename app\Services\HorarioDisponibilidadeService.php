<?php

namespace App\Services;

use App\Models\User;
use App\Models\HorarioBase;
use App\Models\HorarioExcecao;
use App\Models\Feriado;
use App\Models\MedicoFeriadosConfig;
use App\Models\Agendamento;
use Carbon\Carbon;

class HorarioDisponibilidadeService
{
    /**
     * Calcular horários disponíveis para um fisioterapeuta em uma data específica
     */
    public function calcularHorariosDisponiveis($fisioterapeutaId, $data)
    {
        $dataCarbon = Carbon::parse($data);
        $diaSemana = $dataCarbon->dayOfWeek;
        
        // 1. Verificar se é feriado e se o médico trabalha
        if ($this->isFeriadoEMedicoNaoTrabalha($fisioterapeutaId, $dataCarbon)) {
            return [];
        }

        // 2. Buscar configurações de horário para esta data (ordem de prioridade)
        $horariosDisponiveis = $this->obterHorariosParaData($fisioterapeutaId, $dataCarbon, $diaSemana);

        if (empty($horariosDisponiveis)) {
            return [];
        }

        // 3. Remover horários já agendados
        $horariosDisponiveis = $this->removerHorariosAgendados($fisioterapeutaId, $dataCarbon, $horariosDisponiveis);

        // 4. Aplicar bloqueios específicos (sistema atual de Disponibilidade)
        $horariosDisponiveis = $this->aplicarBloqueiosEspecificos($fisioterapeutaId, $dataCarbon, $horariosDisponiveis);

        return $horariosDisponiveis;
    }

    /**
     * Verificar se é feriado e se o médico não trabalha
     */
    private function isFeriadoEMedicoNaoTrabalha($fisioterapeutaId, Carbon $data)
    {
        // Buscar feriados para esta data
        $feriados = Feriado::ativos()
            ->porData($data)
            ->get();

        if ($feriados->isEmpty()) {
            return false;
        }

        // Buscar configuração de feriados do médico
        $configFeriados = MedicoFeriadosConfig::where('fisioterapeuta_id', $fisioterapeutaId)->first();

        if (!$configFeriados) {
            // Se não tem configuração, assume que não trabalha em feriados
            return true;
        }

        // Verificar se trabalha em algum dos feriados desta data
        foreach ($feriados as $feriado) {
            if (!$configFeriados->trabalhaEmFeriado($feriado)) {
                return true; // Não trabalha neste feriado
            }
        }

        return false; // Trabalha em todos os feriados desta data
    }

    /**
     * Obter horários para uma data específica (seguindo ordem de prioridade)
     */
    private function obterHorariosParaData($fisioterapeutaId, Carbon $data, $diaSemana)
    {
        // Prioridade 1: Exceções de data específica
        $excecaoEspecifica = HorarioExcecao::porFisioterapeuta($fisioterapeutaId)
            ->ativas()
            ->porTipo('data_especifica')
            ->where('data_inicio', $data->format('Y-m-d'))
            ->first();

        if ($excecaoEspecifica) {
            return $this->processarExcecao($excecaoEspecifica, $data);
        }

        // Prioridade 2: Exceções de período (semana/mês)
        $excecaoPeriodo = HorarioExcecao::porFisioterapeuta($fisioterapeutaId)
            ->ativas()
            ->whereIn('tipo', ['semana', 'mes', 'periodo_personalizado'])
            ->porPeriodo($data, $data)
            ->where(function ($query) use ($diaSemana) {
                $query->whereNull('dia_semana')
                      ->orWhere('dia_semana', $diaSemana);
            })
            ->first();

        if ($excecaoPeriodo) {
            return $this->processarExcecao($excecaoPeriodo, $data);
        }

        // Prioridade 3: Configuração base para o dia da semana
        $horariosBase = HorarioBase::porFisioterapeuta($fisioterapeutaId)
            ->ativos()
            ->porDiaSemana($diaSemana)
            ->orderBy('hora_inicio')
            ->get();

        return $this->processarHorariosBase($horariosBase);
    }

    /**
     * Processar exceção de horário
     */
    private function processarExcecao(HorarioExcecao $excecao, Carbon $data)
    {
        if (!$excecao->aplicaParaData($data)) {
            return [];
        }

        if ($excecao->acao === 'indisponivel') {
            return [];
        }

        if (!$excecao->hora_inicio || !$excecao->hora_fim) {
            // Se não especifica horário, retorna vazio (dia indisponível) ou usa configuração base
            return $excecao->acao === 'disponivel' ? [] : [];
        }

        return $this->gerarHorarios($excecao->hora_inicio, $excecao->hora_fim);
    }

    /**
     * Processar horários base
     */
    private function processarHorariosBase($horariosBase)
    {
        $todosHorarios = [];

        foreach ($horariosBase as $horario) {
            $horarios = $this->gerarHorarios($horario->hora_inicio, $horario->hora_fim);
            $todosHorarios = array_merge($todosHorarios, $horarios);
        }

        return array_unique($todosHorarios);
    }

    /**
     * Gerar lista de horários disponíveis em intervalos de 60 minutos (1 hora)
     */
    private function gerarHorarios($horaInicio, $horaFim, $intervalo = 60)
    {
        $horarios = [];
        $inicio = Carbon::parse($horaInicio);
        $fim = Carbon::parse($horaFim);

        $atual = $inicio->copy();
        while ($atual->lt($fim)) {
            $horarios[] = $atual->format('H:i');
            $atual->addMinutes($intervalo);
        }

        return $horarios;
    }

    /**
     * Remover horários já agendados
     */
    private function removerHorariosAgendados($fisioterapeutaId, Carbon $data, array $horarios)
    {
        $agendamentos = Agendamento::where('fisioterapeuta_id', $fisioterapeutaId)
            ->whereDate('scheduled_at', $data)
            ->whereNotIn('status', ['cancelado'])
            ->get();

        $horariosOcupados = $agendamentos->map(function ($agendamento) {
            return Carbon::parse($agendamento->scheduled_at)->format('H:i');
        })->toArray();

        return array_values(array_diff($horarios, $horariosOcupados));
    }

    /**
     * Aplicar bloqueios específicos do sistema atual
     */
    private function aplicarBloqueiosEspecificos($fisioterapeutaId, Carbon $data, array $horarios)
    {
        // Aqui mantemos a compatibilidade com o sistema atual de Disponibilidade
        // para bloqueios específicos que não se encaixam no novo sistema
        
        // Por enquanto, retorna os horários sem modificação
        // Pode ser expandido para integrar com o sistema atual se necessário
        
        return $horarios;
    }

    /**
     * Verificar se um fisioterapeuta está disponível em uma data/hora específica
     */
    public function verificarDisponibilidade($fisioterapeutaId, $data, $horario)
    {
        $horariosDisponiveis = $this->calcularHorariosDisponiveis($fisioterapeutaId, $data);
        return in_array($horario, $horariosDisponiveis);
    }

    /**
     * Obter próximos horários disponíveis para um fisioterapeuta
     */
    public function obterProximosHorarios($fisioterapeutaId, $dias = 7)
    {
        $horarios = [];
        $dataInicio = Carbon::now()->addDay();
        $dataFim = $dataInicio->copy()->addDays($dias);

        $dataAtual = $dataInicio->copy();
        while ($dataAtual->lte($dataFim)) {
            $horariosData = $this->calcularHorariosDisponiveis($fisioterapeutaId, $dataAtual);
            
            if (!empty($horariosData)) {
                $horarios[$dataAtual->format('Y-m-d')] = [
                    'data' => $dataAtual->format('d/m/Y'),
                    'dia_semana' => $dataAtual->locale('pt_BR')->dayName,
                    'horarios' => $horariosData,
                ];
            }

            $dataAtual->addDay();
        }

        return $horarios;
    }
}
