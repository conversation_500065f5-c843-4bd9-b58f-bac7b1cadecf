<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Assinatura extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plano_id',
        'status',
        'cancel_at_period_end',
        'start_date',
        'end_date',
        'sessions_used',
        'current_period_start',
        'current_period_end',
        'monthly_price',
        'mercadopago_subscription_id',
        'scheduled_new_plano_id',
        'scheduled_change_date',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'current_period_start' => 'datetime',
        'current_period_end' => 'datetime',
        'monthly_price' => 'decimal:2',
        'cancel_at_period_end' => 'boolean',
        'scheduled_new_plano_id' => 'integer',
        'scheduled_change_date' => 'date',
    ];

    // Relacionamentos
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function plano()
    {
        return $this->belongsTo(Plano::class);
    }

    public function agendamentos()
    {
        return $this->hasMany(Agendamento::class);
    }

    public function pagamentos()
    {
        return $this->hasMany(Pagamento::class);
    }

    // Scopes
    public function scopeAtivas($query)
    {
        return $query->where('status', 'ativa');
    }

    // Métodos auxiliares
    public function canScheduleSession()
    {
        return $this->status === 'ativa' &&
               $this->sessions_used < $this->plano->sessions_per_month;
    }

    public function getRemainingSessionsAttribute()
    {
        return $this->plano->sessions_per_month - $this->sessions_used;
    }
}
