import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { type ReactNode } from 'react';

interface PacienteLayoutProps {
    children: ReactNode;
    breadcrumbs?: BreadcrumbItem[];
}

export default function PacienteLayout({ children, breadcrumbs, ...props }: PacienteLayoutProps) {
    return (
        <AppLayout breadcrumbs={breadcrumbs} {...props}>
            {children}
        </AppLayout>
    );
}
