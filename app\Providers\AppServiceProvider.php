<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\URL;
use App\Services\UnifiedPaymentService;
use App\Services\MercadoPagoService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Registrar o UnifiedPaymentService
        $this->app->singleton(UnifiedPaymentService::class, function ($app) {
            return new UnifiedPaymentService($app->make(MercadoPagoService::class));
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Não forçar URLs globalmente para não interferir no envio de emails
        // Apenas forçar HTTPS em produção para segurança geral
        if (config('app.env') === 'production') {
            URL::forceScheme('https');
            $this->app['request']->server->set('HTTPS', 'on');
        }
    }
}
