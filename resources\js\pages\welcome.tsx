import BenefitsSection from '@/components/benefits-section';
import FAQsSection from '@/components/faqs-section';
import HeroSection from '@/components/hero-section';
import ServicesSection from '@/components/services-section';
import TestimonialsSection from '@/components/testimonials-section';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import PublicLayout from '@/layouts/public-layout';
import { Building2, Check, Mail, MapPin, MessageCircle, User } from 'lucide-react';

export default function Welcome() {
    return (
        <PublicLayout
            title="F4 Fisio - Fisioterapia Domiciliar"
            description="Fisioterapia domiciliar de qualidade. Cuidado personalizado no conforto da sua casa."
        >
            {/* 1. Hero Section - Gradient background */}
            <HeroSection />

            {/* 2. Serviços Section - Background claro */}
            <ServicesSection />

            {/* 3. Como Funciona Section - Background escuro para contraste */}
            <section id="como-funciona" className="bg-muted/30 py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Como Funciona Nossa Fisioterapia Domiciliar</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Processo profissional e seguro para seu tratamento em casa
                        </p>
                    </div>

                    <div className="mt-16 grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
                        {/* Passo 1 - Contato */}
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                <MessageCircle className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Contato Inicial</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Entre em contato via WhatsApp ou telefone. Coletamos informações sobre sua condição e necessidades específicas.
                            </p>
                        </div>

                        {/* Passo 2 - Avaliação Domiciliar */}
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                <MapPin className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Avaliação em Casa</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Fisioterapeuta credenciado realiza avaliação completa no seu domicílio, com anamnese detalhada e testes funcionais.
                            </p>
                        </div>

                        {/* Passo 3 - Plano de Tratamento */}
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                                    />
                                </svg>
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Plano Personalizado</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Desenvolvimento de plano terapêutico individualizado com objetivos claros, frequência e técnicas específicas.
                            </p>
                        </div>

                        {/* Passo 4 - Acompanhamento */}
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                    />
                                </svg>
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Tratamento e Evolução</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Sessões regulares com acompanhamento da evolução, relatórios de progresso e ajustes no plano terapêutico.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* 4. Benefícios Section - Background escuro para contraste */}
            <BenefitsSection />

            {/* 6. Depoimentos Section - Background escuro para contraste */}
            <TestimonialsSection />

            {/* 7. Áreas de Atendimento Section - Background claro */}
            <section className="bg-background py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Áreas de Atendimento</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Atendemos nas principais regiões da Grande São Paulo
                        </p>
                    </div>

                    <div className="mt-16 grid gap-8 sm:grid-cols-1 md:grid-cols-3">
                        <div className="rounded-(--radius) border border-transparent bg-card px-8 py-6 shadow ring-1 ring-foreground/5">
                            <div className="flex flex-col items-center text-center">
                                <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                    <MapPin className="h-6 w-6" />
                                </Badge>
                                <h3 className="mt-4 text-base font-semibold text-card-foreground">São Paulo</h3>
                                <ul className="mt-4 space-y-2 text-sm text-muted-foreground">
                                    <li>• Zona Sul</li>
                                    <li>• Zona Oeste</li>
                                    <li>• Zona Norte</li>
                                    <li>• Centro</li>
                                </ul>
                            </div>
                        </div>

                        <div className="rounded-(--radius) border border-transparent bg-card px-8 py-6 shadow ring-1 ring-foreground/5">
                            <div className="flex flex-col items-center text-center">
                                <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                    <MapPin className="h-6 w-6" />
                                </Badge>
                                <h3 className="mt-4 text-base font-semibold text-card-foreground">Barueri/Alphaville</h3>
                                <ul className="mt-4 space-y-2 text-sm text-muted-foreground">
                                    <li>• Alphaville</li>
                                    <li>• Tamboré</li>
                                    <li>• Aldeia da Serra</li>
                                    <li>• Centro de Barueri</li>
                                </ul>
                            </div>
                        </div>

                        <div className="rounded-(--radius) border border-transparent bg-card px-8 py-6 shadow ring-1 ring-foreground/5">
                            <div className="flex flex-col items-center text-center">
                                <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                    <MapPin className="h-6 w-6" />
                                </Badge>
                                <h3 className="mt-4 text-base font-semibold text-card-foreground">Osasco/Carapicuíba/Itapevi</h3>
                                <ul className="mt-4 space-y-2 text-sm text-muted-foreground">
                                    <li>• Centro de Osasco</li>
                                    <li>• Carapicuíba</li>
                                    <li>• Itapevi</li>
                                    <li>• Região do Km 18</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* 8. Trabalhe Conosco Section - Background escuro para contraste */}
            <section className="bg-muted/30 py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Trabalhe Conosco</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Faça parte da nossa rede de parceiros e amplie seu alcance
                        </p>
                    </div>

                    <div className="mt-16 grid gap-8 sm:grid-cols-1 md:grid-cols-2">
                        {/* Para Profissionais */}
                        <div className="rounded-(--radius) border border-transparent bg-card px-8 py-6 shadow ring-1 ring-foreground/5">
                            <div className="flex flex-col items-center text-center">
                                <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                    <User className="h-6 w-6" />
                                </Badge>
                                <h3 className="mt-4 text-base font-semibold text-card-foreground">Para Fisioterapeutas</h3>
                                <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                    Junte-se à nossa equipe de profissionais qualificados e ofereça atendimento domiciliar de qualidade.
                                </p>
                                <ul className="mt-4 space-y-2 text-sm text-muted-foreground">
                                    <li className="flex items-center gap-2">
                                        <Check className="h-4 w-4 text-primary" />
                                        Flexibilidade de horários
                                    </li>
                                    <li className="flex items-center gap-2">
                                        <Check className="h-4 w-4 text-primary" />
                                        Remuneração competitiva
                                    </li>
                                    <li className="flex items-center gap-2">
                                        <Check className="h-4 w-4 text-primary" />
                                        Suporte completo
                                    </li>
                                </ul>
                                <div className="mt-6 w-full">
                                    <Button asChild className="w-full">
                                        <a
                                            href="https://wa.me/5511978196207?text=Olá! Sou fisioterapeuta e gostaria de trabalhar com vocês."
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            <MessageCircle className="mr-2 h-4 w-4" />
                                            Falar com RH
                                        </a>
                                    </Button>
                                </div>
                            </div>
                        </div>

                        {/* Para Empresas */}
                        <div className="rounded-(--radius) border border-transparent bg-card px-8 py-6 shadow ring-1 ring-foreground/5">
                            <div className="flex flex-col items-center text-center">
                                <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                    <Building2 className="h-6 w-6" />
                                </Badge>
                                <h3 className="mt-4 text-base font-semibold text-card-foreground">Para Estabelecimentos</h3>
                                <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                    Cadastre sua empresa de saúde e apareça nos resultados de busca da nossa plataforma.
                                </p>
                                <ul className="mt-4 space-y-2 text-sm text-muted-foreground">
                                    <li className="flex items-center gap-2">
                                        <Check className="h-4 w-4 text-primary" />
                                        Visibilidade online
                                    </li>
                                    <li className="flex items-center gap-2">
                                        <Check className="h-4 w-4 text-primary" />
                                        Contato direto via WhatsApp
                                    </li>
                                    <li className="flex items-center gap-2">
                                        <Check className="h-4 w-4 text-primary" />
                                        Apenas R$ 14,90/mês
                                    </li>
                                </ul>
                                <div className="mt-6 w-full">
                                    <Button asChild className="w-full" variant="outline">
                                        <a href="/empresa/cadastro">
                                            <Building2 className="mr-2 h-4 w-4" />
                                            Cadastrar Empresa
                                        </a>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="mt-12 rounded-(--radius) border border-transparent bg-card px-8 py-6 shadow ring-1 ring-foreground/5">
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                <Mail className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Outras Oportunidades</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Tem uma proposta diferente ou quer fazer parte da nossa equipe de outra forma? Entre em contato!
                            </p>
                            <div className="mt-6">
                                <Button asChild variant="outline">
                                    <a href="mailto:<EMAIL>">
                                        <Mail className="mr-2 h-4 w-4" />
                                        Enviar E-mail
                                    </a>
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* 9. FAQ Section - Background claro para finalizar */}
            <FAQsSection />
        </PublicLayout>
    );
}
