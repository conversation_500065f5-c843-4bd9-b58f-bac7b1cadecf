<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // If plan/subscription features are disabled, bypass checks entirely
        if (!config('features.plans_enabled')) {
            return $next($request);
        }

        $user = auth()->user();

        // Só aplica para pacientes
        if ($user && $user->role === 'paciente' && !$user->has_subscription) {
            // Permite acesso às rotas do fluxo de onboarding, pagamentos/checkout e configurações
            $allowedRoutes = [
                'paciente.onboarding',
                'paciente.onboarding.store',
                'paciente.onboarding.save-partial',
                'paciente.checkout',
                'paciente.checkout.process',
                'paciente.checkout.simulation',
                'paciente.checkout.success',
                'paciente.checkout.confirm',
                'paciente.afiliados',
                'logout',
                'profile.edit',
                'profile.update',
                'profile.destroy',
                'paciente.pagamentos.index'
            ];

            if (!in_array($request->route()->getName(), $allowedRoutes)) {
                // Redirecionar baseado no progresso do usuário
                if (!$user->onboarding_completed) {
                    return redirect()->route('paciente.onboarding')
                        ->with('warning', 'Complete seu perfil médico para continuar.');
                } elseif (!$user->checkout_completed) {
                    return redirect()->route('paciente.pagamentos.index')
                        ->with('warning', 'Finalize o pagamento para continuar.');
                } else {
                    return redirect()->route('paciente.pagamentos.index')
                        ->with('warning', 'Complete o pagamento para ativar sua assinatura.');
                }
            }
        }

        return $next($request);
    }
}
