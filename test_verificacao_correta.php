<?php

require_once 'vendor/autoload.php';

use App\Models\Pagamento;
use App\Services\MercadoPagoService;
use Illuminate\Support\Facades\Log;

try {
    // Inicializar a aplicação Laravel
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    echo "=== Teste Correto de Verificação de Pagamento ===\n\n";

    // Buscar pagamentos de teste recentes
    $pagamentos = Pagamento::whereNull('assinatura_id')
        ->whereNull('agendamento_id')
        ->where('notes', 'like', '%Pagamento de Teste%')
        ->where('created_at', '>=', now()->subHours(2))
        ->orderBy('created_at', 'desc')
        ->get();

    if ($pagamentos->isEmpty()) {
        echo "Nenhum pagamento de teste encontrado nas últimas 2 horas.\n";
        exit;
    }

    echo "Encontrados " . $pagamentos->count() . " pagamentos de teste:\n\n";

    // Obter o serviço MercadoPago
    $mercadoPagoService = app(MercadoPagoService::class);
    $baseUrl = config('mercadopago.base_url');
    $accessToken = $mercadoPagoService->getAccessToken();

    foreach ($pagamentos as $pagamento) {
        echo "----------------------------------------\n";
        echo "ID: " . $pagamento->id . "\n";
        echo "Status: " . $pagamento->status . "\n";
        echo "Valor: R$ " . number_format($pagamento->amount, 2, ',', '.') . "\n";
        echo "Preference ID: " . ($pagamento->preference_id ?? 'N/A') . "\n";
        echo "Criado em: " . $pagamento->created_at->format('d/m/Y H:i:s') . "\n";
        
        if ($pagamento->paid_at) {
            echo "Pago em: " . $pagamento->paid_at->format('d/m/Y H:i:s') . "\n";
        }
        
        echo "\n";

        // Testar verificação correta usando preference_id
        if ($pagamento->preference_id) {
            echo "🔍 Buscando pagamentos para preference_id: " . $pagamento->preference_id . "\n";
            
            try {
                $httpClient = \Illuminate\Support\Facades\Http::withHeaders([
                    'Authorization' => 'Bearer ' . $accessToken,
                ]);

                // Desabilitar verificação SSL em desenvolvimento
                $disableSslVerify = app()->environment('local', 'development') || env('MERCADOPAGO_DISABLE_SSL_VERIFY', false);
                if ($disableSslVerify) {
                    $httpClient = $httpClient->withOptions(['verify' => false]);
                }

                // Buscar pagamentos filtrando por preference_id
                $response = $httpClient->get($baseUrl . '/v1/payments/search', [
                    'preference_id' => $pagamento->preference_id,
                    'limit' => 10
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    $pagamentosMP = $data['results'] ?? [];
                    
                    echo "📊 Encontrados " . count($pagamentosMP) . " pagamentos para esta preferência\n";
                    
                    if (!empty($pagamentosMP)) {
                        // Ordenar por data de criação (mais recente primeiro)
                        usort($pagamentosMP, function($a, $b) {
                            $dateA = strtotime($a['date_created'] ?? 0);
                            $dateB = strtotime($b['date_created'] ?? 0);
                            return $dateB <=> $dateA;
                        });
                        
                        $payment = $pagamentosMP[0]; // Pegar o pagamento mais recente
                        
                        echo "✅ Pagamento mais recente:\n";
                        echo "   Payment ID: " . ($payment['id'] ?? 'N/A') . "\n";
                        echo "   Status: " . ($payment['status'] ?? 'N/A') . "\n";
                        echo "   Status Detalhe: " . ($payment['status_detail'] ?? 'N/A') . "\n";
                        echo "   Método: " . ($payment['payment_method_id'] ?? 'N/A') . "\n";
                        echo "   Valor: R$ " . number_format(($payment['transaction_amount'] ?? 0), 2, ',', '.') . "\n";
                        echo "   Data: " . ($payment['date_created'] ?? 'N/A') . "\n";
                        
                        if ($payment['status'] === 'approved') {
                            echo "   🎉 PAGAMENTO APROVADO!\n";
                        } else {
                            echo "   ⏳ Pagamento ainda não aprovado\n";
                        }
                        
                        // Atualizar o pagamento local com as informações corretas
                        if ($payment['status'] === 'approved') {
                            $pagamento->update([
                                'status' => 'pago',
                                'payment_id' => $payment['id'],
                                'method' => $payment['payment_method_id'],
                                'paid_at' => now(),
                                'gateway_response' => $payment
                            ]);
                            echo "   💾 Pagamento local atualizado para 'pago'\n";
                        }
                    } else {
                        echo "❌ Nenhum pagamento encontrado para esta preferência\n";
                    }
                } else {
                    echo "❌ Erro na busca: " . $response->status() . " - " . $response->body() . "\n";
                }
                
            } catch (\Exception $e) {
                echo "❌ Erro ao verificar pagamento: " . $e->getMessage() . "\n";
            }
        } else {
            echo "❌ Preference ID não disponível\n";
        }
        
        echo "\n";
    }

} catch (\Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
