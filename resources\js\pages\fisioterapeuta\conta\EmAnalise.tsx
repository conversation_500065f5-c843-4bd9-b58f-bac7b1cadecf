import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Head, router } from '@inertiajs/react';
import { CheckCircle, LogOut, Mail, User } from 'lucide-react';
import { useState } from 'react';

export default function EmAnalise() {
    const [isResending, setIsResending] = useState(false);

    const handleResendEmail = async () => {
        setIsResending(true);
        try {
            await router.post(
                '/fisioterapeuta/verificar-email/reenviar',
                {},
                {
                    preserveState: true,
                    onFinish: () => setIsResending(false),
                },
            );
        } catch (error) {
            console.error('Erro ao reenviar email:', error);
            setIsResending(false);
        }
    };

    const handleViewProfile = () => {
        router.get('/fisioterapeuta/perfil');
    };

    const handleSignOut = () => {
        router.post('/logout');
    };

    return (
        <>
            <Head title="Conta em Análise" />

            <div className="flex min-h-screen items-center justify-center bg-background p-4">
                <div className="w-full max-w-md space-y-6">
                    {/* Header */}
                    <div className="space-y-2 text-center">
                        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center overflow-hidden rounded-full">
                            <img src="/images/logo.png" alt="Logo" className="h-12 w-12 object-contain" />
                        </div>
                        <h1 className="text-2xl font-bold text-foreground">Sua conta está em análise</h1>
                        <p className="text-sm text-muted-foreground">
                            Estamos analisando suas informações. Este processo pode levar até 48 horas úteis.
                        </p>
                    </div>

                    {/* Main Card */}
                    <Card className="border-border/50 shadow-lg">
                        <CardContent className="space-y-4 p-6">
                            <div className="flex items-start space-x-3">
                                <div className="mb-4 flex justify-center">
                                    <Badge size="icon-lg" variant="default" className="shadow-md">
                                        <CheckCircle />
                                    </Badge>
                                </div>
                                <div className="space-y-2">
                                    <h3 className="font-semibold text-foreground">Cadastro realizado com sucesso</h3>
                                    <p className="text-sm leading-relaxed text-muted-foreground">
                                        Agradecemos por se cadastrar em nossa plataforma. Nossa equipe está verificando as informações fornecidas para
                                        garantir a qualidade dos nossos serviços.
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-3">
                                <div className="mb-4 flex justify-center">
                                    <Badge size="icon-lg" variant="default" className="shadow-md">
                                        <Mail />
                                    </Badge>
                                </div>
                                <div className="space-y-2">
                                    <h3 className="font-semibold text-foreground">Notificação por email</h3>
                                    <p className="text-sm leading-relaxed text-muted-foreground">
                                        Você receberá um e-mail assim que sua conta for aprovada. Enquanto isso, você pode acessar a página de perfil
                                        para verificar ou atualizar suas informações.
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Action Buttons */}
                    <div className="space-y-3">
                        <Button onClick={handleViewProfile} className="w-full bg-primary text-primary-foreground hover:bg-primary/90" size="lg">
                            <User className="mr-2 h-4 w-4" />
                            Ver Perfil
                        </Button>

                        <Button onClick={handleSignOut} variant="outline" className="w-full border-border bg-transparent hover:bg-muted" size="lg">
                            <LogOut className="mr-2 h-4 w-4" />
                            Sair
                        </Button>
                    </div>
                </div>
            </div>
        </>
    );
}
