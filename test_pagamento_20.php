<?php

require_once 'vendor/autoload.php';

use App\Models\Pagamento;
use App\Services\MercadoPagoService;
use Illuminate\Support\Facades\Log;

try {
    // Inicializar a aplicação Laravel
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    echo "=== Teste Específico do Pagamento ID 20 ===\n\n";

    // Buscar apenas o pagamento ID 20
    $pagamento = Pagamento::find(20);

    if (!$pagamento) {
        echo "Pagamento ID 20 não encontrado.\n";
        exit;
    }

    echo "Detalhes do pagamento:\n";
    echo "ID: " . $pagamento->id . "\n";
    echo "Status: " . $pagamento->status . "\n";
    echo "Valor: R$ " . number_format($pagamento->amount, 2, ',', '.') . "\n";
    echo "Transaction ID: " . ($pagamento->transaction_id ?? 'N/A') . "\n";
    echo "Payment ID: " . ($pagamento->payment_id ?? 'N/A') . "\n";
    echo "Preference ID: " . ($pagamento->preference_id ?? 'N/A') . "\n";
    echo "Criado em: " . $pagamento->created_at->format('d/m/Y H:i:s') . "\n";
    
    if ($pagamento->paid_at) {
        echo "Pago em: " . $pagamento->paid_at->format('d/m/Y H:i:s') . "\n";
    }
    
    echo "\n";

    // Obter o serviço MercadoPago
    $mercadoPagoService = app(MercadoPagoService::class);
    $baseUrl = config('mercadopago.base_url');
    $accessToken = $mercadoPagoService->getAccessToken();

    // Estratégia 1: Verificar se temos um payment_id direto
    if ($pagamento->payment_id) {
        echo "🔍 Estratégia 1: Verificando payment_id direto: " . $pagamento->payment_id . "\n";
        
        try {
            $paymentInfo = $mercadoPagoService->getPayment($pagamento->payment_id);
            
            if ($paymentInfo) {
                echo "✅ Pagamento encontrado com payment_id:\n";
                echo "   Status: " . ($paymentInfo['status'] ?? 'N/A') . "\n";
                echo "   Status Detalhe: " . ($paymentInfo['status_detail'] ?? 'N/A') . "\n";
                echo "   Método: " . ($paymentInfo['payment_method_id'] ?? 'N/A') . "\n";
                echo "   Valor: R$ " . number_format(($paymentInfo['transaction_amount'] ?? 0), 2, ',', '.') . "\n";
                echo "   External Reference: " . ($paymentInfo['external_reference'] ?? 'N/A') . "\n";
                
                if ($paymentInfo['status'] === 'approved') {
                    echo "   🎉 PAGAMENTO APROVADO!\n";
                    
                    // Atualizar o pagamento local
                    $pagamento->update([
                        'status' => 'pago',
                        'method' => $paymentInfo['payment_method_id'],
                        'paid_at' => now(),
                        'gateway_response' => $paymentInfo
                    ]);
                    echo "   💾 Pagamento local atualizado para 'pago'\n";
                } else {
                    echo "   ⏳ Status: " . $paymentInfo['status'] . "\n";
                }
            } else {
                echo "❌ Pagamento não encontrado com payment_id\n";
            }
        } catch (\Exception $e) {
            echo "❌ Erro ao verificar payment_id: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    // Estratégia 2: Buscar por external_reference (que é o ID do pagamento local)
    if ($pagamento->id) {
        echo "🔍 Estratégia 2: Buscando por external_reference: " . $pagamento->id . "\n";
        
        try {
            $httpClient = \Illuminate\Support\Facades\Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ]);

            // Desabilitar verificação SSL em desenvolvimento
            $disableSslVerify = app()->environment('local', 'development') || env('MERCADOPAGO_DISABLE_SSL_VERIFY', false);
            if ($disableSslVerify) {
                $httpClient = $httpClient->withOptions(['verify' => false]);
            }

            // Buscar pagamentos filtrando por external_reference
            $response = $httpClient->get($baseUrl . '/v1/payments/search', [
                'external_reference' => $pagamento->id,
                'limit' => 10
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $pagamentosMP = $data['results'] ?? [];
                
                echo "📊 Encontrados " . count($pagamentosMP) . " pagamentos para esta external_reference\n";
                
                if (!empty($pagamentosMP)) {
                    // Ordenar por data de criação (mais recente primeiro)
                    usort($pagamentosMP, function($a, $b) {
                        $dateA = strtotime($a['date_created'] ?? 0);
                        $dateB = strtotime($b['date_created'] ?? 0);
                        return $dateB <=> $dateA;
                    });
                    
                    $payment = $pagamentosMP[0]; // Pegar o pagamento mais recente
                    
                    echo "✅ Pagamento mais recente:\n";
                    echo "   Payment ID: " . ($payment['id'] ?? 'N/A') . "\n";
                    echo "   Status: " . ($payment['status'] ?? 'N/A') . "\n";
                    echo "   Status Detalhe: " . ($payment['status_detail'] ?? 'N/A') . "\n";
                    echo "   Método: " . ($payment['payment_method_id'] ?? 'N/A') . "\n";
                    echo "   Valor: R$ " . number_format(($payment['transaction_amount'] ?? 0), 2, ',', '.') . "\n";
                    echo "   Data: " . ($payment['date_created'] ?? 'N/A') . "\n";
                    echo "   External Reference: " . ($payment['external_reference'] ?? 'N/A') . "\n";
                    
                    if ($payment['status'] === 'approved') {
                        echo "   🎉 PAGAMENTO APROVADO!\n";
                        
                        // Atualizar o pagamento local com as informações corretas
                        $pagamento->update([
                            'status' => 'pago',
                            'payment_id' => $payment['id'],
                            'method' => $payment['payment_method_id'],
                            'paid_at' => now(),
                            'gateway_response' => $payment
                        ]);
                        echo "   💾 Pagamento local atualizado para 'pago'\n";
                    } else {
                        echo "   ⏳ Pagamento ainda não aprovado\n";
                    }
                } else {
                    echo "❌ Nenhum pagamento encontrado para esta external_reference\n";
                }
            } else {
                echo "❌ Erro na busca: " . $response->status() . " - " . $response->body() . "\n";
            }
            
        } catch (\Exception $e) {
            echo "❌ Erro ao buscar por external_reference: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    // Estratégia 3: Verificar informações da preferência
    if ($pagamento->preference_id) {
        echo "🔍 Estratégia 3: Verificando informações da preferência: " . $pagamento->preference_id . "\n";
        
        try {
            $httpClient = \Illuminate\Support\Facades\Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ]);

            // Desabilitar verificação SSL em desenvolvimento
            $disableSslVerify = app()->environment('local', 'development') || env('MERCADOPAGO_DISABLE_SSL_VERIFY', false);
            if ($disableSslVerify) {
                $httpClient = $httpClient->withOptions(['verify' => false]);
            }

            // Buscar informações da preferência
            $response = $httpClient->get($baseUrl . '/checkout/preferences/' . $pagamento->preference_id);

            if ($response->successful()) {
                $preferenceData = $response->json();
                
                echo "✅ Preferência encontrada:\n";
                echo "   ID: " . ($preferenceData['id'] ?? 'N/A') . "\n";
                echo "   Status: " . ($preferenceData['status'] ?? 'N/A') . "\n";
                echo "   External Reference: " . ($preferenceData['external_reference'] ?? 'N/A') . "\n";
                echo "   Init Point: " . ($preferenceData['init_point'] ?? 'N/A') . "\n";
                echo "   Items: " . count($preferenceData['items'] ?? []) . "\n";
                
                if (!empty($preferenceData['items'])) {
                    foreach ($preferenceData['items'] as $item) {
                        echo "   - " . ($item['title'] ?? 'N/A') . ": R$ " . number_format(($item['unit_price'] ?? 0), 2, ',', '.') . "\n";
                    }
                }
            } else {
                echo "❌ Preferência não encontrada: " . $response->status() . " - " . $response->body() . "\n";
            }
            
        } catch (\Exception $e) {
            echo "❌ Erro ao verificar preferência: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    echo "=== Status Final ===\n";
    echo "Status local: " . $pagamento->refresh()->status . "\n";
    echo "Payment ID: " . ($pagamento->payment_id ?? 'N/A') . "\n";

} catch (\Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
