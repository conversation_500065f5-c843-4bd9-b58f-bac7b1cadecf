<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // SQLite doesn't support MODIFY COLUMN, so we need to recreate the table
        DB::statement("CREATE TABLE agendamentos_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            paciente_id INTEGER NOT NULL,
            fisioterapeuta_id INTEGER NOT NULL,
            assinatura_id INTEGER NOT NULL,
            scheduled_at DATETIME NOT NULL,
            duration INTEGER NOT NULL DEFAULT 60,
            status VARCHAR(20) NOT NULL DEFAULT 'agendado' CHECK (status IN ('agendado', 'confirmado', 'em_andamento', 'concluido', 'cancelado', 'pendente')),
            service_type TEXT NOT NULL,
            notes TEXT,
            address TEXT NOT NULL,
            price DECIMAL(8,2),
            started_at DATETIME,
            finished_at DATETIME,
            cancellation_reason TEXT,
            created_at DATETIME,
            updated_at DATETIME,
            FOREIGN KEY (paciente_id) REFERENCES users(id),
            FOREIGN KEY (fisioterapeuta_id) REFERENCES users(id),
            FOREIGN KEY (assinatura_id) REFERENCES assinaturas(id)
        )");

        // Copy data from old table
        DB::statement("INSERT INTO agendamentos_new (id, paciente_id, fisioterapeuta_id, assinatura_id, scheduled_at, duration, status, service_type, notes, address, price, started_at, finished_at, cancellation_reason, created_at, updated_at)
                       SELECT id, paciente_id, fisioterapeuta_id, assinatura_id, scheduled_at, duration, status, service_type, notes, address, price, started_at, finished_at, cancellation_reason, created_at, updated_at FROM agendamentos");

        // Drop old table and rename new table
        DB::statement("DROP TABLE agendamentos");
        DB::statement("ALTER TABLE agendamentos_new RENAME TO agendamentos");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the original table structure
        DB::statement("CREATE TABLE agendamentos_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            paciente_id INTEGER NOT NULL,
            fisioterapeuta_id INTEGER NOT NULL,
            assinatura_id INTEGER NOT NULL,
            scheduled_at DATETIME NOT NULL,
            duration INTEGER NOT NULL DEFAULT 60,
            status VARCHAR(20) NOT NULL DEFAULT 'agendado' CHECK (status IN ('agendado', 'confirmado', 'em_andamento', 'concluido', 'cancelado')),
            service_type TEXT NOT NULL,
            notes TEXT,
            address TEXT NOT NULL,
            price DECIMAL(8,2),
            started_at DATETIME,
            finished_at DATETIME,
            cancellation_reason TEXT,
            created_at DATETIME,
            updated_at DATETIME,
            FOREIGN KEY (paciente_id) REFERENCES users(id),
            FOREIGN KEY (fisioterapeuta_id) REFERENCES users(id),
            FOREIGN KEY (assinatura_id) REFERENCES assinaturas(id)
        )");

        // Copy data from old table
        DB::statement("INSERT INTO agendamentos_new (id, paciente_id, fisioterapeuta_id, assinatura_id, scheduled_at, duration, status, service_type, notes, address, price, started_at, finished_at, cancellation_reason, created_at, updated_at)
                       SELECT id, paciente_id, fisioterapeuta_id, assinatura_id, scheduled_at, duration, status, service_type, notes, address, price, started_at, finished_at, cancellation_reason, created_at, updated_at FROM agendamentos");

        // Drop old table and rename new table
        DB::statement("DROP TABLE agendamentos");
        DB::statement("ALTER TABLE agendamentos_new RENAME TO agendamentos");
    }
};
