import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useInitials } from '@/hooks/use-initials';
import { router } from '@inertiajs/react';
import { Camera, Trash2, Upload } from 'lucide-react';
import { useRef, useState } from 'react';
import { toast } from 'sonner';

interface AvatarUploadProps {
    user: {
        name: string;
        avatar?: string;
    };
    uploadUrl: string;
    removeUrl: string;
    className?: string;
}

export function AvatarUpload({ user, uploadUrl, removeUrl, className }: AvatarUploadProps) {
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isUploading, setIsUploading] = useState(false);
    const [isRemoving, setIsRemoving] = useState(false);
    const getInitials = useInitials();

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        // Validar tipo de arquivo
        if (!file.type.startsWith('image/')) {
            toast.error('Por favor, selecione apenas arquivos de imagem.');
            return;
        }

        // Validar tamanho (2MB)
        if (file.size > 2 * 1024 * 1024) {
            toast.error('A imagem deve ter no máximo 2MB.');
            return;
        }

        uploadAvatar(file);
    };

    const uploadAvatar = async (file: File) => {
        setIsUploading(true);

        const formData = new FormData();
        formData.append('avatar', file);

        try {
            await new Promise((resolve, reject) => {
                router.post(uploadUrl, formData, {
                    onSuccess: () => {
                        // Toast será mostrado pelo backend via flash message
                        resolve(true);
                    },
                    onError: (errors) => {
                        const errorMessage = errors.avatar || 'Erro ao fazer upload do avatar.';
                        toast.error(errorMessage);
                        reject(new Error(errorMessage));
                    },
                    onFinish: () => setIsUploading(false),
                });
            });
        } catch (error) {
            console.error('Erro no upload:', error);
        }
    };

    const removeAvatar = async () => {
        setIsRemoving(true);

        try {
            await new Promise((resolve, reject) => {
                router.delete(removeUrl, {
                    onSuccess: () => {
                        // Toast será mostrado pelo backend via flash message
                        resolve(true);
                    },
                    onError: () => {
                        toast.error('Erro ao remover avatar.');
                        reject(new Error('Erro ao remover avatar'));
                    },
                    onFinish: () => setIsRemoving(false),
                });
            });
        } catch (error) {
            console.error('Erro ao remover avatar:', error);
        }
    };

    const triggerFileInput = () => {
        fileInputRef.current?.click();
    };

    return (
        <Card className={className}>
            <CardContent className="p-6">
                <div className="flex flex-col items-center space-y-4">
                    <div className="relative">
                        <Avatar className="h-24 w-24">
                            <AvatarImage 
                                src={user.avatar} 
                                alt={user.name} 
                                className="object-cover"
                            />
                            <AvatarFallback className="text-lg">
                                {getInitials(user.name)}
                            </AvatarFallback>
                        </Avatar>
                        
                        <Button
                            size="sm"
                            variant="secondary"
                            className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0"
                            onClick={triggerFileInput}
                            disabled={isUploading}
                        >
                            <Camera className="h-4 w-4" />
                        </Button>
                    </div>

                    <div className="text-center">
                        <h3 className="font-medium">{user.name}</h3>
                        <p className="text-sm text-muted-foreground">
                            Clique na câmera para alterar sua foto
                        </p>
                    </div>

                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={triggerFileInput}
                            disabled={isUploading}
                            className="gap-2"
                        >
                            <Upload className="h-4 w-4" />
                            {isUploading ? 'Enviando...' : 'Alterar Foto'}
                        </Button>

                        {user.avatar && (
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={removeAvatar}
                                disabled={isRemoving}
                                className="gap-2 text-destructive hover:text-destructive"
                            >
                                <Trash2 className="h-4 w-4" />
                                {isRemoving ? 'Removendo...' : 'Remover'}
                            </Button>
                        )}
                    </div>

                    <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleFileSelect}
                        className="hidden"
                    />
                </div>
            </CardContent>
        </Card>
    );
}
