<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Plano extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'sessions_per_month',
        'session_duration',
        'included_services',
        'benefits',
        'active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'included_services' => 'array',
        'benefits' => 'array',
        'active' => 'boolean',
    ];

    // Relacionamentos
    public function assinaturas()
    {
        return $this->hasMany(Assinatura::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    // Accessors
    public function getFormattedPriceAttribute()
    {
        return 'R$ ' . number_format($this->price, 2, ',', '.');
    }

    // Métodos auxiliares
    public function isActive()
    {
        return $this->active;
    }

    public function hasUnlimitedSessions()
    {
        return $this->sessions_per_month === 0;
    }
}
