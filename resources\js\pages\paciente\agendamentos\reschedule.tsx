import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Calendar, Clock, User, AlertTriangle } from 'lucide-react';
import { useState, useEffect } from 'react';
import axios from 'axios';

interface Agendamento {
    id: number;
    data_hora: string;
    duracao: number;
    status: string;
    observacoes?: string;
    fisioterapeuta: {
        id: number;
        user: {
            name: string;
            email: string;
        };
        especialidade?: string;
    };
}

interface Fisioterapeuta {
    id: number;
    user: {
        name: string;
        email: string;
    };
    especialidade?: string;
}

interface RescheduleProps {
    agendamento: Agendamento;
    fisioterapeuta: Fisioterapeuta;
}

export default function RescheduleAgendamento({ agendamento, fisioterapeuta }: RescheduleProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Início', href: '/paciente/dashboard' },
        { title: 'Agendamentos', href: '/paciente/agendamentos' },
        { title: 'Reagendar', href: '' },
    ];
    const [horariosDisponiveis, setHorariosDisponiveis] = useState<string[]>([]);
    const [loadingHorarios, setLoadingHorarios] = useState(false);
    const [selectedDate, setSelectedDate] = useState('');

    const { data, setData, put, processing, errors } = useForm({
        data_agendamento: '',
        horario: '',
        data_hora: '',
        observacoes: agendamento.observacoes || '',
    });

    // Buscar horários disponíveis quando a data for selecionada
    useEffect(() => {
        if (selectedDate) {
            buscarHorariosDisponiveis();
        }
    }, [selectedDate]);

    const buscarHorariosDisponiveis = async () => {
        if (!selectedDate) return;

        setLoadingHorarios(true);
        try {
            const response = await axios.post('/paciente/agendamentos/horarios-disponiveis', {
                fisioterapeuta_id: fisioterapeuta.id,
                data: selectedDate,
            });
            setHorariosDisponiveis(response.data.horarios || []);
        } catch (error) {
            console.error('Erro ao buscar horários:', error);
            setHorariosDisponiveis([]);
        } finally {
            setLoadingHorarios(false);
        }
    };

    const handleDateChange = (date: string) => {
        setSelectedDate(date);
        setData('data_agendamento', date);
        setData('horario', ''); // Reset horário quando data muda
        setData('data_hora', ''); // Reset data_hora
    };

    const handleTimeChange = (time: string) => {
        setData('horario', time);
        if (selectedDate && time) {
            const dataHora = `${selectedDate} ${time}:00`;
            setData('data_hora', dataHora);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!data.data_hora) {
            return;
        }

        put(route('paciente.agendamentos.update-reschedule', agendamento.id));
    };

    // Data mínima: amanhã
    const minDate = new Date();
    minDate.setDate(minDate.getDate() + 1);
    const minDateString = minDate.toISOString().split('T')[0];

    // Data máxima: 30 dias a partir de hoje
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 30);
    const maxDateString = maxDate.toISOString().split('T')[0];

    const formatDateTime = (dateTime: string) => {
        return new Date(dateTime).toLocaleString('pt-BR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Reagendar Consulta" />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                <div className="mb-8">
                    <div className="mb-4">
                        <Link href={route('paciente.agendamentos.index')}>
                            <Button variant="ghost" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar aos Agendamentos
                            </Button>
                        </Link>
                    </div>
                    <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">Reagendar Consulta</h1>
                    <p className="text-muted-foreground">Escolha uma nova data e horário para sua sessão</p>
                </div>

                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Informações do Agendamento Atual */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Agendamento Atual
                            </CardTitle>
                            <CardDescription>
                                Informações da consulta que será reagendada
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-3">
                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                                    <User className="h-5 w-5 text-blue-600" />
                                </div>
                                <div>
                                    <p className="font-medium">{fisioterapeuta.user.name}</p>
                                    {fisioterapeuta.especialidade && (
                                        <p className="text-sm text-muted-foreground">
                                            {fisioterapeuta.especialidade}
                                        </p>
                                    )}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <div className="flex items-center gap-2 text-sm">
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                    <span>{formatDateTime(agendamento.data_hora)}</span>
                                </div>
                                <div className="flex items-center gap-2 text-sm">
                                    <Clock className="h-4 w-4 text-muted-foreground" />
                                    <span>{agendamento.duracao} minutos</span>
                                </div>
                            </div>

                            {agendamento.observacoes && (
                                <div className="border-t pt-4">
                                    <p className="text-sm">
                                        <strong>Observações atuais:</strong>
                                    </p>
                                    <p className="text-sm text-muted-foreground mt-1">
                                        {agendamento.observacoes}
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Formulário de Reagendamento */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Nova Data e Horário</CardTitle>
                            <CardDescription>
                                Selecione quando deseja reagendar sua consulta
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Alerta sobre prazo */}
                                <Alert>
                                    <AlertTriangle className="h-4 w-4" />
                                    <AlertDescription>
                                        Reagendamentos devem ser feitos com pelo menos 24 horas de antecedência.
                                    </AlertDescription>
                                </Alert>

                                {/* Seleção de Data */}
                                <div className="space-y-2">
                                    <Label htmlFor="data_agendamento">Nova Data</Label>
                                    <Input
                                        id="data_agendamento"
                                        type="date"
                                        min={minDateString}
                                        max={maxDateString}
                                        value={selectedDate}
                                        onChange={(e) => handleDateChange(e.target.value)}
                                        className={errors.data_hora ? 'border-red-500' : ''}
                                    />
                                    {errors.data_hora && (
                                        <p className="text-sm text-red-600">{errors.data_hora}</p>
                                    )}
                                </div>

                                {/* Seleção de Horário */}
                                {selectedDate && (
                                    <div className="space-y-2">
                                        <Label>Horários Disponíveis</Label>
                                        {loadingHorarios ? (
                                            <div className="flex items-center justify-center py-4">
                                                <div className="h-6 w-6 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
                                                <span className="ml-2 text-sm text-muted-foreground">
                                                    Carregando horários...
                                                </span>
                                            </div>
                                        ) : horariosDisponiveis.length > 0 ? (
                                            <div className="grid grid-cols-3 gap-2">
                                                {horariosDisponiveis.map((horario) => (
                                                    <Button
                                                        key={horario}
                                                        type="button"
                                                        variant={data.horario === horario ? "default" : "outline"}
                                                        size="sm"
                                                        onClick={() => handleTimeChange(horario)}
                                                        className="justify-center"
                                                    >
                                                        {horario}
                                                    </Button>
                                                ))}
                                            </div>
                                        ) : (
                                            <p className="text-sm text-muted-foreground py-4 text-center">
                                                Nenhum horário disponível para esta data.
                                                Tente selecionar outra data.
                                            </p>
                                        )}
                                    </div>
                                )}

                                {/* Observações */}
                                <div className="space-y-2">
                                    <Label htmlFor="observacoes">Observações (opcional)</Label>
                                    <Textarea
                                        id="observacoes"
                                        placeholder="Adicione observações sobre o reagendamento..."
                                        value={data.observacoes}
                                        onChange={(e) => setData('observacoes', e.target.value)}
                                        rows={3}
                                    />
                                </div>

                                {/* Botões */}
                                <div className="flex gap-4">
                                    <Button
                                        type="submit"
                                        disabled={processing || !data.data_hora}
                                        className="flex-1"
                                    >
                                        {processing ? (
                                            <>
                                                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                                Reagendando...
                                            </>
                                        ) : (
                                            'Confirmar Reagendamento'
                                        )}
                                    </Button>
                                    <Button type="button" variant="outline" asChild>
                                        <Link href={route('paciente.agendamentos.index')}>
                                            Cancelar
                                        </Link>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
