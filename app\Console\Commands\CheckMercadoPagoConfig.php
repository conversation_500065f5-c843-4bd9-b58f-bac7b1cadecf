<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MercadoPagoService;
use Illuminate\Support\Facades\Http;

class CheckMercadoPagoConfig extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mercadopago:check {--env= : Environment to check}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verificar configuração do Mercado Pago';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $environment = $this->option('env') ?? 'production';
        
        $this->info("🔍 Verificando configuração do Mercado Pago - Ambiente: {$environment}");
        $this->newLine();

        // Verificar configuração do ambiente
        $this->checkEnvironmentConfig($environment);
        $this->newLine();

        // Verificar credenciais
        $this->checkCredentials($environment);
        $this->newLine();

        // Verificar conectividade com API
        $this->checkApiConnectivity($environment);
        $this->newLine();

        // Verificar webhook
        $this->checkWebhookConfig($environment);
        $this->newLine();

        $this->info('✅ Verificação concluída!');
    }

    private function checkEnvironmentConfig($environment)
    {
        $this->info('📋 Configuração do Ambiente:');
        
        $config = config("mercadopago.environments.{$environment}");
        
        if (!$config) {
            $this->error("❌ Ambiente '{$environment}' não configurado");
            return;
        }

        $this->table(
            ['Configuração', 'Valor'],
            [
                ['Access Token', $this->maskToken($config['access_token'] ?? 'N/A')],
                ['Public Key', $this->maskToken($config['public_key'] ?? 'N/A')],
                ['Base URL', $config['base_url'] ?? 'N/A'],
                ['Webhook Secret', $this->maskToken($config['webhook_secret'] ?? 'N/A')],
            ]
        );
    }

    private function checkCredentials($environment)
    {
        $this->info('🔑 Verificação de Credenciais:');
        
        $config = config("mercadopago.environments.{$environment}");
        
        if (empty($config['access_token'])) {
            $this->error("❌ Access Token não configurado para ambiente '{$environment}'");
            return;
        }

        if (empty($config['public_key'])) {
            $this->error("❌ Public Key não configurada para ambiente '{$environment}'");
            return;
        }

        // Verificar formato das credenciais
        $accessToken = $config['access_token'];
        $publicKey = $config['public_key'];

        if (!str_starts_with($accessToken, 'APP_USR-')) {
            $this->warn("⚠️ Access Token não parece ser de produção (deveria começar com 'APP_USR-')");
        }
        if (!str_starts_with($publicKey, 'APP_USR-')) {
            $this->warn("⚠️ Public Key não parece ser de produção (deveria começar com 'APP_USR-')");
        }

        $this->info("✅ Credenciais configuradas para ambiente '{$environment}'");
    }

    private function checkApiConnectivity($environment)
    {
        $this->info('🌐 Teste de Conectividade com API:');
        
        $config = config("mercadopago.environments.{$environment}");
        
        if (empty($config['access_token'])) {
            $this->error("❌ Não é possível testar conectividade sem Access Token");
            return;
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $config['access_token'],
            ])->get('https://api.mercadopago.com/users/me');

            if ($response->successful()) {
                $userData = $response->json();
                $this->info("✅ Conectividade OK - Usuário: " . (isset($userData['nickname']) ? $userData['nickname'] : 'N/A'));
                $this->info("   ID: " . (isset($userData['id']) ? $userData['id'] : 'N/A'));
                $this->info("   Email: " . (isset($userData['email']) ? $userData['email'] : 'N/A'));
            } else {
                $this->error("❌ Erro na API: {$response->status()} - {$response->body()}");
            }
        } catch (\Exception $e) {
            $this->error("❌ Erro de conectividade: {$e->getMessage()}");
        }
    }

    private function checkWebhookConfig($environment)
    {
        $this->info('🔔 Configuração do Webhook:');
        
        $webhookUrl = config('mercadopago.webhook.url', '/webhook/mercadopago');
        $webhookSecret = config("mercadopago.environments.{$environment}.webhook_secret");
        
        $this->info("   URL: {$webhookUrl}");
        $this->info("   Secret: " . ($webhookSecret ? $this->maskToken($webhookSecret) : 'N/A'));
        
        if (empty($webhookSecret)) {
            $this->warn("⚠️ Webhook Secret não configurado - validação de assinatura desabilitada");
        } else {
            $this->info("✅ Webhook Secret configurado");
        }

        // Verificar se a rota está registrada
        $this->info("   Rota registrada: " . (route('mercadopago.webhook') ? '✅ Sim' : '❌ Não'));
    }

    private function maskToken($token)
    {
        if (empty($token)) {
            return 'N/A';
        }
        
        if (strlen($token) <= 10) {
            return $token;
        }
        
        return substr($token, 0, 10) . '...' . substr($token, -4);
    }
}
