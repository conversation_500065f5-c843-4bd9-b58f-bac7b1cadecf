<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Log;

class CustomRegisteredListener
{
    /**
     * Handle the event.
     * 
     * Este listener substitui o comportamento padrão do Laravel
     * que envia automaticamente email de verificação no registro.
     * Agora apenas logamos o evento, sem enviar email.
     */
    public function handle(Registered $event): void
    {
        Log::info('Usuário registrado (sem envio automático de verificação)', [
            'user_id' => $event->user->id,
            'email' => $event->user->email,
            'role' => $event->user->role
        ]);
        
        // Não fazemos nada aqui - o email de verificação será enviado
        // apenas quando o usuário solicitar explicitamente
    }
}