<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\URL;

class EmailVerificationPromptController extends Controller
{
    /**
     * Show the email verification prompt page.
     */
    public function __invoke(Request $request): Response|RedirectResponse
    {
        if ($request->user()->hasVerifiedEmail()) {
            return redirect()->intended(route('dashboard', absolute: false));
        }

        // Generate a sample verification URL to show the email content
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            [
                'id' => $request->user()->getKey(),
                'hash' => sha1($request->user()->getEmailForVerification()),
            ]
        );

        return Inertia::render('auth/verify-email', [
            'status' => $request->session()->get('status'),
            'user' => $request->user(),
            'verificationUrl' => $verificationUrl,
            'emailContent' => $this->getEmailContent($request->user(), $verificationUrl)
        ]);
    }

    /**
     * Get the email content for preview
     */
    private function getEmailContent($user, $verificationUrl)
    {
        return [
            'subject' => 'Verificar Email - F4 Fisio',
            'greeting' => "Olá, {$user->name}!",
            'message' => 'Bem-vindo à plataforma F4 Fisio! Para garantir a segurança da sua conta e prosseguir com o processo de aprovação profissional, precisamos verificar seu endereço de email.',
            'buttonText' => '✓ Verificar Meu Email',
            'verificationUrl' => $verificationUrl,
            'securityNote' => 'Este link é válido por 60 minutos e só pode ser usado uma vez. Se você não solicitou esta verificação, ignore este email.'
        ];
    }
}
