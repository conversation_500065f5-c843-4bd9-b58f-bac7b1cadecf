import { Head, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Mail, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { useState } from 'react';

interface Props {
    user: {
        name: string;
        email: string;
    };
    canResendEmail: boolean;
    status?: string;
    message?: string;
    remaining_time?: number;
}

export default function VerificarEmail({ user, canResendEmail, status, message, remaining_time }: Props) {
    const [isResending, setIsResending] = useState(false);
    
    const { post, processing } = useForm();

    const handleResendEmail = () => {
        setIsResending(true);
        post(route('fisioterapeuta.verificar-email.reenviar'), {
            onFinish: () => setIsResending(false),
        });
    };

    return (
        <>
            <Head title="Verificar Email - Fisioterapeuta" />
            
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
                <div className="w-full max-w-md">
                    <Card className="shadow-lg">
                        <CardHeader className="text-center">
                            <div className="mx-auto mb-4 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                                <Mail className="h-8 w-8 text-blue-600" />
                            </div>
                            <CardTitle className="text-2xl font-bold text-gray-900">
                                Verificar Email
                            </CardTitle>
                            <CardDescription className="text-gray-600">
                                Para continuar como fisioterapeuta, você precisa verificar seu email
                            </CardDescription>
                        </CardHeader>
                        
                        <CardContent className="space-y-6">
                            {status === 'verification-link-sent' && (
                                <Alert className="border-green-200 bg-green-50">
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                    <AlertDescription className="text-green-800">
                                        {message || 'Um novo email de verificação foi enviado para seu endereço.'}
                                    </AlertDescription>
                                </Alert>
                            )}

                            {status === 'verification-link-throttled' && (
                                <Alert className="border-yellow-200 bg-yellow-50">
                                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                                    <AlertDescription className="text-yellow-800">
                                        {message || `Aguarde ${remaining_time} segundos antes de solicitar outro email de verificação.`}
                                    </AlertDescription>
                                </Alert>
                            )}

                            {status === 'verification-link-limit-reached' && (
                                <Alert className="border-orange-200 bg-orange-50">
                                    <AlertCircle className="h-4 w-4 text-orange-600" />
                                    <AlertDescription className="text-orange-800">
                                        {message || 'Limite de emails de verificação atingido. Tente novamente em 1 hora.'}
                                    </AlertDescription>
                                </Alert>
                            )}

                            <Alert className="border-blue-200 bg-blue-50">
                                <AlertCircle className="h-4 w-4 text-blue-600" />
                                <AlertDescription className="text-blue-800">
                                    <strong>Olá, {user.name}!</strong><br />
                                    Enviamos um email de verificação para <strong>{user.email}</strong>. 
                                    Clique no link do email para verificar sua conta e continuar o processo de aprovação.
                                </AlertDescription>
                            </Alert>

                            <div className="space-y-4">
                                <div className="text-sm text-gray-600">
                                    <p className="mb-2">
                                        <strong>Por que preciso verificar meu email?</strong>
                                    </p>
                                    <ul className="list-disc list-inside space-y-1 text-xs">
                                        <li>Garantir a segurança da sua conta</li>
                                        <li>Receber notificações importantes</li>
                                        <li>Prosseguir com a aprovação profissional</li>
                                        <li>Acessar todas as funcionalidades da plataforma</li>
                                    </ul>
                                </div>

                                {canResendEmail && (
                                    <Button
                                        onClick={handleResendEmail}
                                        disabled={processing || isResending}
                                        variant="outline"
                                        className="w-full"
                                    >
                                        {(processing || isResending) ? (
                                            <>
                                                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                                Enviando...
                                            </>
                                        ) : (
                                            <>
                                                <Mail className="mr-2 h-4 w-4" />
                                                Reenviar Email de Verificação
                                            </>
                                        )}
                                    </Button>
                                )}

                                <div className="text-center">
                                    <a
                                        href={route('logout')}
                                        className="text-sm text-gray-500 hover:text-gray-700 underline"
                                    >
                                        Sair da conta
                                    </a>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <div className="mt-6 text-center">
                        <p className="text-xs text-gray-500">
                            Não recebeu o email? Verifique sua caixa de spam ou lixo eletrônico.
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
