<?php
// Verificar se o PHP está funcionando corretamente
echo "<!DOCTYPE html>\n";
echo "<html>\n";
echo "<head>\n";
echo "    <title>Teste de PHP</title>\n";
echo "</head>\n";
echo "<body>\n";
echo "    <h1>Teste de PHP</h1>\n";

// Exibir informações básicas do PHP
echo "<h2>Informações do PHP</h2>\n";
echo "<p>Versão do PHP: " . phpversion() . "</p>\n";

// Verificar extensões carregadas
echo "<h3>Extensões carregadas:</h3>\n";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<ul>\n";
foreach ($extensions as $ext) {
    echo "    <li>$ext</li>\n";
}
echo "</ul>\n";

// Verificar configurações importantes
echo "<h3>Configurações importantes:</h3>\n";
$settings = [
    'memory_limit',
    'max_execution_time',
    'post_max_size',
    'upload_max_filesize',
    'date.timezone',
    'display_errors',
    'error_reporting',
    'extension_dir'
];

echo "<table border='1'>\n";
echo "    <tr><th>Configuração</th><th>Valor</th></tr>\n";
foreach ($settings as $setting) {
    echo "    <tr><td>$setting</td><td>" . htmlspecialchars(ini_get($setting)) . "</td></tr>\n";
}
echo "</table>\n";

// Verificar permissões de escrita
echo "<h3>Verificação de permissões:</h3>\n";
$paths = [
    'Diretório raiz' => __DIR__ . '/..',
    'Diretório de armazenamento' => __DIR__ . '/../storage',
    'Diretório de cache' => __DIR__ . '/../bootstrap/cache',
    'Arquivo de banco de dados' => __DIR__ . '/../database/database.sqlite'
];

echo "<table border='1'>\n";
echo "    <tr><th>Local</th><th>Existe</th><th>Gravável</th><th>Detalhes</th></tr>\n";
foreach ($paths as $name => $path) {
    $exists = file_exists($path);
    $isWritable = $exists ? (is_writable($path) ? 'Sim' : 'Não') : 'Não existe';
    $details = '';
    
    if ($exists) {
        if (is_dir($path)) {
            $details = 'Diretório';
        } else {
            $details = 'Arquivo, Tamanho: ' . filesize($path) . ' bytes';
        }
    }
    
    echo "    <tr>";
    echo "<td>$name</td>";
    echo "<td>" . ($exists ? 'Sim' : 'Não') . "</td>";
    echo "<td>$isWritable</td>";
    echo "<td>$details</td>";
    echo "</tr>\n";
}
echo "</table>\n";

// Verificar se o SQLite está disponível
echo "<h3>Verificação do SQLite:</h3>\n";
$sqliteAvailable = extension_loaded('pdo_sqlite') ? 'Sim' : 'Não';
echo "<p>PDO_SQLITE disponível: $sqliteAvailable</p>\n";

if (extension_loaded('pdo_sqlite')) {
    try {
        $testDb = new PDO('sqlite::memory:');
        $testDb->exec('CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)');
        $testDb->exec('INSERT INTO test (name) VALUES ("test")');
        $result = $testDb->query('SELECT * FROM test')->fetch(PDO::FETCH_ASSOC);
        echo "<p>Teste de operação SQLite em memória: <span style='color: green;'>Sucesso!</span></p>\n";
    } catch (PDOException $e) {
        echo "<p>Erro no teste SQLite: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
}

echo "</body>\n";
echo "</html>\n";
?>
