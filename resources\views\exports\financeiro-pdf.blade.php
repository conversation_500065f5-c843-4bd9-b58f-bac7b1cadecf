<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Relatório Financeiro - F4 Fisio</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #0066cc;
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .filters {
            background-color: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .filters h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .filters p {
            margin: 5px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        th {
            background-color: #0066cc;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .summary {
            background-color: #e7f3ff;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .summary h3 {
            margin: 0 0 10px 0;
            color: #0066cc;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .status-pago {
            color: #28a745;
            font-weight: bold;
        }
        .status-pendente {
            color: #ffc107;
            font-weight: bold;
        }
        .status-cancelado {
            color: #dc3545;
            font-weight: bold;
        }
        .status-vencido {
            color: #dc3545;
            font-weight: bold;
        }
        .value {
            text-align: right;
            font-weight: bold;
        }
        .total-row {
            background-color: #0066cc !important;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>F4 Fisio - Relatório Financeiro</h1>
        <p>Gerado em: {{ $generated_at->format('d/m/Y H:i:s') }}</p>
    </div>

    @if(!empty($filters))
    <div class="filters">
        <h3>Filtros Aplicados:</h3>
        @if(isset($filters['status']))
            <p><strong>Status:</strong> {{ ucfirst($filters['status']) }}</p>
        @endif
        @if(isset($filters['forma_pagamento']))
            <p><strong>Forma de Pagamento:</strong> {{ ucfirst(str_replace('_', ' ', $filters['forma_pagamento'])) }}</p>
        @endif
        @if(isset($filters['date_from']))
            <p><strong>Data Inicial:</strong> {{ \Carbon\Carbon::parse($filters['date_from'])->format('d/m/Y') }}</p>
        @endif
        @if(isset($filters['date_to']))
            <p><strong>Data Final:</strong> {{ \Carbon\Carbon::parse($filters['date_to'])->format('d/m/Y') }}</p>
        @endif
    </div>
    @endif

    <div class="summary">
        <h3>Resumo Financeiro</h3>
        <div class="summary-grid">
            <div>
                <p><strong>Total de Pagamentos:</strong> {{ count($pagamentos) }}</p>
                <p><strong>Por Status:</strong></p>
                <ul>
                    @foreach($pagamentos->groupBy('status') as $status => $statusPagamentos)
                        <li>{{ ucfirst($status) }}: {{ $statusPagamentos->count() }}</li>
                    @endforeach
                </ul>
            </div>
            <div>
                <p><strong>Valor Total:</strong> R$ {{ number_format($pagamentos->sum('amount'), 2, ',', '.') }}</p>
                <p><strong>Valor Pago:</strong> R$ {{ number_format($pagamentos->where('status', 'pago')->sum('amount'), 2, ',', '.') }}</p>
                <p><strong>Valor Pendente:</strong> R$ {{ number_format($pagamentos->where('status', 'pendente')->sum('amount'), 2, ',', '.') }}</p>
                <p><strong>Por Forma de Pagamento:</strong></p>
                <ul>
                    @foreach($pagamentos->groupBy('method') as $forma => $formaPagamentos)
                        <li>{{ ucfirst(str_replace('_', ' ', $forma ?? 'Não informado')) }}: R$ {{ number_format($formaPagamentos->sum('amount'), 2, ',', '.') }}</li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Cliente</th>
                <th>Valor</th>
                <th>Status</th>
                <th>Forma Pagamento</th>
                <th>Data Vencimento</th>
                <th>Data Pagamento</th>
            </tr>
        </thead>
        <tbody>
            @foreach($pagamentos as $pagamento)
            <tr>
                <td>{{ $pagamento->id }}</td>
                <td>{{ $pagamento->assinatura->user->name ?? ($pagamento->user->name ?? 'N/A') }}</td>
                <td class="value">R$ {{ number_format($pagamento->amount, 2, ',', '.') }}</td>
                <td class="status-{{ $pagamento->status }}">
                    {{ ucfirst($pagamento->status) }}
                </td>
                <td>{{ ucfirst(str_replace('_', ' ', $pagamento->method ?? 'Não informado')) }}</td>
                <td>{{ $pagamento->due_date ? \Carbon\Carbon::parse($pagamento->due_date)->format('d/m/Y') : ($pagamento->data_vencimento ? \Carbon\Carbon::parse($pagamento->data_vencimento)->format('d/m/Y') : 'N/A') }}</td>
                <td>{{ $pagamento->paid_at ? \Carbon\Carbon::parse($pagamento->paid_at)->format('d/m/Y') : 'N/A' }}</td>
            </tr>
            @endforeach
            <tr class="total-row">
                <td colspan="2"><strong>TOTAL</strong></td>
                <td class="value"><strong>R$ {{ number_format($pagamentos->sum('amount'), 2, ',', '.') }}</strong></td>
                <td colspan="4"></td>
            </tr>
        </tbody>
    </table>

    <div class="footer">
        <p>F4 Fisio - Sistema de Gestão de Fisioterapia</p>
        <p>Este relatório foi gerado automaticamente pelo sistema</p>
    </div>
</body>
</html>
