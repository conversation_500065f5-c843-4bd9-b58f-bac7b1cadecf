<?php

namespace App\Http\Controllers\Fisioterapeuta;

use App\Http\Controllers\Controller;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;

class StatusContaController extends Controller
{
    /**
     * Mostra a tela de análise em andamento
     */
    public function analise()
    {
        $fisioterapeuta = Auth::user()->fisioterapeuta;
        
        // Se o status não for 'pending', redireciona para a página apropriada
        if ($fisioterapeuta->status === 'approved') {
            return redirect()->route('fisioterapeuta.dashboard');
        } elseif ($fisioterapeuta->status === 'rejected') {
            return redirect()->route('fisioterapeuta.conta-rejeitada');
        }
        
        return Inertia::render('fisioterapeuta/conta/EmAnalise');
    }
    
    /**
     * Mostra a tela de conta rejeitada
     */
    public function contaRejeitada()
    {
        $fisioterapeuta = Auth::user()->fisioterapeuta;
        
        // Se o status não for 'rejected', redireciona para a página apropriada
        if ($fisioterapeuta->status === 'approved') {
            return redirect()->route('fisioterapeuta.dashboard');
        } elseif ($fisioterapeuta->status === 'pending') {
            return redirect()->route('fisioterapeuta.analise');
        }
        
        return Inertia::render('fisioterapeuta/conta/ContaRejeitada', [
            'motivo' => $fisioterapeuta->rejection_reason
        ]);
    }
}
