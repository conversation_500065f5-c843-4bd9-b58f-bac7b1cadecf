<?php

require_once 'vendor/autoload.php';

use App\Models\Pagamento;
use App\Http\Controllers\TestePagamentoController;
use Illuminate\Http\Request;

try {
    // Inicializar a aplicação Laravel
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    echo "=== Teste Final de Verificação de Pagamento ===\n\n";

    // Buscar o pagamento ID 20
    $pagamento = Pagamento::find(20);

    if (!$pagamento) {
        echo "Pagamento ID 20 não encontrado.\n";
        exit;
    }

    echo "=== Status ANTES da verificação ===\n";
    echo "ID: " . $pagamento->id . "\n";
    echo "Status: " . $pagamento->status . "\n";
    echo "Método: " . ($pagamento->method ?? 'N/A') . "\n";
    echo "Payment ID: " . ($pagamento->payment_id ?? 'N/A') . "\n";
    echo "Preference ID: " . ($pagamento->preference_id ?? 'N/A') . "\n";
    echo "Pago em: " . ($pagamento->paid_at ? $pagamento->paid_at->format('d/m/Y H:i:s') : 'N/A') . "\n";
    echo "\n";

    // Simular uma requisição para o controller
    $request = new Request(['pagamento_id' => $pagamento->id]);
    $controller = new TestePagamentoController();

    echo "=== Executando verificação via Controller ===\n";
    
    try {
        $response = $controller->verificarPagamento($request);
        $responseData = $response->getData(true);
        
        echo "Resposta do controller:\n";
        echo "Success: " . ($responseData['success'] ? 'true' : 'false') . "\n";
        echo "Message: " . ($responseData['message'] ?? 'N/A') . "\n";
        
        if (isset($responseData['pagamento'])) {
            echo "\nDados do pagamento na resposta:\n";
            echo "Status: " . ($responseData['pagamento']['status'] ?? 'N/A') . "\n";
            echo "Status Detalhe: " . ($responseData['pagamento']['status_detail'] ?? 'N/A') . "\n";
            echo "Payment ID: " . ($responseData['pagamento']['id'] ?? 'N/A') . "\n";
            echo "External Reference: " . ($responseData['pagamento']['external_reference'] ?? 'N/A') . "\n";
        }
        
        if (isset($responseData['payment_data'])) {
            echo "\nDados do Mercado Pago:\n";
            echo "MP Status: " . ($responseData['payment_data']['status'] ?? 'N/A') . "\n";
            echo "MP Status Detalhe: " . ($responseData['payment_data']['status_detail'] ?? 'N/A') . "\n";
            echo "MP Método: " . ($responseData['payment_data']['payment_method_id'] ?? 'N/A') . "\n";
            echo "MP Valor: R$ " . number_format(($responseData['payment_data']['transaction_amount'] ?? 0), 2, ',', '.') . "\n";
        }
        
    } catch (\Exception $e) {
        echo "ERRO no controller: " . $e->getMessage() . "\n";
        echo "Trace: " . $e->getTraceAsString() . "\n";
    }
    
    echo "\n";

    // Recarregar o pagamento do banco para ver o status final
    $pagamento->refresh();

    echo "=== Status DEPOIS da verificação ===\n";
    echo "ID: " . $pagamento->id . "\n";
    echo "Status: " . $pagamento->status . "\n";
    echo "Método: " . ($pagamento->method ?? 'N/A') . "\n";
    echo "Payment ID: " . ($pagamento->payment_id ?? 'N/A') . "\n";
    echo "Preference ID: " . ($pagamento->preference_id ?? 'N/A') . "\n";
    echo "Pago em: " . ($pagamento->paid_at ? $pagamento->paid_at->format('d/m/Y H:i:s') : 'N/A') . "\n";

    echo "\n=== RESUMO ===\n";
    if ($pagamento->status === 'pago' && $pagamento->payment_id) {
        echo "✅ SUCESSO: Pagamento verificado e atualizado corretamente!\n";
        echo "   - Status local: " . $pagamento->status . "\n";
        echo "   - Payment ID: " . $pagamento->payment_id . "\n";
        echo "   - Método: " . $pagamento->method . "\n";
        echo "   - Pago em: " . $pagamento->paid_at->format('d/m/Y H:i:s') . "\n";
    } else {
        echo "❌ PROBLEMA: Pagamento não foi atualizado corretamente.\n";
        echo "   - Status local: " . $pagamento->status . "\n";
        echo "   - Payment ID: " . ($pagamento->payment_id ?? 'N/A') . "\n";
    }

} catch (\Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
