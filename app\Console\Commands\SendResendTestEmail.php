<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SendResendTestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * Usage:
     *   php artisan email:resend-test --to="<EMAIL>" --from="Nome <<EMAIL>>"
     */
    protected $signature = 'email:resend-test {--to=} {--from=} {--subject=Teste Resend} {--html=<p>it works!</p>}';

    /**
     * The console command description.
     */
    protected $description = 'Envia um email de teste usando o client oficial do Resend';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $to = $this->option('to');
        $from = $this->option('from') ?: sprintf('F4 Fisio <%s>', config('mail.from.address', '<EMAIL>'));
        $subject = $this->option('subject');
        $html = $this->option('html');

        if (!$to) {
            $this->error('Parâmetro --to é obrigatório.');
            return self::FAILURE;
        }

        $apiKey = config('resend.api_key') ?: env('RESEND_API_KEY');
        if (!$apiKey) {
            $this->error('RESEND_API_KEY não configurada. Defina no .env ou passe via variável de ambiente.');
            return self::FAILURE;
        }

        // Usa a classe global Resend do SDK oficial
        $resend = \Resend::client($apiKey);

        try {
            $result = $resend->emails->send([
                'from' => $from,
                'to' => [$to],
                'subject' => $subject,
                'html' => $html,
            ]);

            $this->info('Email enviado com sucesso. ID: ' . ($result->id ?? 'desconhecido'));
            return self::SUCCESS;
        } catch (\Throwable $e) {
            $this->error('Falha ao enviar email: ' . $e->getMessage());
            return self::FAILURE;
        }
    }
}
