import { Head } from '@inertiajs/react';
import { <PERSON>, ArrowLeft, CreditCard, Bell, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface Props {
    payment_id?: string;
    external_reference?: string;
    message: string;
}

export default function MercadoPagoPending({ payment_id, external_reference, message }: Props) {
    const handleBackToSite = () => {
        window.location.href = '/';
    };

    const handleCheckStatus = () => {
        window.location.reload();
    };

    return (
        <>
            <Head title="Pagamento Pendente" />
            
            <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-amber-100 flex items-center justify-center p-4">
                <div className="max-w-md w-full space-y-6">
                    {/* Ícone de Pendente */}
                    <div className="text-center">
                        <div className="mx-auto w-20 h-20 bg-yellow-500 rounded-full flex items-center justify-center mb-4">
                            <Clock className="w-12 h-12 text-white" />
                        </div>
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">
                            Pagamento Pendente
                        </h1>
                        <p className="text-gray-600">
                            {message}
                        </p>
                    </div>

                    {/* Detalhes do Pagamento */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg">
                                <CreditCard className="w-5 h-5" />
                                Detalhes do Pagamento
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {payment_id && (
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">ID do Pagamento:</span>
                                    <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                        {payment_id}
                                    </span>
                                </div>
                            )}
                            
                            {external_reference && (
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">Referência:</span>
                                    <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                        {external_reference}
                                    </span>
                                </div>
                            )}

                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Status:</span>
                                <span className="text-sm font-semibold text-yellow-600 bg-yellow-100 px-2 py-1 rounded">
                                    Processando
                                </span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Data:</span>
                                <span className="text-sm text-gray-900">
                                    {new Date().toLocaleDateString('pt-BR', {
                                        day: '2-digit',
                                        month: '2-digit',
                                        year: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    })}
                                </span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* O que está acontecendo */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg">
                                <Bell className="w-5 h-5" />
                                O que está acontecendo?
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <ul className="space-y-2 text-sm text-gray-600">
                                <li className="flex items-start gap-2">
                                    <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span>Seu pagamento está sendo processado</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span>Aguardando confirmação do banco emissor</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span>Você será notificado quando for aprovado</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span>O processo pode levar alguns minutos</span>
                                </li>
                            </ul>
                        </CardContent>
                    </Card>

                    {/* Tempo Estimado */}
                    <Card className="bg-blue-50 border-blue-200">
                        <CardContent className="pt-6">
                            <div className="text-center">
                                <Clock className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                                <h3 className="font-semibold text-blue-900 mb-1">Tempo Estimado</h3>
                                <p className="text-sm text-blue-700">
                                    Normalmente o processamento leva de 2 a 5 minutos
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Botões de Ação */}
                    <div className="space-y-3">
                        <Button 
                            onClick={handleCheckStatus}
                            className="w-full bg-yellow-600 hover:bg-yellow-700"
                        >
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Verificar Status
                        </Button>
                        
                        <Button 
                            onClick={handleBackToSite}
                            variant="outline"
                            className="w-full"
                        >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Voltar ao Site
                        </Button>
                    </div>

                    {/* Informações de Suporte */}
                    <div className="text-center text-xs text-gray-500">
                        <p>Você receberá um email quando o pagamento for processado</p>
                        <p className="mt-1">
                            Processamento seguro pelo Mercado Pago
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
