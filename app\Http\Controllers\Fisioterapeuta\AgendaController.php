<?php

namespace App\Http\Controllers\Fisioterapeuta;

use App\Http\Controllers\Controller;
use App\Models\Agendamento;
use App\Services\NotificacaoService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AgendaController extends Controller
{
    public function index(Request $request)
    {
        $fisioterapeuta = auth()->user();
        
        // Filtros
        $dataInicio = $request->get('data_inicio', Carbon::now()->subDays(30)->format('Y-m-d'));
        $dataFim = $request->get('data_fim', Carbon::now()->addDays(90)->format('Y-m-d'));
        $status = $request->get('status', 'todos');
        $visualizacao = $request->get('visualizacao', 'periodo'); // periodo, semana, mes, dia

        // Ajustar datas baseado na visualização
        if ($visualizacao === 'dia') {
            $dataInicio = $request->get('data', Carbon::now()->format('Y-m-d'));
            $dataFim = $dataInicio;
        } elseif ($visualizacao === 'semana') {
            $dataInicio = $request->get('data_inicio', Carbon::now()->startOfWeek()->format('Y-m-d'));
            $dataFim = $request->get('data_fim', Carbon::now()->endOfWeek()->format('Y-m-d'));
        } elseif ($visualizacao === 'mes') {
            $data = Carbon::parse($request->get('data', Carbon::now()));
            $dataInicio = $data->startOfMonth()->format('Y-m-d');
            $dataFim = $data->endOfMonth()->format('Y-m-d');
        }

        // Query base
        $query = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->with(['paciente', 'assinatura.plano'])
            ->whereBetween('scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ]);

        // Filtro por status
        if ($status !== 'todos') {
            $query->where('status', $status);
        }

        // Filtro de busca por nome do paciente
        $search = $request->get('search');
        if ($search) {
            $query->whereHas('paciente', function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%');
            });
        }

        $agendamentos = $query->orderBy('scheduled_at')
            ->get()
            ->map(function ($agendamento) {
                return [
                    'id' => $agendamento->id,
                    'scheduled_at' => $agendamento->scheduled_at ? $agendamento->scheduled_at->toISOString() : null,
                    'duration' => $agendamento->duration,
                    'status' => $agendamento->status,
                    'service_type' => $agendamento->service_type,
                    'notes' => $agendamento->notes,
                    'address' => $agendamento->address,
                    'price' => $agendamento->price ? (float) $agendamento->price : 0,
                    'paciente' => [
                        'id' => $agendamento->paciente->id,
                        'name' => $agendamento->paciente->name,
                        'email' => $agendamento->paciente->email,
                        'phone' => $agendamento->paciente->phone,
                        'avatar' => $agendamento->paciente->avatar,
                    ],
                    'assinatura' => $agendamento->assinatura ? [
                        'id' => $agendamento->assinatura->id,
                        'plano' => [
                            'id' => $agendamento->assinatura->plano->id,
                            'name' => $agendamento->assinatura->plano->name,
                        ],
                    ] : null,
                    'pode_iniciar' => in_array($agendamento->status, ['confirmado', 'a caminho']) &&
                                    Carbon::now()->diffInMinutes($agendamento->scheduled_at, false) <= 15,
                    'pode_marcar_caminho' => $agendamento->status === 'confirmado',
                    'pode_confirmar' => $agendamento->status === 'agendado',
                    'pode_cancelar' => in_array($agendamento->status, ['agendado', 'confirmado', 'a caminho']),
                ];
            });

        // Estatísticas do período
        $stats = [
            'total' => $agendamentos->count(),
            'agendados' => $agendamentos->where('status', 'agendado')->count(),
            'confirmados' => $agendamentos->where('status', 'confirmado')->count(),
            'concluidos' => $agendamentos->where('status', 'concluido')->count(),
            'cancelados' => $agendamentos->where('status', 'cancelado')->count(),
            'receita_periodo' => $agendamentos->where('status', 'concluido')->sum('preco'),
        ];

        // Horários disponíveis para novos agendamentos (próximos 30 dias)
        $horariosDisponiveis = $this->getHorariosDisponiveis($fisioterapeuta);

        return Inertia::render('fisioterapeuta/agenda', [
            'agendamentos' => [
                'data' => $agendamentos,
                'links' => [],
                'meta' => [
                    'total' => $agendamentos->count(),
                    'from' => 1,
                    'to' => $agendamentos->count(),
                ]
            ],
            'stats' => $stats,
            'filters' => [
                'date' => $request->get('data'),
                'status' => $status !== 'todos' ? $status : null,
                'search' => $request->get('search'),
            ],
            'horariosDisponiveis' => $horariosDisponiveis,
        ]);
    }

    public function show(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao fisioterapeuta
        if ($agendamento->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        $agendamento->load(['paciente', 'assinatura.plano', 'relatorioSessao']);

        return Inertia::render('fisioterapeuta/agenda/show', [
            'agendamento' => [
                'id' => $agendamento->id,
                'scheduled_at' => $agendamento->scheduled_at ? $agendamento->scheduled_at->toISOString() : null,
                'duration' => $agendamento->duration,
                'status' => $agendamento->status,
                'service_type' => $agendamento->service_type,
                'appointment_type' => $agendamento->appointment_type,
                'appointment_type_notes' => $agendamento->appointment_type_notes,
                'notes' => $agendamento->notes,
                'endereco_atendimento' => [
                    'logradouro' => $agendamento->address ?: 'Endereço não informado',
                    'complemento' => $agendamento->address_line2 ?: '',
                    'cidade' => $agendamento->address_city ?: '',
                    'estado' => $agendamento->address_state ?: '',
                    'cep' => $agendamento->address_zip_code ?: '',
                    'tipo' => $agendamento->address_type ?: 'residencia',
                ],
                'price' => $agendamento->price ? (float) $agendamento->price : 0,
                'started_at' => $agendamento->started_at ? $agendamento->started_at->toISOString() : null,
                'finished_at' => $agendamento->finished_at ? $agendamento->finished_at->toISOString() : null,
                'cancellation_reason' => $agendamento->cancellation_reason,
                'paciente' => [
                    'id' => $agendamento->paciente->id,
                    'name' => $agendamento->paciente->name,
                    'email' => $agendamento->paciente->email,
                    'phone' => $agendamento->paciente->phone,
                    'avatar' => $agendamento->paciente->avatar,
                ],
                'assinatura' => $agendamento->assinatura ? [
                    'id' => $agendamento->assinatura->id,
                    'plano' => [
                        'id' => $agendamento->assinatura->plano->id,
                        'name' => $agendamento->assinatura->plano->name,
                    ],
                ] : null,
                'relatorio' => $agendamento->relatorioSessao ? [
                    'id' => $agendamento->relatorioSessao->id,
                    'observacoes' => $agendamento->relatorioSessao->observations,
                    'exercicios' => $agendamento->relatorioSessao->exercises,
                    'evolucao' => $agendamento->relatorioSessao->progress_notes,
                    'proximos_passos' => $agendamento->relatorioSessao->next_steps,
                ] : null,
                'pode_iniciar' => in_array($agendamento->status, ['confirmado', 'a caminho']) &&
                                Carbon::now()->diffInMinutes($agendamento->scheduled_at, false) <= 15,
                'pode_marcar_caminho' => $agendamento->status === 'confirmado',
                'pode_confirmar' => $agendamento->status === 'agendado',
                'pode_cancelar' => in_array($agendamento->status, ['agendado', 'confirmado', 'a caminho']),
                'pode_finalizar' => $agendamento->status === 'em_andamento',
            ],
        ]);
    }

    public function confirmar(Agendamento $agendamento)
    {
        if ($agendamento->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        if ($agendamento->status !== 'agendado') {
            return back()->withErrors(['message' => 'Este agendamento não pode ser confirmado.']);
        }

        $agendamento->update(['status' => 'confirmado']);

        // Enviar notificação para o paciente
        $notificacaoService = new NotificacaoService();
        $notificacaoService->notificarAgendamentoConfirmado($agendamento);

        return back()->with('success', 'Agendamento confirmado com sucesso!');
    }

    public function marcarACaminho(Agendamento $agendamento)
    {
        if ($agendamento->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        if ($agendamento->status !== 'confirmado') {
            return back()->withErrors(['message' => 'Este agendamento não pode ser marcado como "a caminho".']);
        }

        $agendamento->update(['status' => 'a caminho']);

        // Enviar notificação para o paciente
        $notificacaoService = new NotificacaoService();
        $notificacaoService->notificarProfissionalACaminho($agendamento);

        return back()->with('success', 'Status atualizado: Profissional a caminho!');
    }

    public function iniciar(Agendamento $agendamento)
    {
        if ($agendamento->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        if (!in_array($agendamento->status, ['confirmado', 'a caminho'])) {
            return back()->withErrors(['message' => 'Este agendamento não pode ser iniciado.']);
        }

        // Verificar se está no horário correto (até 15 minutos antes)
        if (Carbon::now()->diffInMinutes($agendamento->scheduled_at, false) > 15) {
            return back()->withErrors(['message' => 'Só é possível iniciar a sessão até 15 minutos antes do horário agendado.']);
        }

        $agendamento->update([
            'status' => 'em_andamento',
            'started_at' => Carbon::now(),
        ]);

        // Enviar notificação para o paciente
        $notificacaoService = new NotificacaoService();
        $notificacaoService->notificarSessaoIniciada($agendamento);

        return back()->with('success', 'Sessão iniciada com sucesso!');
    }

    public function finalizar(Request $request, Agendamento $agendamento)
    {
        if ($agendamento->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        if ($agendamento->status !== 'em_andamento') {
            return back()->withErrors(['message' => 'Este agendamento não pode ser finalizado.']);
        }

        $agendamento->update([
            'status' => 'concluido',
            'finished_at' => Carbon::now(),
        ]);

        // Ao concluir a sessão, incrementar o uso da assinatura do paciente
        // para descontar das sessões disponíveis do plano
        if ($agendamento->assinatura) {
            $assinatura = $agendamento->assinatura()->with('plano')->first();
            if ($assinatura && $assinatura->plano) {
                $limite = (int) ($assinatura->plano->sessions_per_month ?? 0);
                $usadas = (int) ($assinatura->sessions_used ?? 0);
                // Incrementa 1, respeitando o limite do plano
                $novoValor = $limite > 0 ? min($usadas + 1, $limite) : $usadas + 1;
                if ($novoValor !== $usadas) {
                    $assinatura->update(['sessions_used' => $novoValor]);
                }
            }
        }

        // Enviar notificação para o paciente
        $notificacaoService = new NotificacaoService();
        $notificacaoService->notificarSessaoFinalizada($agendamento);

        return redirect()->route('fisioterapeuta.relatorios.create', $agendamento)
            ->with('success', 'Sessão finalizada! Agora preencha o relatório.');
    }

    public function aceitar(Agendamento $agendamento)
    {
        if ($agendamento->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        if ($agendamento->status !== 'agendado') {
            return back()->withErrors(['message' => 'Este agendamento não pode ser aceito.']);
        }

        $agendamento->update(['status' => 'confirmado']);

        // Enviar notificação para o paciente
        $notificacaoService = new NotificacaoService();
        $notificacaoService->notificarAgendamentoConfirmado($agendamento);

        return back()->with('success', 'Agendamento aceito com sucesso!');
    }

    public function recusar(Request $request, Agendamento $agendamento)
    {
        if ($agendamento->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        if ($agendamento->status !== 'agendado') {
            return back()->withErrors(['message' => 'Este agendamento não pode ser recusado.']);
        }

        $request->validate([
            'motivo' => 'required|string|max:500',
        ]);

        $agendamento->update([
            'status' => 'cancelado',
            'cancellation_reason' => $request->motivo,
        ]);

        // Enviar notificação para o paciente
        $notificacaoService = new NotificacaoService();
        $notificacaoService->notificarAgendamentoCancelado($agendamento, $request->motivo);

        return back()->with('success', 'Agendamento recusado com sucesso!');
    }

    public function reagendar(Request $request, Agendamento $agendamento)
    {
        if ($agendamento->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        if (!in_array($agendamento->status, ['agendado', 'confirmado', 'a caminho', 'em_andamento'])) {
            return back()->withErrors(['message' => 'Este agendamento não pode ser reagendado.']);
        }

        $request->validate([
            'data_hora' => 'required|date|after:now',
            'motivo' => 'nullable|string|max:500',
        ]);

        // Verificar disponibilidade do fisioterapeuta
        $conflito = Agendamento::where('fisioterapeuta_id', $agendamento->fisioterapeuta_id)
            ->where('id', '!=', $agendamento->id)
            ->where('scheduled_at', $request->data_hora)
            ->whereIn('status', ['agendado', 'confirmado', 'em_andamento'])
            ->exists();

        if ($conflito) {
            return back()->withErrors(['data_hora' => 'Este horário não está disponível.']);
        }

        $dataHoraAntiga = $agendamento->scheduled_at;

        $agendamento->update([
            'scheduled_at' => $request->data_hora,
            'status' => 'agendado', // Resetar para agendado
            'notes' => $request->motivo ?
                ($agendamento->notes ? $agendamento->notes . "\n\nReagendado: " . $request->motivo : "Reagendado: " . $request->motivo)
                : $agendamento->notes,
        ]);

        // Enviar notificação para o paciente
        $notificacaoService = new NotificacaoService();
        $notificacaoService->notificarAgendamentoReagendado($agendamento, $dataHoraAntiga, $request->motivo);

        return back()->with('success', 'Agendamento reagendado com sucesso!');
    }

    public function cancelar(Request $request, Agendamento $agendamento)
    {
        if ($agendamento->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        if (!in_array($agendamento->status, ['agendado', 'confirmado', 'a caminho'])) {
            return back()->withErrors(['message' => 'Este agendamento não pode ser cancelado.']);
        }

        $request->validate([
            'motivo' => 'required|string|max:500',
        ]);

        $agendamento->update([
            'status' => 'cancelado',
            'cancellation_reason' => $request->motivo,
        ]);

        // Enviar notificação para o paciente
        $notificacaoService = new NotificacaoService();
        $notificacaoService->notificarAgendamentoCancelado($agendamento, $request->motivo);

        return back()->with('success', 'Agendamento cancelado com sucesso!');
    }

    private function getHorariosDisponiveis($fisioterapeuta)
    {
        // Implementar lógica para calcular horários disponíveis
        // baseado nos working_hours do fisioterapeuta e agendamentos existentes
        $workingHours = $fisioterapeuta->fisioterapeuta->working_hours ?? [];
        
        // Por simplicidade, retornando array vazio
        // Em implementação real, calcular slots disponíveis
        return [];
    }
}
