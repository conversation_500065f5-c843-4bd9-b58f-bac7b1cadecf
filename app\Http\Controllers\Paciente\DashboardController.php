<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Agendamento;
use App\Models\Assinatura;
use App\Models\Avaliacao;
use App\Models\Pagamento;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the patient dashboard.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Estatísticas do paciente
        $stats = [
            'proximoAgendamento' => $this->getProximoAgendamento($user->id),
            'sessoesMes' => $this->getSessoesMes($user->id),
            'sessoesRestantes' => $this->getSessoesRestantes($user->id),
            'ultimaAvaliacao' => $this->getUltimaAvaliacao($user->id),
        ];

        // Agendamentos próximos (próximos 7 dias)
        $proximosAgendamentos = Agendamento::where('paciente_id', $user->id)
            ->where('scheduled_at', '>=', Carbon::now())
            ->where('scheduled_at', '<=', Carbon::now()->addDays(7))
            ->where('status', '!=', 'cancelado')
            ->with(['fisioterapeuta.user'])
            ->orderBy('scheduled_at', 'asc')
            ->limit(5)
            ->get();

        // Histórico recente (últimas 5 sessões)
        $historicoRecente = Agendamento::where('paciente_id', $user->id)
            ->where('status', 'concluido')
            ->with(['fisioterapeuta.user'])
            ->orderBy('scheduled_at', 'desc')
            ->limit(5)
            ->get();

        // Informações do plano atual
        $planoAtual = null;
        if ($user->has_subscription) {
            $assinatura = Assinatura::where('user_id', $user->id)
                ->where('status', 'ativa')
                ->with('plano')
                ->first();

            if ($assinatura) {
                $planoAtual = $assinatura;

                // Determinar o tipo de plano baseado no preço
                if ($assinatura->monthly_price == 14.80) {
                    $assinatura->type = 'busca';
                    $assinatura->plano_name = 'Plano Busca';
                } elseif ($assinatura->monthly_price == 180.00) {
                    $assinatura->type = 'pessoal';
                    $assinatura->plano_name = 'Plano Pessoal';
                } elseif ($assinatura->monthly_price == 640.00) {
                    $assinatura->type = 'empresarial';
                    $assinatura->plano_name = 'Plano Empresarial';
                }
            }
        }

        // Pagamentos pendentes
        $pagamentosPendentes = Pagamento::whereHas('assinatura', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })
            ->where('status', 'pendente')
            ->orderBy('due_date', 'asc')
            ->limit(3)
            ->get();

        return Inertia::render('paciente/dashboard', [
            'stats' => $stats,
            'proximosAgendamentos' => $proximosAgendamentos,
            'historicoRecente' => $historicoRecente,
            'planoAtual' => $planoAtual,
            'pagamentosPendentes' => $pagamentosPendentes,
        ]);
    }

    /**
     * Get next appointment for the patient
     */
    private function getProximoAgendamento($pacienteId)
    {
        return Agendamento::where('paciente_id', $pacienteId)
            ->where('scheduled_at', '>', Carbon::now())
            ->where('status', '!=', 'cancelado')
            ->with(['fisioterapeuta.user'])
            ->orderBy('scheduled_at', 'asc')
            ->first();
    }

    /**
     * Get sessions count for current month
     */
    private function getSessoesMes($pacienteId)
    {
        return Agendamento::where('paciente_id', $pacienteId)
            ->where('status', 'concluido')
            ->whereMonth('scheduled_at', Carbon::now()->month)
            ->whereYear('scheduled_at', Carbon::now()->year)
            ->count();
    }

    /**
     * Get remaining sessions based on current plan
     */
    private function getSessoesRestantes($pacienteId)
    {
        $user = User::find($pacienteId);

        if (!$user || !$user->has_subscription) {
            return 0;
        }

        $assinatura = Assinatura::where('user_id', $pacienteId)
            ->where('status', 'ativa')
            ->with('plano')
            ->first();

        if ($assinatura) {
            // Plano mensal com limite de sessões
            $sessoesUsadas = $this->getSessoesMes($pacienteId);
            $sessoesPlano = $assinatura->plano->sessions_per_month;
            return max(0, $sessoesPlano - $sessoesUsadas);
        } else {
            // Sessão avulsa - ilimitado
            return 999;
        }
    }

    /**
     * Get last evaluation
     */
    private function getUltimaAvaliacao($pacienteId)
    {
        return Avaliacao::where('paciente_id', $pacienteId)
            ->with(['fisioterapeuta.user'])
            ->orderBy('created_at', 'desc')
            ->first();
    }
}
