<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificar Email - F4 Fisio</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background-color: #ffffff;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            width: 120px;
            height: auto;
            margin: 0 auto 20px;
            display: block;
        }
        .title {
            color: #1e293b;
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 10px 0;
        }
        .subtitle {
            color: #64748b;
            font-size: 16px;
            margin: 0;
        }
        .content {
            margin: 30px 0;
        }
        .greeting {
            font-size: 18px;
            color: #1e293b;
            margin-bottom: 20px;
        }
        .message {
            color: #475569;
            margin-bottom: 25px;
            line-height: 1.7;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, oklch(0.8542 0.2851 143.0785), oklch(0.7357 0.2444 143.2345));
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .cta-button:hover {
            transform: translateY(-2px);
        }
        .info-box {
            background-color: oklch(0.9834 0.0042 236.4956);
            border-left: 4px solid oklch(0.8542 0.2851 143.0785);
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        .info-title {
            color: oklch(0.7357 0.2444 143.2345);
            font-weight: 600;
            margin-bottom: 10px;
        }
        .info-list {
            color: oklch(0.7357 0.2444 143.2345);
            margin: 0;
            padding-left: 20px;
        }
        .info-list li {
            margin-bottom: 5px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #64748b;
            font-size: 14px;
        }
        .footer-links {
            margin-top: 15px;
        }
        .footer-links a {
            color: oklch(0.8542 0.2851 143.0785);
            text-decoration: none;
            margin: 0 10px;
        }
        .security-note {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-size: 14px;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{ config('app.url') }}/images/logo.png" alt="F4 Fisio" class="logo">
            <h1 class="title">Verificar Email</h1>
            <p class="subtitle">Confirme seu email para continuar como fisioterapeuta</p>
        </div>

        <div class="content">
            <p class="greeting">Olá, <strong>{{ $user->name }}</strong>!</p>
            
            <p class="message">
                Bem-vindo à plataforma F4 Fisio! Para garantir a segurança da sua conta e prosseguir com o processo de aprovação profissional, precisamos verificar seu endereço de email.
            </p>

            <div style="text-align: center;">
                <a href="{{ $verificationUrl }}" class="cta-button">
                    ✓ Verificar Meu Email
                </a>
            </div>

            <div class="info-box">
                <div class="info-title">Por que verificar meu email?</div>
                <ul class="info-list">
                    <li>Garantir a segurança e autenticidade da sua conta</li>
                    <li>Receber notificações importantes sobre agendamentos</li>
                    <li>Prosseguir com a análise e aprovação do seu perfil profissional</li>
                    <li>Acessar todas as funcionalidades da plataforma</li>
                </ul>
            </div>

            <p class="message">
                Após verificar seu email, você poderá completar seu perfil profissional e aguardar a aprovação da nossa equipe para começar a atender pacientes.
            </p>

            <div class="security-note">
                <strong>🔒 Nota de Segurança:</strong> Este link é válido por 60 minutos e só pode ser usado uma vez. Se você não solicitou esta verificação, ignore este email.
            </div>
        </div>

        <div class="footer">
            <p>Este email foi enviado automaticamente pelo sistema F4 Fisio.</p>
            <p>Se você não conseguir clicar no botão acima, copie e cole este link no seu navegador:</p>
            <p style="word-break: break-all; color: oklch(0.8542 0.2851 143.0785); font-size: 12px;">{{ $verificationUrl }}</p>
            
            <div class="footer-links">
                <a href="{{ config('app.url') }}">F4 Fisio</a> |
                <a href="{{ config('app.url') }}/contato">Suporte</a> |
                <a href="{{ config('app.url') }}/politica-privacidade">Privacidade</a>
            </div>
            
            <p style="margin-top: 20px; font-size: 12px;">
                © {{ date('Y') }} F4 Fisio. Todos os direitos reservados.
            </p>
        </div>
    </div>
</body>
</html>
