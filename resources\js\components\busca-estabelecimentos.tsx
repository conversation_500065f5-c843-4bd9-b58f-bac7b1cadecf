import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import Combobox from '@/components/ui/combobox';
import { Label } from '@/components/ui/label';
import { MapPin, Search, Star } from 'lucide-react';
import { useEffect, useState } from 'react';

interface FisioterapeutaPublic {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    crefito: string;
    specializations: string[];
    available_areas: string[];
    rating: number;
    total_reviews: number;
    hourly_rate: number;
    session_rate?: number | null;
    pricing_mode?: 'por_hora' | 'por_sessao' | null;
}

interface BuscaResponse {
    success: boolean;
    fisioterapeutas: FisioterapeutaPublic[];
    total: number;
    message?: string;
    mensagem?: {
        tipo: string;
        titulo: string;
        descricao: string;
        sugestoes: string[];
    };
    filtros?: {
        avaliacao_minima?: number;
        ordenacao?: string;
    };
    pagination?: {
        current_page: number;
        per_page: number;
        total: number;
        total_pages: number;
        has_next_page: boolean;
        has_prev_page: boolean;
        from: number;
        to: number;
    };
}

export default function BuscaEstabelecimentos() {
    const [nome, setNome] = useState('');
    const [regiao, setRegiao] = useState<string>('');
    const [opcoesRegioes, setOpcoesRegioes] = useState<{ value: string; label: string }[]>([]);
    const [carregandoRegioes, setCarregandoRegioes] = useState(false);
    const [fisioterapeutas, setFisioterapeutas] = useState<FisioterapeutaPublic[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [buscaRealizada, setBuscaRealizada] = useState(false);
    const [mensagem, setMensagem] = useState<any>(null);
    const [filtros, setFiltros] = useState<any>(null);

    // Filtros (agora integrados no formulário principal)
    const [avaliacaoMinima, setAvaliacaoMinima] = useState<number | undefined>();
    const [ordenacao, setOrdenacao] = useState('avaliacao');

    // Paginação
    const [paginacao, setPaginacao] = useState<any>(null);
    const [paginaAtual, setPaginaAtual] = useState(1);

    // Buscar top 10 automaticamente ao abrir a página
    const [initialized, setInitialized] = useState(false);
    useEffect(() => {
        if (!initialized) {
            buscarEstabelecimentos(1);
            setInitialized(true);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [initialized]);

    // Carregar opções de regiões (cidades/bairros) para o combobox
    useEffect(() => {
        const loadRegioes = async () => {
            setCarregandoRegioes(true);
            try {
                const res = await fetch('/api/fisioterapeutas/regioes');
                const json = await res.json();
                if (json?.success && Array.isArray(json.options)) {
                    setOpcoesRegioes(json.options);
                }
            } catch (e) {
                // silencioso
            } finally {
                setCarregandoRegioes(false);
            }
        };
        loadRegioes();
    }, []);

    const buscarEstabelecimentos = async (pagina = 1) => {

        setLoading(true);
        setError('');

        try {
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
            const response = await fetch('/api/fisioterapeutas/buscar', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    Accept: 'application/json',
                },
                body: JSON.stringify({
                    ...(nome.trim() ? { nome: nome.trim() } : {}),
                    ...(regiao ? { localizacao: regiao } : {}),
                    avaliacao_minima: avaliacaoMinima,
                    ordenacao,
                    page: pagina,
                    per_page: 10,
                }),
            });

            const data: BuscaResponse = await response.json();

            if (data.success) {
                setFisioterapeutas(data.fisioterapeutas);
                setMensagem(data.mensagem);
                setFiltros(data.filtros);
                setPaginacao(data.pagination);
                setPaginaAtual(pagina);
                setBuscaRealizada(true);
                setError('');
            } else {
                setError(data.message || 'Erro ao buscar estabelecimentos');
                setFisioterapeutas([]);
                setMensagem(null);
                setFiltros(null);
                setPaginacao(null);
            }
        } catch (err) {
            setError('Erro ao conectar com o servidor');
        } finally {
            setLoading(false);
        }
    };

    // geolocalização removida (não há mais input manual de localização)

    return (
        <div className="mx-auto w-full max-w-6xl">
            {/* Formulário de Busca */}
            <Card className="mb-8 shadow-sm">
                <CardHeader className="text-center">
                    <CardTitle className="flex items-center justify-center gap-2 text-xl font-medium">
                        <Search className="h-5 w-5 text-primary" />
                        Encontre fisioterapeutas perto de você
                    </CardTitle>
                    <CardDescription className="text-base">
                        Busque por nome e refine por região, avaliação e ordenação.
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
                        <div className="space-y-2">
                            <Label htmlFor="nome" className="text-sm font-medium">
                                Nome do fisioterapeuta
                            </Label>
                            <Input
                                id="nome"
                                placeholder="Ex.: Ana, João, Silva..."
                                value={nome}
                                onChange={(e) => setNome(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && buscarEstabelecimentos()}
                                className="flex-1"
                            />
                        </div>
                        {/* Região */}
                        <div className="space-y-2">
                            <Label className="text-sm font-medium">Região</Label>
                            <Combobox
                                options={opcoesRegioes}
                                value={regiao}
                                onValueChange={(v) => setRegiao(v)}
                                placeholder={carregandoRegioes ? 'Carregando regiões...' : 'Selecione uma região'}
                                searchPlaceholder="Buscar cidade/bairro..."
                                emptyText="Nenhuma região encontrada"
                            />
                        </div>
                        
                        {/* Ordenação */}
                        <div className="space-y-2">
                            <Label htmlFor="ordenacao" className="text-sm font-medium">
                                Ordenar por
                            </Label>
                            <select
                                id="ordenacao"
                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                                value={ordenacao}
                                onChange={(e) => setOrdenacao(e.target.value)}
                            >
                                <option value="nome">Nome (A-Z)</option>
                                <option value="avaliacao">Melhor Avaliação</option>
                                <option value="recente">Mais Recente</option>
                            </select>
                        </div>
                        <div className="flex items-end">
                            <Button onClick={() => buscarEstabelecimentos(1)} disabled={loading} className="w-full">
                                {loading ? (
                                    <>
                                        <svg className="mr-2 h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                            ></path>
                                        </svg>
                                        Buscando...
                                    </>
                                ) : (
                                    <>
                                        <Search className="mr-2 h-4 w-4" />
                                        Buscar
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>
                    {/* Avaliação Mínima - linha separada para mobile-friendly */}
                    <div className="space-y-2">
                        <Label className="text-sm font-medium">
                            Avaliação Mínima: {avaliacaoMinima ? `${avaliacaoMinima} estrelas` : 'Qualquer'}
                        </Label>
                        <div className="px-2">
                            <input
                                type="range"
                                min="0"
                                max="5"
                                step="0.5"
                                value={avaliacaoMinima || 0}
                                onChange={(e) => setAvaliacaoMinima(Number(e.target.value) || undefined)}
                                className="w-full"
                            />
                            <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                                <span>0</span>
                                <span>2.5</span>
                                <span>5</span>
                            </div>
                        </div>
                    </div>

                    {error && (
                        <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-600">
                            <div className="flex items-center gap-2">
                                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                </svg>
                                {error}
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Resultados */}
            {buscaRealizada && (
                <div className="space-y-6">
                    {/* Mensagem Descritiva */}
                    {mensagem && (
                        <Card
                            className={`shadow-sm ${
                                mensagem.tipo === 'sucesso'
                                    ? 'border-green-200 bg-green-50'
                                    : mensagem.tipo === 'fora_do_raio'
                                      ? 'border-yellow-200 bg-yellow-50'
                                      : 'border-blue-200 bg-blue-50'
                            }`}
                        >
                            <CardContent className="p-6">
                                <h3 className="mb-2 text-lg font-semibold">{mensagem.titulo}</h3>
                                <p className="mb-4 text-muted-foreground">{mensagem.descricao}</p>
                                {mensagem.sugestoes.length > 0 && (
                                    <div>
                                        <p className="mb-2 font-medium">Sugestões:</p>
                                        <ul className="list-inside list-disc space-y-1 text-sm text-muted-foreground">
                                            {mensagem.sugestoes.map((sugestao: string, index: number) => (
                                                <li key={index}>{sugestao}</li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    )}

                    <div className="text-center">
                        {fisioterapeutas.length > 0 && (
                            <>
                                <h3 className="text-xl font-medium">
                                    {`${paginacao?.total || fisioterapeutas.length} fisioterapeuta${(paginacao?.total || fisioterapeutas.length) > 1 ? 's' : ''} encontrado${(paginacao?.total || fisioterapeutas.length) > 1 ? 's' : ''}`}
                                </h3>
                                <p className="mt-2 text-muted-foreground">Veja detalhes de avaliação, áreas de atendimento e valores</p>
                            </>
                        )}
                    </div>

                    {fisioterapeutas.length === 0 && (
                        <Card className="shadow-sm">
                            <CardContent className="py-12 text-center">
                                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                                    <Search className="h-6 w-6 text-muted-foreground" />
                                </div>
                                <p className="mb-2 text-base font-medium text-foreground">Nenhum fisioterapeuta encontrado</p>
                                <p className="text-sm text-muted-foreground">Tente ajustar a localização ou filtros.</p>
                            </CardContent>
                        </Card>
                    )}

                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        {fisioterapeutas.map((fisio) => (
                            <FisioterapeutaCard key={fisio.id} fisio={fisio} />
                        ))}
                    </div>

                    {/* Paginação */}
                    {paginacao && paginacao.total_pages > 1 && (
                        <div className="mt-8 flex flex-col items-center gap-4">
                            <div className="text-sm text-muted-foreground">
                                Mostrando {paginacao.from} a {paginacao.to} de {paginacao.total} resultados
                            </div>
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => buscarEstabelecimentos(paginaAtual - 1)}
                                    disabled={!paginacao.has_prev_page || loading}
                                >
                                    Anterior
                                </Button>

                                {/* Páginas */}
                                {Array.from({ length: Math.min(5, paginacao.total_pages) }, (_, i) => {
                                    const pageNum = Math.max(1, Math.min(paginacao.total_pages - 4, paginaAtual - 2)) + i;
                                    if (pageNum > paginacao.total_pages) return null;

                                    return (
                                        <Button
                                            key={pageNum}
                                            variant={pageNum === paginaAtual ? 'default' : 'outline'}
                                            size="sm"
                                            onClick={() => buscarEstabelecimentos(pageNum)}
                                            disabled={loading}
                                        >
                                            {pageNum}
                                        </Button>
                                    );
                                })}

                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => buscarEstabelecimentos(paginaAtual + 1)}
                                    disabled={!paginacao.has_next_page || loading}
                                >
                                    Próxima
                                </Button>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}

function FisioterapeutaCard({ fisio }: { fisio: FisioterapeutaPublic }) {
    return (
        <Card className="h-full shadow-sm transition-shadow hover:shadow-md">
            <CardHeader className="pb-4">
                <CardTitle className="text-base font-medium">{fisio.name}</CardTitle>
                <div className="mt-1 flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">CREFITO: {fisio.crefito}</Badge>
                    {fisio.rating > 0 && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Star className="h-3.5 w-3.5 fill-current text-yellow-500" />
                            <span className="font-medium">{fisio.rating.toFixed(1)}</span>
                            <span>({fisio.total_reviews})</span>
                        </div>
                    )}
                </div>
            </CardHeader>
            <CardContent className="space-y-3 pt-0">
                {fisio.specializations?.length > 0 && (
                    <div className="text-sm">
                        <span className="font-medium">Especializações: </span>
                        <span className="text-muted-foreground">{fisio.specializations.slice(0, 3).join(', ')}{fisio.specializations.length > 3 ? ' +' + (fisio.specializations.length - 3) : ''}</span>
                    </div>
                )}
                {fisio.available_areas?.length > 0 && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <MapPin className="h-4 w-4" />
                        <span>{fisio.available_areas.slice(0, 2).join(', ')}{fisio.available_areas.length > 2 ? ' +' + (fisio.available_areas.length - 2) : ''}</span>
                    </div>
                )}
                <div className="text-sm">
                    <span className="font-medium">Valor: </span>
                    <span className="text-muted-foreground">
                        {(() => {
                            const isHourly = fisio.pricing_mode === 'por_hora';
                            const price = isHourly ? fisio.hourly_rate : (fisio.session_rate ?? fisio.hourly_rate);
                            const label = isHourly ? 'hora' : 'sessão';
                            return price ? `R$ ${Number(price).toFixed(2)}/${label}` : 'a combinar';
                        })()}
                    </span>
                </div>
            </CardContent>
        </Card>
    );
}
