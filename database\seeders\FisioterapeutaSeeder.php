<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Fisioterapeuta;

class FisioterapeutaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $fisioterapeutas = [
            [
                'user' => [
                    'name' => 'Dr. <PERSON>',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('fisio123'),
                    'role' => 'fisioterapeuta',
                    'active' => true,
                ],
                'fisioterapeuta' => [
                    'crefito' => 'CREFITO-3/123456-F',
                    'specializations' => ['Ortopédica', 'Esportiva'],
                    'bio' => 'Especialista em fisioterapia ortopédica e esportiva com mais de 10 anos de experiência.',
                    'hourly_rate' => 80.00,
                    'available_areas' => ['São Paulo', 'Zona Sul', 'Zona Oeste'],
                    'working_hours' => [
                        'monday' => ['start' => '08:00', 'end' => '18:00', 'available' => true],
                        'tuesday' => ['start' => '08:00', 'end' => '18:00', 'available' => true],
                        'wednesday' => ['start' => '08:00', 'end' => '18:00', 'available' => true],
                        'thursday' => ['start' => '08:00', 'end' => '18:00', 'available' => true],
                        'friday' => ['start' => '08:00', 'end' => '18:00', 'available' => true],
                        'saturday' => ['start' => '08:00', 'end' => '14:00', 'available' => true],
                        'sunday' => ['start' => '09:00', 'end' => '13:00', 'available' => false],
                    ],
                    'available' => true,
                    'rating' => 4.8,
                    'total_reviews' => 25,
                ]
            ],
            [
                'user' => [
                    'name' => 'Dra. Maria Santos',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('fisio123'),
                    'role' => 'fisioterapeuta',
                    'active' => true,
                ],
                'fisioterapeuta' => [
                    'crefito' => 'CREFITO-3/234567-F',
                    'specializations' => ['Neurológica', 'Geriátrica'],
                    'bio' => 'Especialista em reabilitação neurológica e cuidados geriátricos.',
                    'hourly_rate' => 90.00,
                    'available_areas' => ['São Paulo', 'Zona Norte', 'Centro'],
                    'working_hours' => [
                        'monday' => ['start' => '09:00', 'end' => '17:00', 'available' => true],
                        'tuesday' => ['start' => '09:00', 'end' => '17:00', 'available' => true],
                        'wednesday' => ['start' => '09:00', 'end' => '17:00', 'available' => true],
                        'thursday' => ['start' => '09:00', 'end' => '17:00', 'available' => true],
                        'friday' => ['start' => '09:00', 'end' => '17:00', 'available' => true],
                        'saturday' => ['start' => '09:00', 'end' => '13:00', 'available' => false],
                        'sunday' => ['start' => '09:00', 'end' => '13:00', 'available' => false],
                    ],
                    'available' => true,
                    'rating' => 4.9,
                    'total_reviews' => 32,
                ]
            ],
            [
                'user' => [
                    'name' => 'Dr. João Oliveira',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('fisio123'),
                    'role' => 'fisioterapeuta',
                    'active' => true,
                ],
                'fisioterapeuta' => [
                    'crefito' => 'CREFITO-3/345678-F',
                    'specializations' => ['Respiratória', 'Intensiva'],
                    'bio' => 'Especialista em fisioterapia respiratória e cuidados intensivos.',
                    'hourly_rate' => 95.00,
                    'available_areas' => ['São Paulo', 'Zona Leste', 'ABC'],
                    'working_hours' => [
                        'monday' => ['start' => '07:00', 'end' => '19:00', 'available' => true],
                        'tuesday' => ['start' => '07:00', 'end' => '19:00', 'available' => true],
                        'wednesday' => ['start' => '07:00', 'end' => '19:00', 'available' => true],
                        'thursday' => ['start' => '07:00', 'end' => '19:00', 'available' => true],
                        'friday' => ['start' => '07:00', 'end' => '19:00', 'available' => true],
                        'saturday' => ['start' => '07:00', 'end' => '12:00', 'available' => true],
                        'sunday' => ['start' => '09:00', 'end' => '13:00', 'available' => false],
                    ],
                    'available' => true,
                    'rating' => 4.7,
                    'total_reviews' => 18,
                ]
            ],
            [
                'user' => [
                    'name' => 'Dra. Ana Costa',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('fisio123'),
                    'role' => 'fisioterapeuta',
                    'active' => true,
                ],
                'fisioterapeuta' => [
                    'crefito' => 'CREFITO-3/456789-F',
                    'specializations' => ['Pediátrica', 'Neurológica'],
                    'bio' => 'Especialista em fisioterapia pediátrica e desenvolvimento infantil.',
                    'hourly_rate' => 85.00,
                    'available_areas' => ['São Paulo', 'Zona Sul', 'Vila Madalena'],
                    'working_hours' => [
                        'monday' => ['start' => '08:00', 'end' => '16:00', 'available' => true],
                        'tuesday' => ['start' => '08:00', 'end' => '16:00', 'available' => true],
                        'wednesday' => ['start' => '08:00', 'end' => '16:00', 'available' => true],
                        'thursday' => ['start' => '08:00', 'end' => '16:00', 'available' => true],
                        'friday' => ['start' => '08:00', 'end' => '16:00', 'available' => true],
                        'saturday' => ['start' => '09:00', 'end' => '13:00', 'available' => false],
                        'sunday' => ['start' => '09:00', 'end' => '13:00', 'available' => false],
                    ],
                    'available' => true,
                    'rating' => 4.9,
                    'total_reviews' => 28,
                ]
            ],
            [
                'user' => [
                    'name' => 'Dr. Pedro Ferreira',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('fisio123'),
                    'role' => 'fisioterapeuta',
                    'active' => true,
                ],
                'fisioterapeuta' => [
                    'crefito' => 'CREFITO-3/567890-F',
                    'specializations' => ['Ortopédica', 'Traumato-Ortopédica'],
                    'bio' => 'Especialista em reabilitação pós-cirúrgica e traumato-ortopedia.',
                    'hourly_rate' => 100.00,
                    'available_areas' => ['São Paulo', 'Zona Oeste', 'Alphaville'],
                    'working_hours' => [
                        'monday' => ['start' => '06:00', 'end' => '20:00', 'available' => true],
                        'tuesday' => ['start' => '06:00', 'end' => '20:00', 'available' => true],
                        'wednesday' => ['start' => '06:00', 'end' => '20:00', 'available' => true],
                        'thursday' => ['start' => '06:00', 'end' => '20:00', 'available' => true],
                        'friday' => ['start' => '06:00', 'end' => '20:00', 'available' => true],
                        'saturday' => ['start' => '06:00', 'end' => '16:00', 'available' => true],
                        'sunday' => ['start' => '09:00', 'end' => '13:00', 'available' => false],
                    ],
                    'available' => true,
                    'rating' => 4.8,
                    'total_reviews' => 45,
                ]
            ]
        ];

        foreach ($fisioterapeutas as $data) {
            $user = User::create($data['user']);
            
            $fisioterapeuta = new Fisioterapeuta($data['fisioterapeuta']);
            $fisioterapeuta->user_id = $user->id;
            $fisioterapeuta->save();
        }
    }
}
