<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Assinatura;
use App\Models\Plano;
use Illuminate\Support\Facades\Auth;

class TestOnboardingFlow extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:onboarding-flow';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Testa o fluxo completo de onboarding do paciente';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Iniciando teste do fluxo de onboarding...');

        // 1. Buscar usuário de teste
        $user = User::where('email', '<EMAIL>')->first();

        if (!$user) {
            $this->error('❌ Usuário de teste não encontrado. Execute: php artisan db:seed --class=TestUserSeeder');
            return 1;
        }

        $this->info("✅ Usuário encontrado: {$user->name} ({$user->email})");

        // 2. Verificar estado inicial
        $this->info('📊 Estado inicial:');
        $this->line("   - Onboarding completo: " . ($user->onboarding_completed ? '✅' : '❌'));
        $this->line("   - Plano selecionado: " . ($user->plan_selected ? '✅' : '❌'));
        $this->line("   - Checkout completo: " . ($user->checkout_completed ? '✅' : '❌'));
        $this->line("   - Tem assinatura: " . ($user->has_subscription ? '✅' : '❌'));

        // 3. Simular preenchimento do onboarding
        $this->info('📝 Simulando preenchimento do onboarding...');

        $user->update([
            'phone' => '(11) 99999-9999',
            'birth_date' => '1990-01-01',
            'gender' => 'masculino',
            'address' => 'Rua Teste, 123',
            'city' => 'São Paulo',
            'state' => 'SP',
            'zip_code' => '01234-567',
            'medical_history' => 'Histórico médico de teste com mais de 20 caracteres para validação.',
            'emergency_contact' => 'Contato de emergência: João Silva - (11) 88888-8888',
            'main_objective' => 'alivio_dor',
            'pain_level' => '5',
            'specific_areas' => ['lombar', 'cervical'],
            'treatment_goals' => 'Reduzir dor nas costas e melhorar postura',
            'preferred_time' => 'manha',
            'preferred_days' => ['segunda', 'quarta', 'sexta'],
            'communication_preference' => 'whatsapp',
            'reminder_frequency' => 'daily',
            'onboarding_completed' => true,
            'onboarding_completed_at' => now(),
        ]);

        $this->info('✅ Onboarding completado!');

        // 4. Simular seleção de plano
        $this->info('💳 Simulando seleção de plano...');

        // Buscar ou criar plano de teste
        $plano = Plano::firstOrCreate([
            'name' => 'Plano Pessoal'
        ], [
            'description' => 'Plano pessoal de fisioterapia',
            'price' => 640.00,
            'sessions_per_month' => 4,
            'session_duration' => 60,
            'included_services' => ['Fisioterapia domiciliar', 'Relatórios de evolução'],
            'active' => true,
        ]);

        // Criar assinatura
        $assinatura = Assinatura::create([
            'user_id' => $user->id,
            'plano_id' => $plano->id,
            'status' => 'ativa',
            'start_date' => now(),
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
            'monthly_price' => $plano->price,
        ]);

        $user->update([
            'plan_selected' => true,
            'plan_selected_at' => now(),
        ]);

        $this->info('✅ Plano selecionado!');

        // 5. Simular checkout
        $this->info('🛒 Simulando checkout...');

        $user->update([
            'checkout_completed' => true,
            'checkout_completed_at' => now(),
            'has_subscription' => true,
        ]);

        $this->info('✅ Checkout completado!');

        // 6. Verificar estado final
        $user->refresh();
        $this->info('🎉 Estado final:');
        $this->line("   - Onboarding completo: " . ($user->onboarding_completed ? '✅' : '❌'));
        $this->line("   - Plano selecionado: " . ($user->plan_selected ? '✅' : '❌'));
        $this->line("   - Checkout completo: " . ($user->checkout_completed ? '✅' : '❌'));
        $this->line("   - Tem assinatura: " . ($user->has_subscription ? '✅' : '❌'));

        $this->info('🚀 Fluxo de onboarding testado com sucesso!');
        $this->info('🌐 Agora o usuário pode acessar: http://localhost:8000/dashboard');

        return 0;
    }
}
