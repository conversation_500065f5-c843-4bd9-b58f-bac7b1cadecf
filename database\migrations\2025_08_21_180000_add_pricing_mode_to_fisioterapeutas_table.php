<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('fisioterapeutas', function (Blueprint $table) {
            if (!Schema::hasColumn('fisioterapeutas', 'pricing_mode')) {
                $table->string('pricing_mode')->default('por_sessao');
            }
        });
    }

    public function down(): void
    {
        Schema::table('fisioterapeutas', function (Blueprint $table) {
            if (Schema::hasColumn('fisioterapeutas', 'pricing_mode')) {
                $table->dropColumn('pricing_mode');
            }
        });
    }
};
