<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Pagamento;
use App\Models\Assinatura;
use App\Models\VendaAfiliado;
use App\Services\MercadoPagoService;
use App\Services\UnifiedPaymentService;
use App\Services\AffiliateTrackingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Carbon\Carbon;

class PagamentoController extends Controller
{
    protected $mercadoPagoService;
    protected $unifiedPaymentService;
    protected $affiliateTrackingService;

    public function __construct(
        MercadoPagoService $mercadoPagoService,
        UnifiedPaymentService $unifiedPaymentService,
        AffiliateTrackingService $affiliateTrackingService
    ) {
        $this->mercadoPagoService = $mercadoPagoService;
        $this->unifiedPaymentService = $unifiedPaymentService;
        $this->affiliateTrackingService = $affiliateTrackingService;
    }
    /**
     * Display patient's payments
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $query = Pagamento::whereHas('assinatura', function ($q) use ($user) {
            $q->where('user_id', $user->id);
        })->with(['assinatura.plano']);

        // Filtro por status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filtro por período
        if ($request->filled('periodo')) {
            switch ($request->periodo) {
                case 'mes_atual':
                    $query->whereMonth('data_vencimento', Carbon::now()->month)
                          ->whereYear('data_vencimento', Carbon::now()->year);
                    break;
                case 'mes_anterior':
                    $query->whereMonth('data_vencimento', Carbon::now()->subMonth()->month)
                          ->whereYear('data_vencimento', Carbon::now()->subMonth()->year);
                    break;
                case 'ultimos_3_meses':
                    $query->whereBetween('data_vencimento', [
                        Carbon::now()->subMonths(3),
                        Carbon::now()
                    ]);
                    break;
                case 'ano_atual':
                    $query->whereYear('data_vencimento', Carbon::now()->year);
                    break;
            }
        }

        $pagamentos = $query->orderBy('data_vencimento', 'desc')
            ->paginate(15)
            ->withQueryString();

        // Estatísticas
        $stats = [
            'total_pago' => Pagamento::whereHas('assinatura', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })->pagos()->sum('amount'),
            'total_pendente' => Pagamento::whereHas('assinatura', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })->pendentes()->sum('amount'),
            'total_vencido' => Pagamento::whereHas('assinatura', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })->vencidos()->sum('amount'),
            'count_pendente' => Pagamento::whereHas('assinatura', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })->pendentes()->count(),
        ];

        // Próximos vencimentos
        $proximosVencimentos = Pagamento::whereHas('assinatura', function ($q) use ($user) {
            $q->where('user_id', $user->id);
        })->pendentes()
          ->whereBetween('data_vencimento', [Carbon::now(), Carbon::now()->addDays(30)])
          ->orderBy('data_vencimento', 'asc')
          ->limit(5)
          ->get();

        return Inertia::render('paciente/pagamentos/index', [
            'pagamentos' => $pagamentos,
            'filters' => $request->only(['status', 'periodo']),
            'stats' => $stats,
            'proximosVencimentos' => $proximosVencimentos,
        ]);
    }

    /**
     * Display the specified payment
     */
    public function show(Pagamento $pagamento)
    {
        $user = Auth::user();
        
        // Verificar se o pagamento pertence ao usuário
        $ownerByAssinatura = $pagamento->assinatura?->user_id;
        $ownerByAgendamento = $pagamento->agendamento?->paciente_id;
        if ($ownerByAssinatura !== $user->id && $ownerByAgendamento !== $user->id) {
            abort(403, 'Acesso negado.');
        }

        $pagamento->load(['assinatura.plano', 'agendamento']);

        return Inertia::render('paciente/pagamentos/show', [
            'pagamento' => $pagamento,
            'available_methods' => $this->unifiedPaymentService->getAvailablePaymentMethods(),
        ]);
    }

    /**
     * Process payment with specific method
     */
    public function processPaymentWithMethod(Request $request, Pagamento $pagamento)
    {
        $user = Auth::user();

        // Verificar se o pagamento pertence ao usuário
        if ($pagamento->assinatura->user_id !== $user->id) {
            abort(403, 'Acesso negado.');
        }

        if ($pagamento->status !== 'pendente') {
            return back()->with('error', 'Este pagamento não pode ser processado.');
        }

        $request->validate([
            'payment_method' => 'required|in:cartao_credito,cartao_debito,pix,boleto',
        ]);

        $paymentMethod = $request->input('payment_method');

        try {
            // Verificar se há venda de afiliado pendente
            $vendaAfiliado = VendaAfiliado::where('user_id', $user->id)
                ->where('status', 'pendente')
                ->first();

            $preferenceData = [
                'title' => 'Pagamento - ' . $pagamento->assinatura->plano->name,
                'description' => 'Assinatura mensal do plano ' . $pagamento->assinatura->plano->name,
                'amount' => $pagamento->amount,
                'payer' => [
                    'name' => $user->name,
                    'email' => $user->email,
                ],
                'external_reference' => $pagamento->id,
                'payment_methods' => $paymentMethod,
                'success_url' => url('/success'),
                'failure_url' => url('/failure'),
                'pending_url' => url('/pending'),
                'notification_url' => url('/webhook/mercadopago'),
            ];

            // Criar preferência usando o serviço unificado
            $preference = $this->unifiedPaymentService->createUnifiedPayment($preferenceData);

            if (!$preference['success']) {
                if (isset($preference['redirect_whatsapp']) && $preference['redirect_whatsapp']) {
                    // Redirecionar para WhatsApp se MP não estiver configurado
                    $whatsappUrl = config('app.whatsapp_url', 'https://wa.me/5511978196207');
                    $message = urlencode('Olá! Gostaria de realizar o pagamento da minha assinatura.');
                    return redirect($whatsappUrl . '?text=' . $message);
                }

                return back()->with('error', $preference['message']);
            }

            // Atualizar pagamento com dados da preferência
            $pagamento->update([
                'transaction_id' => $preference['preference_id'],
                'method' => $paymentMethod,
                'gateway_response' => $preference,
            ]);

            // Se há venda de afiliado, atualizar com o transaction_id
            if ($vendaAfiliado) {
                $vendaAfiliado->update([
                    'transaction_id' => $preference['preference_id'],
                ]);

                Log::info('Transaction ID adicionado à venda de afiliado', [
                    'venda_afiliado_id' => $vendaAfiliado->id,
                    'transaction_id' => $preference['preference_id'],
                ]);
            }

            // Redirecionar para o checkout do Mercado Pago
            $checkoutUrl = $preference['init_point'];
            return redirect($checkoutUrl);

        } catch (\Exception $e) {
            $pagamento->update([
                'status' => 'falhou',
                'notes' => 'Erro no processamento: ' . $e->getMessage(),
            ]);

            return back()->with('error', 'Erro ao processar pagamento. Tente novamente.');
        }
    }

    /**
     * Process payment with Mercado Pago (legacy method)
     */
    public function processPayment(Request $request, Pagamento $pagamento)
    {
        $user = Auth::user();

        // Verificar se o pagamento pertence ao usuário
        if ($pagamento->assinatura->user_id !== $user->id) {
            abort(403, 'Acesso negado.');
        }

        if ($pagamento->status !== 'pendente') {
            return back()->with('error', 'Este pagamento não pode ser processado.');
        }

        try {
            // Verificar se há afiliado rastreado e criar venda temporária
            $vendaAfiliado = null;
            $afiliado = $this->affiliateTrackingService->getCurrentAffiliate($request);

            if ($afiliado) {
                Log::info('Afiliado detectado no pagamento', [
                    'pagamento_id' => $pagamento->id,
                    'afiliado_id' => $afiliado->id,
                    'codigo_afiliado' => $afiliado->codigo_afiliado,
                    'user_id' => $user->id,
                ]);

                // Criar venda de afiliado temporária (será confirmada pelo webhook)
                $vendaAfiliado = $this->affiliateTrackingService->createAffiliateSale(
                    $user,
                    $pagamento->assinatura_id,
                    $this->mapPlanoTipo($pagamento->assinatura->plano),
                    (float) $pagamento->amount,
                    $request
                );
            }

            // Preparar dados para o Mercado Pago
            $preferenceData = [
                'title' => 'Pagamento - ' . $pagamento->assinatura->plano->name,
                'description' => 'Assinatura mensal - ' . $pagamento->assinatura->plano->description,
                'amount' => $pagamento->amount,
                'external_reference' => $pagamento->id,
                'payer' => [
                    'name' => $user->name,
                    'email' => $user->email,
                ],
            ];

            // Criar preferência no Mercado Pago
            $preference = $this->mercadoPagoService->createPreference($preferenceData);

            if (!$preference['success']) {
                if (isset($preference['redirect_whatsapp']) && $preference['redirect_whatsapp']) {
                    // Redirecionar para WhatsApp se MP não estiver configurado
                    $whatsappUrl = config('app.whatsapp_url', 'https://wa.me/5511978196207');
                    $message = urlencode('Olá! Gostaria de realizar o pagamento da minha assinatura.');
                    return redirect($whatsappUrl . '?text=' . $message);
                }

                return back()->with('error', $preference['message']);
            }

            // Atualizar pagamento com dados da preferência
            $pagamento->update([
                'transaction_id' => $preference['preference_id'],
                'gateway_response' => $preference,
            ]);

            // Se há venda de afiliado, atualizar com o transaction_id
            if ($vendaAfiliado) {
                $vendaAfiliado->update([
                    'transaction_id' => $preference['preference_id'],
                ]);

                Log::info('Transaction ID adicionado à venda de afiliado', [
                    'venda_afiliado_id' => $vendaAfiliado->id,
                    'transaction_id' => $preference['preference_id'],
                ]);
            }

            // Redirecionar para o checkout do Mercado Pago
            $checkoutUrl = $preference['init_point'];
            return redirect($checkoutUrl);

        } catch (\Exception $e) {
            $pagamento->update([
                'status' => 'falhou',
                'notes' => 'Erro no processamento: ' . $e->getMessage(),
            ]);

            return back()->with('error', 'Erro ao processar pagamento. Tente novamente.');
        }
    }

    /**
     * Generate payment slip (boleto)
     */
    public function generateSlip(Pagamento $pagamento)
    {
        $user = Auth::user();
        
        // Verificar se o pagamento pertence ao usuário
        if ($pagamento->assinatura->user_id !== $user->id) {
            abort(403, 'Acesso negado.');
        }

        if ($pagamento->status !== 'pendente') {
            return back()->with('error', 'Não é possível gerar boleto para este pagamento.');
        }

        // Simular geração de boleto
        // Em um ambiente real, aqui seria feita a integração com o banco para gerar o boleto
        
        $boletoData = [
            'codigo_barras' => '23793.39126 60000.000000 00000.000000 0 00000000000000',
            'linha_digitavel' => '23793391266000000000000000000000000000000000',
            'nosso_numero' => str_pad($pagamento->id, 8, '0', STR_PAD_LEFT),
            'data_vencimento' => $pagamento->data_vencimento->format('d/m/Y'),
            'valor' => $pagamento->formatted_valor,
            'beneficiario' => [
                'nome' => config('app.name'),
                'cnpj' => '00.000.000/0001-00',
                'endereco' => 'Endereço da empresa',
            ],
            'pagador' => [
                'nome' => $user->name,
                'email' => $user->email,
                'documento' => '000.000.000-00', // Em um caso real, seria obtido do perfil do usuário
            ],
        ];

        return Inertia::render('paciente/pagamentos/boleto', [
            'pagamento' => $pagamento->load(['assinatura.plano']),
            'boleto' => $boletoData,
        ]);
    }

    /**
     * Generate PIX payment
     */
    public function generatePix(Pagamento $pagamento)
    {
        $user = Auth::user();
        
        // Verificar se o pagamento pertence ao usuário
        if ($pagamento->assinatura->user_id !== $user->id) {
            abort(403, 'Acesso negado.');
        }

        if ($pagamento->status !== 'pendente') {
            return back()->with('error', 'Não é possível gerar PIX para este pagamento.');
        }

        // Simular geração de PIX
        // Em um ambiente real, aqui seria feita a integração com o PSP para gerar o PIX
        
        $pixData = [
            'qr_code' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', // QR Code simulado
            'pix_key' => '00000000000191', // Chave PIX da empresa
            'codigo_pix' => '00020126580014BR.GOV.BCB.PIX0136' . uniqid() . '5204000053039865802BR5925' . config('app.name') . '6009SAO PAULO62070503***6304' . rand(1000, 9999),
            'valor' => $pagamento->amount,
            'data_expiracao' => Carbon::now()->addHours(24),
            'identificador' => 'PIX_' . $pagamento->id . '_' . uniqid(),
        ];

        return response()->json([
            'success' => true,
            'pix' => $pixData,
        ]);
    }

    /**
     * Download payment receipt
     */
    public function downloadReceipt(Pagamento $pagamento)
    {
        $user = Auth::user();
        
        // Verificar se o pagamento pertence ao usuário
        if ($pagamento->assinatura->user_id !== $user->id) {
            abort(403, 'Acesso negado.');
        }

        if ($pagamento->status !== 'pago') {
            return back()->with('error', 'Comprovante disponível apenas para pagamentos confirmados.');
        }

        $pagamento->load(['assinatura.plano']);

        // Simular geração de PDF do comprovante
        // Em um ambiente real, aqui seria usado uma biblioteca como DomPDF ou similar
        
        $receiptData = [
            'pagamento' => $pagamento,
            'empresa' => [
                'nome' => config('app.name'),
                'cnpj' => '00.000.000/0001-00',
                'endereco' => 'Endereço da empresa',
                'telefone' => '(11) 99999-9999',
            ],
            'cliente' => [
                'nome' => $user->name,
                'email' => $user->email,
            ],
            'data_emissao' => Carbon::now(),
        ];

        return Inertia::render('paciente/pagamentos/comprovante', [
            'receipt' => $receiptData,
        ]);
    }

    /**
     * Get payment history for dashboard
     */
    public function getHistory()
    {
        $user = Auth::user();
        
        $ultimosPagamentos = Pagamento::whereHas('assinatura', function ($q) use ($user) {
            $q->where('user_id', $user->id);
        })->with(['assinatura.plano'])
          ->orderBy('data_vencimento', 'desc')
          ->limit(5)
          ->get();

        return response()->json([
            'ultimosPagamentos' => $ultimosPagamentos,
        ]);
    }

    /**
     * Handle successful payment return from Mercado Pago
     */
    public function paymentSuccess(Request $request)
    {
        $paymentId = $request->get('payment_id');
        $status = $request->get('status');
        $externalReference = $request->get('external_reference');

        $message = 'Pagamento processado com sucesso!';

        if ($externalReference) {
            $pagamento = Pagamento::find($externalReference);
            if ($pagamento) {
                return redirect()->route('paciente.pagamentos.show', $pagamento)
                    ->with('success', $message);
            }
        }

        return redirect()->route('paciente.pagamentos.index')
            ->with('success', $message);
    }

    /**
     * Handle failed payment return from Mercado Pago
     */
    public function paymentFailure(Request $request)
    {
        $paymentId = $request->get('payment_id');
        $status = $request->get('status');
        $externalReference = $request->get('external_reference');

        $message = 'Pagamento não foi aprovado. Tente novamente ou entre em contato conosco.';

        if ($externalReference) {
            $pagamento = Pagamento::find($externalReference);
            if ($pagamento) {
                return redirect()->route('paciente.pagamentos.show', $pagamento)
                    ->with('error', $message);
            }
        }

        return redirect()->route('paciente.pagamentos.index')
            ->with('error', $message);
    }

    /**
     * Handle pending payment return from Mercado Pago
     */
    public function paymentPending(Request $request)
    {
        $paymentId = $request->get('payment_id');
        $status = $request->get('status');
        $externalReference = $request->get('external_reference');

        $message = 'Pagamento está sendo processado. Você será notificado quando for aprovado.';

        if ($externalReference) {
            $pagamento = Pagamento::find($externalReference);
            if ($pagamento) {
                return redirect()->route('paciente.pagamentos.show', $pagamento)
                    ->with('info', $message);
            }
        }

        return redirect()->route('paciente.pagamentos.index')
            ->with('info', $message);
    }

    /**
     * Mapear plano para tipo de afiliado
     */
    private function mapPlanoTipo($plano)
    {
        // Mapear baseado no nome ou ID do plano
        $planoName = strtolower($plano->name);

        if (str_contains($planoName, 'empresarial') || str_contains($planoName, 'empresa')) {
            return 'empresarial';
        } elseif (str_contains($planoName, 'busca') || str_contains($planoName, 'search')) {
            return 'busca';
        } else {
            return 'pessoal';
        }
    }
}
