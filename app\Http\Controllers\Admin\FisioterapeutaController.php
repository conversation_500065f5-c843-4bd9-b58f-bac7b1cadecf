<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Fisioterapeuta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class FisioterapeutaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Fisioterapeuta::with('user');

        // Filtro por status de aprovação
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        } else {
            // Filtro por status ativo/inativo (mantido para compatibilidade)
            if ($request->filled('active') && $request->active !== 'all') {
                $query->whereHas('user', function ($q) use ($request) {
                    $q->where('active', $request->boolean('active'));
                });
            }
        }

        // Busca por nome ou CREFITO
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('crefito', 'like', '%' . $request->search . '%')
                  ->orWhereHas('user', function ($userQuery) use ($request) {
                      $userQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $fisioterapeutas = $query->orderBy('created_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        return Inertia::render('admin/fisioterapeutas', [
            'fisioterapeutas' => $fisioterapeutas,
            'filters' => $request->only(['active', 'search']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Buscar usuários com role fisioterapeuta que ainda não têm perfil
        $usuariosDisponiveis = User::where('role', 'fisioterapeuta')
            ->whereDoesntHave('fisioterapeuta')
            ->get(['id', 'name', 'email']);

        return Inertia::render('admin/fisioterapeutas/create', [
            'usuariosDisponiveis' => $usuariosDisponiveis,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'crefito' => 'required|string|unique:fisioterapeutas',
            'specializations' => 'required|array|min:1',
            'bio' => 'nullable|string',
            'hourly_rate' => 'required|numeric|min:0',
            'available_areas' => 'required|array|min:1',
            'working_hours' => 'required|array',
        ]);

        DB::transaction(function () use ($validated) {
            Fisioterapeuta::create($validated);
        });

        return redirect()->route('admin.fisioterapeutas.index')
            ->with('success', 'Fisioterapeuta cadastrado com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Fisioterapeuta $fisioterapeuta)
    {
        $fisioterapeuta->load([
            'user',
            'agendamentos' => function ($query) {
                $query->with('paciente')->orderBy('data_hora', 'desc')->limit(10);
            },
            'avaliacoes' => function ($query) {
                $query->with('paciente')->orderBy('created_at', 'desc')->limit(5);
            }
        ]);

        return Inertia::render('admin/fisioterapeutas/show', [
            'fisioterapeuta' => $fisioterapeuta,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Fisioterapeuta $fisioterapeuta)
    {
        $fisioterapeuta->load('user');

        return Inertia::render('admin/fisioterapeutas/edit', [
            'fisioterapeuta' => $fisioterapeuta,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Fisioterapeuta $fisioterapeuta)
    {
        $validated = $request->validate([
            'crefito' => 'required|string|unique:fisioterapeutas,crefito,' . $fisioterapeuta->id,
            'specializations' => 'required|array|min:1',
            'bio' => 'nullable|string',
            'hourly_rate' => 'required|numeric|min:0',
            'available_areas' => 'required|array|min:1',
            'working_hours' => 'required|array',
            'status' => 'sometimes|in:pending,approved,rejected',
            'rejection_reason' => 'nullable|string|required_if:status,rejected',
        ]);

        // Se estiver atualizando o status para aprovado, garante que o usuário está ativo
        if (isset($validated['status']) && $validated['status'] === 'approved') {
            $fisioterapeuta->user->update(['active' => true]);
            
            // Limpa o motivo de rejeição se estiver aprovando
            if ($fisioterapeuta->rejection_reason) {
                $fisioterapeuta->rejection_reason = null;
            }
        }

        $fisioterapeuta->update($validated);

        // Se for uma requisição AJAX (como a de aprovação/rejeição)
        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Status do fisioterapeuta atualizado com sucesso!',
                'data' => $fisioterapeuta->fresh()
            ]);
        }

        return redirect()->route('admin.fisioterapeutas.index')
            ->with('success', 'Fisioterapeuta atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Fisioterapeuta $fisioterapeuta)
    {
        $fisioterapeuta->delete();

        return redirect()->route('admin.fisioterapeutas.index')
            ->with('success', 'Fisioterapeuta removido com sucesso!');
    }

    /**
     * Aprova um fisioterapeuta
     */
    public function approve(Request $request, Fisioterapeuta $fisioterapeuta)
    {
        try {
            DB::beginTransaction();
            
            // Verifica se o fisioterapeuta já está aprovado
            if ($fisioterapeuta->status === 'approved') {
                throw new \Exception('Este fisioterapeuta já está aprovado.');
            }
            
            // Verifica se o perfil do fisioterapeuta está completo
            $this->validarPerfilCompleto($fisioterapeuta);
            
            // Atualiza o status para aprovado
            $fisioterapeuta->update([
                'status' => 'approved',
                'rejection_reason' => null, // Limpa o motivo de rejeição se houver
            ]);

            // Ativa o usuário associado
            $fisioterapeuta->user->update(['active' => true]);
            
            // Envia email de notificação de aprovação
            \Illuminate\Support\Facades\Mail::to($fisioterapeuta->user->email)
                ->send(new \App\Mail\FisioterapeutaAprovado($fisioterapeuta));

            DB::commit();

            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Fisioterapeuta aprovado com sucesso!',
                    'data' => $fisioterapeuta->fresh()
                ]);
            }

            return redirect()->back()
                ->with('success', 'Fisioterapeuta aprovado com sucesso!');
                
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Captura exceções de validação para retornar os erros formatados
            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Erro de validação',
                    'errors' => $e->errors()
                ], 422);
            }
            throw $e;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erro ao aprovar fisioterapeuta: ' . $e->getMessage());
            
            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Erro ao aprovar fisioterapeuta: ' . $e->getMessage());
        }
    }
    
    /**
     * Valida se o perfil do fisioterapeuta está completo
     * 
     * @param Fisioterapeuta $fisioterapeuta
     * @throws \Exception
     */
    protected function validarPerfilCompleto(Fisioterapeuta $fisioterapeuta)
    {
        // Primeiro verifica se o email foi verificado
        if (!$fisioterapeuta->user->hasVerifiedEmail()) {
            throw new \Exception(
                'Não é possível aprovar o fisioterapeuta. O email precisa ser verificado antes da aprovação.'
            );
        }

        $camposObrigatorios = [
            'crefito' => 'CREFITO',
            'specializations' => 'especializações',
            'hourly_rate' => 'valor por hora',
            'available_areas' => 'áreas de atendimento',
            'working_hours' => 'horários de trabalho',
        ];
        
        $camposFaltantes = [];
        
        foreach ($camposObrigatorios as $campo => $nomeCampo) {
            if (empty($fisioterapeuta->$campo)) {
                $camposFaltantes[] = $nomeCampo;
            }
        }
        
        if (!empty($camposFaltantes)) {
            throw new \Exception(
                'Não é possível aprovar o fisioterapeuta. Os seguintes campos são obrigatórios: ' . 
                implode(', ', $camposFaltantes) . '.'
            );
        }
    }

    /**
     * Rejeita um fisioterapeuta
     */
    public function reject(Request $request, Fisioterapeuta $fisioterapeuta)
    {
        $validated = $request->validate([
            'rejection_reason' => 'required|string|min:10|max:1000',
        ], [
            'rejection_reason.required' => 'O motivo da rejeição é obrigatório.',
            'rejection_reason.min' => 'O motivo da rejeição deve ter pelo menos 10 caracteres.',
            'rejection_reason.max' => 'O motivo da rejeição não pode ter mais de 1000 caracteres.',
        ]);

        try {
            DB::beginTransaction();
            
            // Verifica se o fisioterapeuta já está rejeitado
            if ($fisioterapeuta->status === 'rejected') {
                throw new \Exception('Este fisioterapeuta já foi rejeitado anteriormente.');
            }
            
            // Atualiza o status para rejeitado e define o motivo
            $fisioterapeuta->update([
                'status' => 'rejected',
                'rejection_reason' => $validated['rejection_reason'],
            ]);

            // Desativa o usuário associado
            $fisioterapeuta->user->update(['active' => false]);

            // Envia email de notificação de rejeição
            \Illuminate\Support\Facades\Mail::to($fisioterapeuta->user->email)
                ->send(new \App\Mail\FisioterapeutaRejeitado($fisioterapeuta));

            DB::commit();

            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Fisioterapeuta rejeitado com sucesso!',
                    'data' => $fisioterapeuta->fresh()
                ]);
            }

            return redirect()->back()
                ->with('success', 'Fisioterapeuta rejeitado com sucesso!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            // Captura exceções de validação para retornar os erros formatados
            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Erro de validação',
                    'errors' => $e->errors()
                ], 422);
            }
            throw $e;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erro ao rejeitar fisioterapeuta: ' . $e->getMessage());
            
            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Erro ao rejeitar fisioterapeuta: ' . $e->getMessage());
        }
    }
}
