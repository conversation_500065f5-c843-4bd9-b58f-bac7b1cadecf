<?php

// Simple script to view Lara<PERSON> logs
$logFile = __DIR__ . '/storage/logs/laravel.log';

if (!file_exists($logFile)) {
    die("Log file not found at: " . $logFile . "\n");
}

echo "=== Last 50 lines of laravel.log ===\n\n";

// Read last 50 lines of the log file
$logContent = file($logFile);
$lastLines = array_slice($logContent, -50);

echo implode("", $lastLines);

echo "\n=== End of log ===\n";
