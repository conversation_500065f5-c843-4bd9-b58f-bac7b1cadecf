import { AppContent } from '@/components/app-content';
import { AppShell } from '@/components/app-shell';
import { AppSidebar } from '@/components/app-sidebar';
import { AppSidebarHeader } from '@/components/app-sidebar-header';
import { type BreadcrumbItem } from '@/types';
import { usePage } from '@inertiajs/react';
import { type PropsWithChildren, useEffect } from 'react';
import { Toaster } from '@/components/ui/sonner';
import { toast } from 'sonner';

export default function AppSidebarLayout({ children, breadcrumbs = [] }: PropsWithChildren<{ breadcrumbs?: BreadcrumbItem[] }>) {
    const { flash } = usePage().props as any;

    // Show flash messages via Sonner toasts
    useEffect(() => {
        if (!flash) return;
        if (flash.success) toast.success(String(flash.success));
        if (flash.error) toast.error(String(flash.error));
        if (flash.warning) toast.warning ? toast.warning(String(flash.warning)) : toast(String(flash.warning));
        if (flash.info) toast.info ? toast.info(String(flash.info)) : toast(String(flash.info));
    }, [flash?.success, flash?.error, flash?.warning, flash?.info]);
    return (
        <AppShell variant="sidebar">
            <AppSidebar />
            <AppContent variant="sidebar" className="overflow-x-hidden">
                <AppSidebarHeader breadcrumbs={breadcrumbs} />
                {/* Sonner toaster for transient notifications */}
                <Toaster position="top-center" richColors />
                {children}
            </AppContent>
        </AppShell>
    );
}
