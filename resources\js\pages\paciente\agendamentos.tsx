import PatientCard from '@/components/patient-card';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button, buttonVariants } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/toast-notification';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { Calendar, CheckCircle, Clock, Eye, Search, User, X } from 'lucide-react';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Início',
        href: '/paciente/dashboard',
    },
    {
        title: 'Agendamentos',
        href: '/paciente/agendamentos',
    },
];

interface Agendamento {
    id: number;
    data_hora: string;
    duracao: number;
    status: string;
    observacoes?: string;
    relatorio_sessao?: any;
    avaliacao?: any;
    fisioterapeuta: {
        id: number;
        user: {
            name: string;
            email: string;
        };
    };
}

interface Props {
    agendamentos: {
        data: Agendamento[];
        links: any[];
        meta: any;
    };
    filters: {
        status?: string;
        periodo?: string;
        search?: string;
    };
    stats: {
        total: number;
        pendentes: number;
        agendados: number;
        confirmados: number;
        a_caminho: number;
        em_andamento: number;
        concluidos: number;
        cancelados: number;
    };
}

export default function PacienteAgendamentos({ agendamentos, filters, stats }: Props) {
    const { data, setData, get } = useForm({
        status: filters.status || 'all',
        periodo: filters.periodo || 'all',
        search: filters.search || '',
    });
    const { showSuccess, showError } = useToast();

    // Infinite scroll state
    const [items, setItems] = useState<Agendamento[]>(agendamentos.data || []);
    const [page, setPage] = useState<number>(agendamentos?.meta?.current_page ?? 1);
    const [hasMore, setHasMore] = useState<boolean>((agendamentos?.meta?.current_page ?? 1) < (agendamentos?.meta?.last_page ?? 1));
    const [loadingMore, setLoadingMore] = useState<boolean>(false);
    const sentinelRef = useRef<HTMLDivElement | null>(null);
    const initialRenderRef = useRef<boolean>(true);
    const lastRequestedPageRef = useRef<number | null>(null);

    // When Inertia returns new page props, append if page advanced; or reset if back to page 1
    useEffect(() => {
        const current = agendamentos?.meta?.current_page ?? 1;
        const last = agendamentos?.meta?.last_page ?? 1;
        if (current === 1 && page !== 1) {
            // reset (e.g., on filter change)
            setItems(agendamentos.data || []);
            setPage(1);
            setHasMore(current < last);
            return;
        }
        if (current > page) {
            setItems((prev) => [...prev, ...(agendamentos.data || [])]);
            setPage(current);
            setHasMore(current < last);
        }
        // if initial mount ensure state mirrors props
        if (page === 1 && items.length === 0 && (agendamentos.data?.length || 0) > 0) {
            setItems(agendamentos.data);
            setHasMore(current < last);
        }
    }, [agendamentos?.meta?.current_page, agendamentos?.meta?.last_page, agendamentos.data]);

    const loadMore = useCallback(() => {
        if (loadingMore || !hasMore) return;
        const nextPage = page + 1;
        if (lastRequestedPageRef.current === nextPage) return;
        lastRequestedPageRef.current = nextPage;
        setLoadingMore(true);
        router.get(
            route('paciente.agendamentos.index', {
                page: nextPage,
                per_page: 10,
                status: data.status,
                periodo: data.periodo,
                search: data.search,
            } as any),
            {},
            {
                preserveState: true,
                replace: false,
                only: ['agendamentos'],
                onFinish: () => {
                    setLoadingMore(false);
                },
            },
        );
    }, [loadingMore, hasMore, page, data.status, data.periodo, data.search]);

    useEffect(() => {
        if (!sentinelRef.current) return;
        const el = sentinelRef.current;
        const io = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        loadMore();
                    }
                });
            },
            { rootMargin: '300px' },
        );
        io.observe(el);
        return () => io.disconnect();
    }, [loadMore]);

    const formatDate = (dateString: string | null | undefined) => {
        if (!dateString) return 'Data não informada';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'Data inválida';
            return date.toLocaleDateString('pt-BR', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
            });
        } catch {
            return 'Data inválida';
        }
    };

    const formatTime = (dateString: string | null | undefined) => {
        if (!dateString) return '--:--';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '--:--';
            return date.toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit',
            });
        } catch {
            return '--:--';
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Reset infinite scroll state
        setItems([]);
        setPage(1);
        setHasMore(false);
        lastRequestedPageRef.current = 1;

        // Submit form via Inertia
        get(route('paciente.agendamentos.index'), {
            preserveState: false,
            replace: true,
        });
    };

    // Inicialização única
    useEffect(() => {
        if (initialRenderRef.current) {
            initialRenderRef.current = false;
        }
    }, []);

    const handleCancel = (item: any) => {
        router.post(
            route('paciente.agendamentos.cancel', item.id),
            {},
            {
                preserveScroll: true,
                onSuccess: () => {
                    showSuccess('Agendamento cancelado com sucesso.');
                    router.reload({ only: ['agendamentos'] });
                },
                onError: (errors: Record<string, string>) => {
                    const msg = errors?.cancel || 'Não foi possível cancelar o agendamento.';
                    showError(msg);
                },
            },
        );
    };

    const renderStatusBadge = (status: string) => {
        const map: Record<
            string,
            { label: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' | 'ghost' | 'neutral' | 'gradient'; icon?: React.ReactNode }
        > = {
            pendente: { label: 'Agendei', variant: 'outline', icon: <Clock className="h-3.5 w-3.5" /> },
            agendado: { label: 'Paguei', variant: 'secondary', icon: <Clock className="h-3.5 w-3.5" /> },
            confirmado: { label: 'Profissional confirma', variant: 'default', icon: <CheckCircle className="h-3.5 w-3.5" /> },
            'a caminho': { label: 'Profissional a caminho', variant: 'outline', icon: <User className="h-3.5 w-3.5" /> },
            em_andamento: { label: 'Em serviço', variant: 'gradient', icon: <Clock className="h-3.5 w-3.5" /> },
            concluido: { label: 'Serviço concluído', variant: 'outline' },
            cancelado: { label: 'Cancelado', variant: 'destructive' },
        };
        const cfg = map[status] ?? { label: status, variant: 'outline' as const };
        return (
            <Badge variant={cfg.variant} size="sm" className="gap-1">
                {cfg.icon}
                {cfg.label}
            </Badge>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Meus Agendamentos" />
            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">Meus Agendamentos</h1>
                        <p className="text-gray-600">Gerencie suas consultas e sessões</p>
                    </div>
                    <div className="flex flex-col gap-2 sm:flex-row">
                        <Link href={route('paciente.fisioterapeutas.index')}>
                            <Button className="w-full sm:w-auto">
                                <User className="mr-2 h-4 w-4" />
                                <span className="hidden sm:inline">Buscar Fisioterapeutas</span>
                                <span className="sm:hidden">Buscar</span>
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Estatísticas */}
                <div className="grid grid-cols-2 gap-4 sm:gap-6 lg:grid-cols-4 lg:gap-8">
                    {/* Total */}
                    <PatientCard
                        icon={
                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                <Calendar className="h-5 w-5" />
                            </Badge>
                        }
                        title="Total"
                        subtitle="Agendamentos"
                        value={stats.total}
                        helperText="Total de agendamentos"
                    />

                    {/* Agendados */}
                    <PatientCard
                        icon={
                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                <Clock className="h-5 w-5" />
                            </Badge>
                        }
                        title="Agendados"
                        subtitle="Pendentes"
                        value={stats.agendados}
                        valueClassName="text-blue-600"
                        helperText="Aguardando atendimento"
                    />

                    {/* Concluídos */}
                    <PatientCard
                        icon={
                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                <CheckCircle className="h-5 w-5" />
                            </Badge>
                        }
                        title="Concluídos"
                        subtitle="Finalizados"
                        value={stats.concluidos}
                        valueClassName="text-green-600"
                        helperText="Sessões realizadas"
                    />

                    {/* Cancelados */}
                    <PatientCard
                        icon={
                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                <X className="h-5 w-5" />
                            </Badge>
                        }
                        title="Cancelados"
                        subtitle="Não realizados"
                        value={stats.cancelados}
                        valueClassName="text-red-600"
                        helperText="Agendamentos cancelados"
                    />
                </div>

                {/* Formulário de busca simples */}
                <div className="w-full">
                    <Card>
                        <CardContent className="p-6">
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
                                    <div className="flex-1">
                                        <label htmlFor="search" className="mb-2 block text-sm font-medium text-gray-700">
                                            Buscar Agendamentos
                                        </label>
                                        <Input
                                            id="search"
                                            type="text"
                                            placeholder="Ex.: nome do fisioterapeuta, observações..."
                                            value={data.search}
                                            onChange={(e) => setData('search', e.target.value)}
                                        />
                                    </div>

                                    <div className="w-full sm:w-48">
                                        <label htmlFor="status" className="mb-2 block text-sm font-medium text-gray-700">
                                            Status
                                        </label>
                                        <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Todos os status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">Todos os status</SelectItem>
                                                <SelectItem value="pendente">Agendei</SelectItem>
                                                <SelectItem value="agendado">Paguei</SelectItem>
                                                <SelectItem value="confirmado">Profissional confirma</SelectItem>
                                                <SelectItem value="a caminho">Profissional a caminho</SelectItem>
                                                <SelectItem value="em_andamento">Em serviço</SelectItem>
                                                <SelectItem value="concluido">Serviço concluído</SelectItem>
                                                <SelectItem value="cancelado">Cancelado</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="w-full sm:w-48">
                                        <label htmlFor="periodo" className="mb-2 block text-sm font-medium text-gray-700">
                                            Período
                                        </label>
                                        <Select value={data.periodo} onValueChange={(value) => setData('periodo', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Todos os períodos" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">Todos os períodos</SelectItem>
                                                <SelectItem value="proximos">Próximos</SelectItem>
                                                <SelectItem value="passados">Passados</SelectItem>
                                                <SelectItem value="mes_atual">Mês atual</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <Button type="submit" className="w-full sm:w-auto">
                                        <Search className="mr-2 h-4 w-4" />
                                        Pesquisar
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>

                {/* Lista de Agendamentos (infinite) */}
                <div className="space-y-4">
                    {items.map((agendamento) => (
                        <Card key={agendamento.id}>
                            <CardContent className="p-6">
                                <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                                            <User className="h-6 w-6 text-blue-600" />
                                        </div>
                                        <div>
                                            <h3 className="font-semibold">
                                                {agendamento.fisioterapeuta?.user?.name || 'Fisioterapeuta não definido'}
                                            </h3>
                                            <p className="text-sm text-muted-foreground">{formatDate(agendamento.data_hora)}</p>
                                            <p className="text-sm text-muted-foreground">
                                                {formatTime(agendamento.data_hora)} • {agendamento.duracao} minutos
                                            </p>
                                        </div>
                                        {/* Desktop-only: status to the right of the text block, ~6px gap */}
                                        <div className="ml-1.5 hidden sm:block">{renderStatusBadge(agendamento.status)}</div>
                                    </div>

                                    <div className="flex w-full items-center justify-between gap-3 sm:w-auto sm:flex-nowrap sm:justify-start">
                                        {/* Mobile: badge alinhado à esquerda */}
                                        <div className="sm:hidden">{renderStatusBadge(agendamento.status)}</div>

                                        {/* Botões alinhados à direita */}
                                        <div className="ml-auto flex items-center gap-2">
                                            <Link href={route('paciente.agendamentos.show', agendamento.id)}>
                                                <Button variant="outline" size="icon" aria-label="Ver detalhes">
                                                    <Eye />
                                                </Button>
                                            </Link>

                                            {/* Only View and Cancel remain */}

                                            {agendamento.status === 'agendado' && (
                                                <AlertDialog>
                                                    <AlertDialogTrigger asChild>
                                                        <Button variant="destructive" size="icon" aria-label="Cancelar agendamento">
                                                            <X />
                                                        </Button>
                                                    </AlertDialogTrigger>
                                                    <AlertDialogContent>
                                                        <AlertDialogHeader>
                                                            <AlertDialogTitle>Cancelar agendamento</AlertDialogTitle>
                                                            <AlertDialogDescription>
                                                                Tem certeza que deseja cancelar este agendamento? Essa ação não pode ser desfeita.
                                                            </AlertDialogDescription>
                                                        </AlertDialogHeader>
                                                        <AlertDialogFooter>
                                                            <AlertDialogCancel>Voltar</AlertDialogCancel>
                                                            <AlertDialogAction
                                                                className={buttonVariants({ variant: 'destructive' })}
                                                                onClick={() => handleCancel(agendamento)}
                                                            >
                                                                Sim, cancelar
                                                            </AlertDialogAction>
                                                        </AlertDialogFooter>
                                                    </AlertDialogContent>
                                                </AlertDialog>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {agendamento.observacoes && (
                                    <div className="mt-4 border-t pt-4">
                                        <p className="text-sm text-muted-foreground">
                                            <strong>Observações:</strong> {agendamento.observacoes}
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    ))}
                    {/* sentinel for infinite scroll */}
                    <div ref={sentinelRef} />
                    {loadingMore && <p className="py-4 text-center text-sm text-muted-foreground">Carregando...</p>}
                    {!hasMore && items.length > 0 && <p className="py-4 text-center text-sm text-muted-foreground">Você chegou ao fim da lista.</p>}
                </div>

                {agendamentos.data.length === 0 && (
                    <div className="py-12 text-center">
                        <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                        <p className="mt-2 text-muted-foreground">Nenhum agendamento encontrado</p>
                        <Link href={route('paciente.fisioterapeutas.index')} className="mt-4 inline-block">
                            <Button>
                                <User className="mr-2 h-4 w-4" />
                                Buscar Fisioterapeutas
                            </Button>
                        </Link>
                    </div>
                )}

                {/* Paginação removida – usamos carregamento infinito */}
            </div>

            {/* Botões de atalho removidos conforme solicitado */}
        </AppLayout>
    );
}
