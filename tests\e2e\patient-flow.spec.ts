import { expect, test } from '@playwright/test';

const BASE_URL = 'http://127.0.0.1:8000';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'Password123';

test('patient flow: login -> onboarding -> plan -> goes to payments (not checkout)', async ({ page }) => {
    // Login
    await page.goto(`${BASE_URL}/login`);
    await page.getByLabel('Endereço de email').fill(TEST_EMAIL);
    await page.getByLabel('Senha').fill(TEST_PASSWORD);
    await page.getByRole('button', { name: 'Entrar' }).click();

    // Ir ao dashboard; deve redirecionar para onboarding se ainda não completo
    await page.goto(`${BASE_URL}/dashboard`);

    // Se estamos no onboarding, completar via POST (para evitar fragilidade do UI DatePicker)
    if (page.url().includes('/paciente/onboarding')) {
        await page.waitForLoadState('domcontentloaded');

        // Extrair CSRF token e enviar POST com dados válidos
        const csrf = await page.locator('meta[name="csrf-token"]').getAttribute('content');
        expect(csrf).toBeTruthy();

        await page.evaluate(
            async ({ token }) => {
                const form = new FormData();
                form.append('name', 'Paciente Teste');
                form.append('phone', '(11) 99999-9999');
                form.append('birth_date', '1990-01-01');
                form.append('gender', 'masculino');
                form.append('address', 'Rua Teste, 123');
                form.append('city', 'São Paulo');
                form.append('state', 'SP');
                form.append('zip_code', '01234-567');
                form.append('medical_history', 'Histórico médico de teste com pelo menos 20 caracteres.');
                form.append('emergency_contact', 'Contato de emergência com 20+ caracteres.');
                form.append('main_objective', 'alivio_dor');
                form.append('pain_level', '5');
                form.append('specific_areas[]', 'lombar');
                form.append('treatment_goals', 'Reduzir dor e melhorar postura');
                form.append('preferred_time', 'manha');
                form.append('preferred_days[]', 'segunda');
                form.append('communication_preference', 'whatsapp');
                form.append('reminder_frequency', 'daily');

                await fetch('/paciente/onboarding', {
                    method: 'POST',
                    headers: { 'X-CSRF-TOKEN': token as string },
                    body: form,
                    redirect: 'manual',
                });
            },
            { token: csrf },
        );

        await page.goto(`${BASE_URL}/dashboard`);
    }

    // Deve cair na página de planos
    await expect(page).toHaveURL(/.*\/paciente\/planos.*/);

    // Selecionar Plano Pessoal (Contratar Plano)
    const contratarBtn = page.getByRole('button', { name: /Contratar Plano|Trocar para este Plano/ });
    await expect(contratarBtn).toBeVisible();
    await contratarBtn.click();

    // Deve redirecionar para a página de pagamentos do paciente (não checkout)
    await page.waitForURL(/.*\/paciente\/pagamentos\/.*/);
    await expect(page).toHaveURL(/.*\/paciente\/pagamentos\/.*/);
    await expect(page.getByText('Pagamento #')).toBeVisible();

    // Garantir que não estamos na rota de checkout do paciente
    expect(page.url()).not.toContain('/paciente/checkout');
});


