<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Notifications\VerifyEmailFisioterapeuta;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Notification;

class EnviarEmailVerificacao extends Command
{
    protected $signature = 'email:send-verification {email}';
    protected $description = 'Enviar email de verificação para um usuário específico';

    public function handle()
    {
        $email = $this->argument('email');

        // Buscar ou criar usuário fisioterapeuta
        $user = User::firstOrCreate(
            ['email' => $email],
            [
                'name' => 'Usuário Teste',
                'password' => bcrypt('SenhaTemporaria123!'),
                'role' => 'fisioterapeuta',
            ]
        );

        // Verificar se já está verificado
        if ($user->hasVerifiedEmail()) {
            $this->error("Usuário já possui email verificado: {$email}");
            return 1;
        }

        try {
            // Configurar temporariamente as opções de SSL para este comando
            $originalConfig = config('resend.http_options');
            
            config([
                'resend.http_options' => [
                    'verify' => false,
                    'curl' => [
                        CURLOPT_SSL_VERIFYPEER => 0,
                        CURLOPT_SSL_VERIFYHOST => 0,
                        CURLOPT_TIMEOUT => 30,
                    ],
                ]
            ]);

            // Enviar notificação
            Notification::sendNow($user, new VerifyEmailFisioterapeuta());

            // Restaurar configuração original
            config(['resend.http_options' => $originalConfig]);

            $this->info("✅ Email de verificação enviado com sucesso para: {$email}");
            $this->line("Nome: {$user->name}");
            $this->line("Role: {$user->role}");
            
            return 0;

        } catch (\Throwable $e) {
            $this->error("❌ Erro ao enviar email de verificação:");
            $this->error($e->getMessage());
            return 1;
        }
    }
}
