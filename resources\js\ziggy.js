const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"resend.webhook":{"uri":"resend\/webhook","methods":["POST"],"domain":"f4fisio.com.br"},"home":{"uri":"\/","methods":["GET","HEAD"]},"sobre":{"uri":"sobre","methods":["GET","HEAD"]},"servicos":{"uri":"servicos","methods":["GET","HEAD"]},"contato":{"uri":"contato","methods":["GET","HEAD"]},"contato.store":{"uri":"contato","methods":["POST"]},"contato.sucesso":{"uri":"contato\/sucesso","methods":["GET","HEAD"]},"planos-publicos":{"uri":"planos","methods":["GET","HEAD"]},"teste-mercadopago":{"uri":"teste-mercadopago","methods":["GET","HEAD"]},"teste-pagamento":{"uri":"teste-pagamento","methods":["GET","HEAD"]},"teste-pagamento.criar":{"uri":"teste-pagamento\/criar","methods":["POST"]},"teste-pagamento.verificar":{"uri":"teste-pagamento\/verificar","methods":["POST"]},"mercadopago.checkout":{"uri":"mercadopago\/checkout","methods":["GET","HEAD"]},"gerar-link-verificacao":{"uri":"gerar-link-verificacao","methods":["GET","HEAD"]},"teste-resend":{"uri":"teste-resend","methods":["GET","HEAD"]},"dev.enviar-email-verificacao":{"uri":"enviar-email-verificacao","methods":["GET","HEAD"]},"afiliados":{"uri":"afiliados","methods":["GET","HEAD"]},"debug.affiliate":{"uri":"debug\/affiliate","methods":["GET","HEAD"]},"debug.affiliate.clear":{"uri":"debug\/affiliate\/clear","methods":["POST"]},"debug.affiliate.simulate":{"uri":"debug\/affiliate\/simulate-sale","methods":["POST"]},"politica-privacidade":{"uri":"politica-privacidade","methods":["GET","HEAD"]},"termos-uso":{"uri":"termos-uso","methods":["GET","HEAD"]},"faq":{"uri":"faq","methods":["GET","HEAD"]},"materiais-divulgacao":{"uri":"materiais-divulgacao","methods":["GET","HEAD"]},"admin.afiliados.index":{"uri":"admin\/afiliados","methods":["GET","HEAD"]},"admin.afiliados.create":{"uri":"admin\/afiliados\/create","methods":["GET","HEAD"]},"admin.afiliados.store":{"uri":"admin\/afiliados","methods":["POST"]},"admin.afiliados.show":{"uri":"admin\/afiliados\/{afiliado}","methods":["GET","HEAD"],"parameters":["afiliado"],"bindings":{"afiliado":"id"}},"admin.afiliados.edit":{"uri":"admin\/afiliados\/{afiliado}\/edit","methods":["GET","HEAD"],"parameters":["afiliado"],"bindings":{"afiliado":"id"}},"admin.afiliados.update":{"uri":"admin\/afiliados\/{afiliado}","methods":["PUT","PATCH"],"parameters":["afiliado"],"bindings":{"afiliado":"id"}},"admin.afiliados.destroy":{"uri":"admin\/afiliados\/{afiliado}","methods":["DELETE"],"parameters":["afiliado"],"bindings":{"afiliado":"id"}},"admin.afiliados.aprovar":{"uri":"admin\/afiliados\/{afiliado}\/aprovar","methods":["POST"],"parameters":["afiliado"],"bindings":{"afiliado":"id"}},"admin.afiliados.rejeitar":{"uri":"admin\/afiliados\/{afiliado}\/rejeitar","methods":["POST"],"parameters":["afiliado"],"bindings":{"afiliado":"id"}},"admin.afiliados.suspender":{"uri":"admin\/afiliados\/{afiliado}\/suspender","methods":["POST"],"parameters":["afiliado"],"bindings":{"afiliado":"id"}},"admin.afiliados.reativar":{"uri":"admin\/afiliados\/{afiliado}\/reativar","methods":["POST"],"parameters":["afiliado"],"bindings":{"afiliado":"id"}},"admin.afiliados.gerar-novo-link":{"uri":"admin\/afiliados\/{afiliado}\/gerar-novo-link","methods":["POST"],"parameters":["afiliado"],"bindings":{"afiliado":"id"}},"admin.cupons.index":{"uri":"admin\/cupons","methods":["GET","HEAD"]},"admin.cupons.create":{"uri":"admin\/cupons\/create","methods":["GET","HEAD"]},"admin.cupons.store":{"uri":"admin\/cupons","methods":["POST"]},"admin.cupons.show":{"uri":"admin\/cupons\/{cupon}","methods":["GET","HEAD"],"parameters":["cupon"]},"admin.cupons.edit":{"uri":"admin\/cupons\/{cupon}\/edit","methods":["GET","HEAD"],"parameters":["cupon"]},"admin.cupons.update":{"uri":"admin\/cupons\/{cupon}","methods":["PUT","PATCH"],"parameters":["cupon"]},"admin.cupons.destroy":{"uri":"admin\/cupons\/{cupon}","methods":["DELETE"],"parameters":["cupon"]},"admin.cupons.toggle-status":{"uri":"admin\/cupons\/{cupom}\/toggle-status","methods":["POST"],"parameters":["cupom"],"bindings":{"cupom":"id"}},"admin.pagamentos-comissao.index":{"uri":"admin\/pagamentos-comissao","methods":["GET","HEAD"]},"admin.pagamentos-comissao.show":{"uri":"admin\/pagamentos-comissao\/{pagamentos_comissao}","methods":["GET","HEAD"],"parameters":["pagamentos_comissao"]},"admin.pagamentos-comissao.aprovar":{"uri":"admin\/pagamentos-comissao\/{pagamento}\/aprovar","methods":["POST"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos-comissao.rejeitar":{"uri":"admin\/pagamentos-comissao\/{pagamento}\/rejeitar","methods":["POST"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos-comissao.marcar-como-pago":{"uri":"admin\/pagamentos-comissao\/{pagamento}\/marcar-como-pago","methods":["POST"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos-comissao.bulk-action":{"uri":"admin\/pagamentos-comissao\/bulk-action","methods":["POST"]},"buscar":{"uri":"buscar","methods":["GET","HEAD"]},"estabelecimentos.buscar":{"uri":"api\/estabelecimentos\/buscar","methods":["POST"]},"estabelecimentos.categorias":{"uri":"api\/estabelecimentos\/categorias","methods":["GET","HEAD"]},"fisioterapeutas.public.buscar":{"uri":"api\/fisioterapeutas\/buscar","methods":["POST"]},"fisioterapeutas.public.regioes":{"uri":"api\/fisioterapeutas\/regioes","methods":["GET","HEAD"]},"mercadopago.webhook":{"uri":"webhook\/mercadopago","methods":["POST"]},"mercadopago.success":{"uri":"success","methods":["GET","HEAD"]},"mercadopago.failure":{"uri":"failure","methods":["GET","HEAD"]},"mercadopago.pending":{"uri":"pending","methods":["GET","HEAD"]},"checkout":{"uri":"checkout","methods":["GET","HEAD"]},"empresa.cadastro":{"uri":"empresa\/cadastro","methods":["GET","HEAD"]},"empresa.store":{"uri":"empresa\/cadastro","methods":["POST"]},"empresa.ativar-plano":{"uri":"empresa\/{estabelecimento}\/ativar-plano","methods":["POST"],"parameters":["estabelecimento"],"bindings":{"estabelecimento":"id"}},"estabelecimento.show":{"uri":"estabelecimento\/{slug}","methods":["GET","HEAD"],"parameters":["slug"]},"switch.mode":{"uri":"switch-mode","methods":["POST"]},"afiliados.store":{"uri":"afiliados\/cadastro","methods":["POST"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"notificacoes.index":{"uri":"notificacoes","methods":["GET","HEAD"]},"notificacoes.nao-lidas":{"uri":"notificacoes\/nao-lidas","methods":["GET","HEAD"]},"notificacoes.show":{"uri":"notificacoes\/{notificacao}","methods":["GET","HEAD"],"parameters":["notificacao"],"bindings":{"notificacao":"id"}},"notificacoes.marcar-lida":{"uri":"notificacoes\/{notificacao}\/marcar-lida","methods":["POST"],"parameters":["notificacao"],"bindings":{"notificacao":"id"}},"notificacoes.marcar-todas-lidas":{"uri":"notificacoes\/marcar-todas-lidas","methods":["POST"]},"notificacoes.destroy":{"uri":"notificacoes\/{notificacao}","methods":["DELETE"],"parameters":["notificacao"],"bindings":{"notificacao":"id"}},"mensagens.index":{"uri":"mensagens","methods":["GET","HEAD"]},"mensagens.buscar-usuarios":{"uri":"mensagens\/buscar-usuarios","methods":["GET","HEAD"]},"mensagens.contar-nao-lidas":{"uri":"mensagens\/contar-nao-lidas","methods":["GET","HEAD"]},"mensagens.store":{"uri":"mensagens","methods":["POST"]},"mensagens.show":{"uri":"mensagens\/{usuario}","methods":["GET","HEAD"],"parameters":["usuario"],"bindings":{"usuario":"id"}},"mensagens.marcar-lida":{"uri":"mensagens\/{mensagem}\/lida","methods":["PATCH"],"parameters":["mensagem"],"bindings":{"mensagem":"id"}},"avaliacoes.create":{"uri":"avaliacoes\/create","methods":["GET","HEAD"]},"avaliacoes.store":{"uri":"avaliacoes","methods":["POST"]},"avaliacoes.minhas":{"uri":"avaliacoes\/minhas","methods":["GET","HEAD"]},"avaliacoes.recebidas":{"uri":"avaliacoes\/recebidas","methods":["GET","HEAD"]},"avaliacoes.pode-avaliar":{"uri":"avaliacoes\/{agendamento}\/pode-avaliar","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"avaliacoes.show":{"uri":"avaliacoes\/{avaliacao}","methods":["GET","HEAD"],"parameters":["avaliacao"],"bindings":{"avaliacao":"id"}},"profile-status.status":{"uri":"profile-status\/status","methods":["GET","HEAD"]},"profile-status.analysis":{"uri":"profile-status\/analysis","methods":["GET","HEAD"]},"profile-status.check":{"uri":"profile-status\/check","methods":["GET","HEAD"]},"profile-status.suggestions":{"uri":"profile-status\/suggestions","methods":["GET","HEAD"]},"admin.dashboard":{"uri":"admin\/dashboard","methods":["GET","HEAD"]},"admin.usuarios.index":{"uri":"admin\/usuarios","methods":["GET","HEAD"]},"admin.usuarios.create":{"uri":"admin\/usuarios\/create","methods":["GET","HEAD"]},"admin.usuarios.store":{"uri":"admin\/usuarios","methods":["POST"]},"admin.usuarios.show":{"uri":"admin\/usuarios\/{usuario}","methods":["GET","HEAD"],"parameters":["usuario"],"bindings":{"usuario":"id"}},"admin.usuarios.edit":{"uri":"admin\/usuarios\/{usuario}\/edit","methods":["GET","HEAD"],"parameters":["usuario"],"bindings":{"usuario":"id"}},"admin.usuarios.update":{"uri":"admin\/usuarios\/{usuario}","methods":["PUT","PATCH"],"parameters":["usuario"],"bindings":{"usuario":"id"}},"admin.usuarios.destroy":{"uri":"admin\/usuarios\/{usuario}","methods":["DELETE"],"parameters":["usuario"],"bindings":{"usuario":"id"}},"admin.usuarios.history":{"uri":"admin\/usuarios\/{usuario}\/history","methods":["GET","HEAD"],"parameters":["usuario"],"bindings":{"usuario":"id"}},"admin.usuarios.export":{"uri":"admin\/usuarios-export","methods":["GET","HEAD"]},"admin.fisioterapeutas.index":{"uri":"admin\/fisioterapeutas","methods":["GET","HEAD"]},"admin.fisioterapeutas.create":{"uri":"admin\/fisioterapeutas\/create","methods":["GET","HEAD"]},"admin.fisioterapeutas.store":{"uri":"admin\/fisioterapeutas","methods":["POST"]},"admin.fisioterapeutas.show":{"uri":"admin\/fisioterapeutas\/{fisioterapeuta}","methods":["GET","HEAD"],"parameters":["fisioterapeuta"],"bindings":{"fisioterapeuta":"id"}},"admin.fisioterapeutas.edit":{"uri":"admin\/fisioterapeutas\/{fisioterapeuta}\/edit","methods":["GET","HEAD"],"parameters":["fisioterapeuta"],"bindings":{"fisioterapeuta":"id"}},"admin.fisioterapeutas.update":{"uri":"admin\/fisioterapeutas\/{fisioterapeuta}","methods":["PUT","PATCH"],"parameters":["fisioterapeuta"],"bindings":{"fisioterapeuta":"id"}},"admin.fisioterapeutas.destroy":{"uri":"admin\/fisioterapeutas\/{fisioterapeuta}","methods":["DELETE"],"parameters":["fisioterapeuta"],"bindings":{"fisioterapeuta":"id"}},"admin.fisioterapeutas.approve":{"uri":"admin\/fisioterapeutas\/{fisioterapeuta}\/approve","methods":["POST"],"parameters":["fisioterapeuta"],"bindings":{"fisioterapeuta":"id"}},"admin.fisioterapeutas.reject":{"uri":"admin\/fisioterapeutas\/{fisioterapeuta}\/reject","methods":["POST"],"parameters":["fisioterapeuta"],"bindings":{"fisioterapeuta":"id"}},"admin.planos.index":{"uri":"admin\/planos","methods":["GET","HEAD"]},"admin.planos.create":{"uri":"admin\/planos\/create","methods":["GET","HEAD"]},"admin.planos.store":{"uri":"admin\/planos","methods":["POST"]},"admin.planos.show":{"uri":"admin\/planos\/{plano}","methods":["GET","HEAD"],"parameters":["plano"],"bindings":{"plano":"id"}},"admin.planos.edit":{"uri":"admin\/planos\/{plano}\/edit","methods":["GET","HEAD"],"parameters":["plano"],"bindings":{"plano":"id"}},"admin.planos.update":{"uri":"admin\/planos\/{plano}","methods":["PUT","PATCH"],"parameters":["plano"],"bindings":{"plano":"id"}},"admin.planos.destroy":{"uri":"admin\/planos\/{plano}","methods":["DELETE"],"parameters":["plano"],"bindings":{"plano":"id"}},"admin.estabelecimentos.index":{"uri":"admin\/estabelecimentos","methods":["GET","HEAD"]},"admin.estabelecimentos.create":{"uri":"admin\/estabelecimentos\/create","methods":["GET","HEAD"]},"admin.estabelecimentos.store":{"uri":"admin\/estabelecimentos","methods":["POST"]},"admin.estabelecimentos.show":{"uri":"admin\/estabelecimentos\/{estabelecimento}","methods":["GET","HEAD"],"parameters":["estabelecimento"],"bindings":{"estabelecimento":"id"}},"admin.estabelecimentos.edit":{"uri":"admin\/estabelecimentos\/{estabelecimento}\/edit","methods":["GET","HEAD"],"parameters":["estabelecimento"],"bindings":{"estabelecimento":"id"}},"admin.estabelecimentos.update":{"uri":"admin\/estabelecimentos\/{estabelecimento}","methods":["PUT","PATCH"],"parameters":["estabelecimento"],"bindings":{"estabelecimento":"id"}},"admin.estabelecimentos.destroy":{"uri":"admin\/estabelecimentos\/{estabelecimento}","methods":["DELETE"],"parameters":["estabelecimento"],"bindings":{"estabelecimento":"id"}},"admin.estabelecimentos.toggle-ativo":{"uri":"admin\/estabelecimentos\/{estabelecimento}\/toggle-ativo","methods":["POST"],"parameters":["estabelecimento"],"bindings":{"estabelecimento":"id"}},"admin.estabelecimentos.toggle-plano":{"uri":"admin\/estabelecimentos\/{estabelecimento}\/toggle-plano","methods":["POST"],"parameters":["estabelecimento"],"bindings":{"estabelecimento":"id"}},"admin.pagamentos.index":{"uri":"admin\/pagamentos","methods":["GET","HEAD"]},"admin.pagamentos.show":{"uri":"admin\/pagamentos\/{pagamento}","methods":["GET","HEAD"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos.mark-as-paid":{"uri":"admin\/pagamentos\/{pagamento}\/mark-as-paid","methods":["POST"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos.mark-as-failed":{"uri":"admin\/pagamentos\/{pagamento}\/mark-as-failed","methods":["POST"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos.cancel":{"uri":"admin\/pagamentos\/{pagamento}\/cancel","methods":["POST"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos.update":{"uri":"admin\/pagamentos\/{pagamento}","methods":["PUT"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos.generate":{"uri":"admin\/assinaturas\/{assinatura}\/generate-payment","methods":["POST"],"parameters":["assinatura"],"bindings":{"assinatura":"id"}},"admin.pagamentos.export":{"uri":"admin\/pagamentos\/export","methods":["GET","HEAD"]},"admin.relatorios.index":{"uri":"admin\/relatorios","methods":["GET","HEAD"]},"admin.relatorios.financeiro":{"uri":"admin\/relatorios\/financeiro","methods":["GET","HEAD"]},"admin.relatorios.operacional":{"uri":"admin\/relatorios\/operacional","methods":["GET","HEAD"]},"admin.relatorios.pacientes":{"uri":"admin\/relatorios\/pacientes","methods":["GET","HEAD"]},"admin.relatorios.fisioterapeutas":{"uri":"admin\/relatorios\/fisioterapeutas","methods":["GET","HEAD"]},"admin.relatorios.export":{"uri":"admin\/relatorios\/export","methods":["GET","HEAD"]},"admin.backup.index":{"uri":"admin\/backup","methods":["GET","HEAD"]},"admin.backup.create":{"uri":"admin\/backup\/create","methods":["POST"]},"admin.backup.download":{"uri":"admin\/backup\/download\/{backupId}","methods":["GET","HEAD"],"parameters":["backupId"]},"admin.backup.delete":{"uri":"admin\/backup\/delete\/{backupId}","methods":["DELETE"],"parameters":["backupId"]},"admin.backup.cleanup":{"uri":"admin\/backup\/cleanup","methods":["POST"]},"admin.backup.auto-config":{"uri":"admin\/backup\/auto-config","methods":["POST"]},"admin.backup.get-auto-config":{"uri":"admin\/backup\/auto-config","methods":["GET","HEAD"]},"admin.export.report":{"uri":"admin\/export\/report","methods":["POST"]},"admin.export.download":{"uri":"admin\/export\/download\/{exportId}","methods":["GET","HEAD"],"parameters":["exportId"]},"admin.export.delete":{"uri":"admin\/export\/delete\/{exportId}","methods":["DELETE"],"parameters":["exportId"]},"admin.import.upload":{"uri":"admin\/import\/upload","methods":["POST"]},"admin.import.process":{"uri":"admin\/import\/process","methods":["POST"]},"admin.import.template":{"uri":"admin\/import\/template\/{type}","methods":["GET","HEAD"],"parameters":["type"]},"fisioterapeuta.verificar-email":{"uri":"fisioterapeuta\/verificar-email","methods":["GET","HEAD"]},"fisioterapeuta.verificar-email.reenviar":{"uri":"fisioterapeuta\/verificar-email\/reenviar","methods":["POST"]},"fisioterapeuta.setup":{"uri":"fisioterapeuta\/setup","methods":["GET","HEAD"]},"fisioterapeuta.setup.store":{"uri":"fisioterapeuta\/setup","methods":["POST"]},"fisioterapeuta.analise":{"uri":"fisioterapeuta\/analise","methods":["GET","HEAD"]},"fisioterapeuta.conta-rejeitada":{"uri":"fisioterapeuta\/conta-rejeitada","methods":["GET","HEAD"]},"fisioterapeuta.perfil":{"uri":"fisioterapeuta\/perfil","methods":["GET","HEAD"]},"fisioterapeuta.perfil.update":{"uri":"fisioterapeuta\/perfil","methods":["PUT"]},"fisioterapeuta.perfil.avatar.upload":{"uri":"fisioterapeuta\/perfil\/avatar","methods":["POST"]},"fisioterapeuta.perfil.avatar.remove":{"uri":"fisioterapeuta\/perfil\/avatar","methods":["DELETE"]},"fisioterapeuta.dashboard":{"uri":"fisioterapeuta\/dashboard","methods":["GET","HEAD"]},"fisioterapeuta.agenda.index":{"uri":"fisioterapeuta\/agenda","methods":["GET","HEAD"]},"fisioterapeuta.agenda.show":{"uri":"fisioterapeuta\/agenda\/{agendamento}","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.agenda.confirmar":{"uri":"fisioterapeuta\/agenda\/{agendamento}\/confirmar","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.agenda.aceitar":{"uri":"fisioterapeuta\/agenda\/{agendamento}\/aceitar","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.agenda.recusar":{"uri":"fisioterapeuta\/agenda\/{agendamento}\/recusar","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.agenda.reagendar":{"uri":"fisioterapeuta\/agenda\/{agendamento}\/reagendar","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.agenda.iniciar":{"uri":"fisioterapeuta\/agenda\/{agendamento}\/iniciar","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.agenda.finalizar":{"uri":"fisioterapeuta\/agenda\/{agendamento}\/finalizar","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.agenda.cancelar":{"uri":"fisioterapeuta\/agenda\/{agendamento}\/cancelar","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.pacientes.index":{"uri":"fisioterapeuta\/pacientes","methods":["GET","HEAD"]},"fisioterapeuta.pacientes.show":{"uri":"fisioterapeuta\/pacientes\/{paciente}","methods":["GET","HEAD"],"parameters":["paciente"],"bindings":{"paciente":"id"}},"fisioterapeuta.pacientes.notas":{"uri":"fisioterapeuta\/pacientes\/{paciente}\/notas","methods":["POST"],"parameters":["paciente"],"bindings":{"paciente":"id"}},"fisioterapeuta.disponibilidade.index":{"uri":"fisioterapeuta\/disponibilidade","methods":["GET","HEAD"]},"fisioterapeuta.disponibilidade.create":{"uri":"fisioterapeuta\/disponibilidade\/create","methods":["GET","HEAD"]},"fisioterapeuta.disponibilidade.store":{"uri":"fisioterapeuta\/disponibilidade","methods":["POST"]},"fisioterapeuta.disponibilidade.show":{"uri":"fisioterapeuta\/disponibilidade\/{disponibilidade}","methods":["GET","HEAD"],"parameters":["disponibilidade"],"bindings":{"disponibilidade":"id"}},"fisioterapeuta.disponibilidade.edit":{"uri":"fisioterapeuta\/disponibilidade\/{disponibilidade}\/edit","methods":["GET","HEAD"],"parameters":["disponibilidade"],"bindings":{"disponibilidade":"id"}},"fisioterapeuta.disponibilidade.update":{"uri":"fisioterapeuta\/disponibilidade\/{disponibilidade}","methods":["PUT","PATCH"],"parameters":["disponibilidade"],"bindings":{"disponibilidade":"id"}},"fisioterapeuta.disponibilidade.destroy":{"uri":"fisioterapeuta\/disponibilidade\/{disponibilidade}","methods":["DELETE"],"parameters":["disponibilidade"],"bindings":{"disponibilidade":"id"}},"fisioterapeuta.disponibilidade.toggle":{"uri":"fisioterapeuta\/disponibilidade\/{disponibilidade}\/toggle","methods":["POST"],"parameters":["disponibilidade"],"bindings":{"disponibilidade":"id"}},"fisioterapeuta.horarios.index":{"uri":"fisioterapeuta\/horarios","methods":["GET","HEAD"]},"fisioterapeuta.horarios.base.store":{"uri":"fisioterapeuta\/horarios\/base","methods":["POST"]},"fisioterapeuta.horarios.base.update":{"uri":"fisioterapeuta\/horarios\/base\/{horarioBase}","methods":["PUT"],"parameters":["horarioBase"],"bindings":{"horarioBase":"id"}},"fisioterapeuta.horarios.base.destroy":{"uri":"fisioterapeuta\/horarios\/base\/{horarioBase}","methods":["DELETE"],"parameters":["horarioBase"],"bindings":{"horarioBase":"id"}},"fisioterapeuta.horarios.excecoes.store":{"uri":"fisioterapeuta\/horarios\/excecoes","methods":["POST"]},"fisioterapeuta.horarios.excecoes.update":{"uri":"fisioterapeuta\/horarios\/excecoes\/{excecao}","methods":["PUT"],"parameters":["excecao"],"bindings":{"excecao":"id"}},"fisioterapeuta.horarios.excecoes.destroy":{"uri":"fisioterapeuta\/horarios\/excecoes\/{excecao}","methods":["DELETE"],"parameters":["excecao"],"bindings":{"excecao":"id"}},"fisioterapeuta.horarios.feriados.update":{"uri":"fisioterapeuta\/horarios\/feriados","methods":["PUT"]},"fisioterapeuta.horarios.preview":{"uri":"fisioterapeuta\/horarios\/preview","methods":["POST"]},"fisioterapeuta.horarios.feriados.list":{"uri":"fisioterapeuta\/horarios\/feriados","methods":["GET","HEAD"]},"fisioterapeuta.relatorios.index":{"uri":"fisioterapeuta\/relatorios","methods":["GET","HEAD"]},"fisioterapeuta.relatorios.pendentes":{"uri":"fisioterapeuta\/relatorios\/pendentes","methods":["GET","HEAD"]},"fisioterapeuta.relatorios.create":{"uri":"fisioterapeuta\/relatorios\/create\/{agendamento}","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.relatorios.store":{"uri":"fisioterapeuta\/relatorios\/{agendamento}","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.relatorios.show":{"uri":"fisioterapeuta\/relatorios\/{relatorio}","methods":["GET","HEAD"],"parameters":["relatorio"],"bindings":{"relatorio":"id"}},"fisioterapeuta.relatorios.edit":{"uri":"fisioterapeuta\/relatorios\/{relatorio}\/edit","methods":["GET","HEAD"],"parameters":["relatorio"],"bindings":{"relatorio":"id"}},"fisioterapeuta.relatorios.update":{"uri":"fisioterapeuta\/relatorios\/{relatorio}","methods":["PUT"],"parameters":["relatorio"],"bindings":{"relatorio":"id"}},"fisioterapeuta.relatorios.anexos.upload":{"uri":"fisioterapeuta\/relatorios\/{relatorio}\/anexos","methods":["POST"],"parameters":["relatorio"],"bindings":{"relatorio":"id"}},"fisioterapeuta.relatorios.anexos.remove":{"uri":"fisioterapeuta\/relatorios\/anexos\/{anexo}","methods":["DELETE"],"parameters":["anexo"],"bindings":{"anexo":"id"}},"fisioterapeuta.avaliacoes.index":{"uri":"fisioterapeuta\/avaliacoes","methods":["GET","HEAD"]},"fisioterapeuta.afiliados":{"uri":"fisioterapeuta\/afiliados","methods":["GET","HEAD"]},"afiliado.dashboard":{"uri":"afiliado\/dashboard","methods":["GET","HEAD"]},"afiliado.vendas":{"uri":"afiliado\/vendas","methods":["GET","HEAD"]},"afiliado.materiais":{"uri":"afiliado\/materiais","methods":["GET","HEAD"]},"afiliado.perfil":{"uri":"afiliado\/perfil","methods":["GET","HEAD"]},"afiliado.perfil.update":{"uri":"afiliado\/perfil","methods":["PUT"]},"afiliado.cupons.index":{"uri":"afiliado\/cupons","methods":["GET","HEAD"]},"afiliado.cupons.show":{"uri":"afiliado\/cupons\/{cupom}","methods":["GET","HEAD"],"parameters":["cupom"],"bindings":{"cupom":"id"}},"afiliado.cupons.link":{"uri":"afiliado\/cupons\/{cupom}\/link","methods":["GET","HEAD"],"parameters":["cupom"],"bindings":{"cupom":"id"}},"afiliado.pagamentos.index":{"uri":"afiliado\/pagamentos","methods":["GET","HEAD"]},"afiliado.pagamentos.create":{"uri":"afiliado\/pagamentos\/create","methods":["GET","HEAD"]},"afiliado.pagamentos.store":{"uri":"afiliado\/pagamentos","methods":["POST"]},"afiliado.pagamentos.show":{"uri":"afiliado\/pagamentos\/{pagamento}","methods":["GET","HEAD"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"paciente.onboarding":{"uri":"paciente\/onboarding","methods":["GET","HEAD"]},"paciente.onboarding.store":{"uri":"paciente\/onboarding","methods":["POST"]},"paciente.onboarding.save-partial":{"uri":"paciente\/onboarding\/save-partial","methods":["POST"]},"paciente.checkout":{"uri":"paciente\/checkout","methods":["GET","HEAD"]},"paciente.checkout.process":{"uri":"paciente\/checkout","methods":["POST"]},"paciente.checkout.simulation":{"uri":"paciente\/checkout\/simulation","methods":["GET","HEAD"]},"paciente.checkout.success":{"uri":"paciente\/checkout\/success","methods":["GET","HEAD"]},"paciente.checkout.confirm":{"uri":"paciente\/checkout\/confirm","methods":["POST"]},"paciente.pagamentos.index":{"uri":"paciente\/pagamentos","methods":["GET","HEAD"]},"paciente.pagamentos.show":{"uri":"paciente\/pagamentos\/{pagamento}","methods":["GET","HEAD"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"paciente.pagamentos.process":{"uri":"paciente\/pagamentos\/{pagamento}\/process","methods":["POST"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"paciente.pagamentos.process-method":{"uri":"paciente\/pagamentos\/{pagamento}\/process-method","methods":["POST"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"paciente.pagamentos.boleto":{"uri":"paciente\/pagamentos\/{pagamento}\/boleto","methods":["GET","HEAD"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"paciente.pagamentos.pix":{"uri":"paciente\/pagamentos\/{pagamento}\/pix","methods":["GET","HEAD"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"paciente.pagamentos.comprovante":{"uri":"paciente\/pagamentos\/{pagamento}\/comprovante","methods":["GET","HEAD"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"paciente.pagamentos.success":{"uri":"paciente\/pagamentos\/success","methods":["GET","HEAD"]},"paciente.pagamentos.failure":{"uri":"paciente\/pagamentos\/failure","methods":["GET","HEAD"]},"paciente.pagamentos.pending":{"uri":"paciente\/pagamentos\/pending","methods":["GET","HEAD"]},"paciente.dashboard":{"uri":"paciente\/dashboard","methods":["GET","HEAD"]},"paciente.afiliados":{"uri":"paciente\/afiliados","methods":["GET","HEAD"]},"paciente.fisioterapeutas.index":{"uri":"paciente\/fisioterapeutas","methods":["GET","HEAD"]},"paciente.fisioterapeutas.show":{"uri":"paciente\/fisioterapeutas\/{fisioterapeuta}","methods":["GET","HEAD"],"parameters":["fisioterapeuta"],"bindings":{"fisioterapeuta":"id"}},"paciente.agendamentos.create":{"uri":"paciente\/agendamentos\/create","methods":["GET","HEAD"]},"paciente.agendamentos.index":{"uri":"paciente\/agendamentos","methods":["GET","HEAD"]},"paciente.agendamentos.store":{"uri":"paciente\/agendamentos","methods":["POST"]},"paciente.agendamentos.show":{"uri":"paciente\/agendamentos\/{agendamento}","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.destroy":{"uri":"paciente\/agendamentos\/{agendamento}","methods":["DELETE"],"parameters":["agendamento"]},"paciente.agendamentos.cancel":{"uri":"paciente\/agendamentos\/{agendamento}\/cancel","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.reschedule":{"uri":"paciente\/agendamentos\/{agendamento}\/reschedule","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.update-reschedule":{"uri":"paciente\/agendamentos\/{agendamento}\/reschedule","methods":["PUT"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.report":{"uri":"paciente\/agendamentos\/{agendamento}\/report","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.evaluate":{"uri":"paciente\/agendamentos\/{agendamento}\/evaluate","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.store-evaluation":{"uri":"paciente\/agendamentos\/{agendamento}\/evaluate","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.horarios-disponiveis":{"uri":"paciente\/agendamentos\/horarios-disponiveis","methods":["POST"]},"paciente.agendamentos.fisioterapeutas-disponiveis":{"uri":"paciente\/agendamentos\/fisioterapeutas-disponiveis","methods":["POST"]},"paciente.agendamentos.verificar-disponibilidade":{"uri":"paciente\/agendamentos\/verificar-disponibilidade","methods":["POST"]},"paciente.historico.index":{"uri":"paciente\/historico","methods":["GET","HEAD"]},"paciente.historico.export":{"uri":"paciente\/historico\/export","methods":["GET","HEAD"]},"paciente.historico.show":{"uri":"paciente\/historico\/{agendamento}","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.perfil":{"uri":"paciente\/perfil","methods":["GET","HEAD"]},"paciente.perfil.update":{"uri":"paciente\/perfil","methods":["PUT"]},"paciente.perfil.avatar.upload":{"uri":"paciente\/perfil\/avatar","methods":["POST"]},"paciente.perfil.avatar.remove":{"uri":"paciente\/perfil\/avatar","methods":["DELETE"]},"paciente.avaliacoes.index":{"uri":"paciente\/avaliacoes","methods":["GET","HEAD"]},"empresa.setup":{"uri":"empresa\/setup","methods":["GET","HEAD"]},"empresa.setup.store":{"uri":"empresa\/setup","methods":["POST"]},"empresa.dashboard":{"uri":"empresa\/dashboard","methods":["GET","HEAD"]},"empresa.perfil":{"uri":"empresa\/perfil","methods":["GET","HEAD"]},"empresa.perfil.update":{"uri":"empresa\/perfil","methods":["PUT"]},"empresa.publica.show":{"uri":"empresa\/{slug}","methods":["GET","HEAD"],"parameters":["slug"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
