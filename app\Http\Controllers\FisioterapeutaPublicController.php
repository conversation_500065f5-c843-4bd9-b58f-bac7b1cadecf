<?php

namespace App\Http\Controllers;

use App\Models\Fisioterapeuta;
use App\Models\User;
use Illuminate\Http\Request;

class FisioterapeutaPublicController extends Controller
{
    /**
     * Public JSON search for physiotherapists used by the public /buscar page.
     */
    public function buscar(Request $request)
    {
        $query = User::query()
            ->where('active', true)
            ->where('banned', false)
            ->where('suspended', false)
            ->whereHas('fisioterapeuta', function($q) {
                $q->where('status', 'approved')
                  ->where('available', true);
            })
            ->with(['fisioterapeuta']);

        // Search by name (optional)
        if ($request->filled('nome')) {
            $query->where('name', 'like', '%' . $request->string('nome') . '%');
        }

        // Filter by approximate location text using available_areas contains
        if ($request->filled('localizacao')) {
            $term = (string) $request->input('localizacao');
            $query->whereHas('fisioterapeuta', function ($q) use ($term) {
                $q->where(function ($qq) use ($term) {
                    // Tenta correspondência exata no array JSON
                    $qq->whereJsonContains('available_areas', $term)
                        // Fallback: LIKE no JSON serializado (funciona para MySQL/SQLite)
                        ->orWhere('available_areas', 'like', '%' . $term . '%');
                });
            });
        }

        // Rating minimum
        if ($request->filled('avaliacao_minima')) {
            $min = (float) $request->input('avaliacao_minima');
            $query->whereHas('fisioterapeuta', fn ($q) => $q->where('rating', '>=', $min));
        }

        // Ordering (default to rating for empty searches)
        $ordenacao = (string) $request->input('ordenacao', 'avaliacao');
        switch ($ordenacao) {
            case 'avaliacao':
                $query->orderByRaw('(COALESCE((select fisioterapeutas.rating from fisioterapeutas where fisioterapeutas.user_id = users.id), 0)) desc');
                break;
            case 'recente':
                $query->latest('users.created_at');
                break;
            default:
                $query->orderBy('users.name');
        }

        $perPage = (int) $request->input('per_page', 10);
        $paginator = $query->paginate($perPage);

        $items = $paginator->getCollection()->map(function ($user) {
            $fisio = $user->fisioterapeuta;
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'avatar' => $user->avatar,
                'crefito' => $fisio->crefito,
                'specializations' => $fisio->specializations ?? [],
                'available_areas' => $fisio->available_areas ?? [],
                'rating' => (float) ($fisio->rating ?? 0),
                'total_reviews' => (int) ($fisio->total_reviews ?? 0),
                'hourly_rate' => (float) ($fisio->hourly_rate ?? 0),
                'session_rate' => $fisio->session_rate !== null ? (float) $fisio->session_rate : null,
                'pricing_mode' => $fisio->pricing_mode,
            ];
        })->values();

        return response()->json([
            'success' => true,
            'fisioterapeutas' => $items,
            'pagination' => [
                'current_page' => $paginator->currentPage(),
                'per_page' => $paginator->perPage(),
                'total' => $paginator->total(),
                'total_pages' => $paginator->lastPage(),
                'has_next_page' => $paginator->hasMorePages(),
                'has_prev_page' => $paginator->currentPage() > 1,
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
            ],
        ]);
    }

    /**
     * Lista pública de regiões/cidades disponíveis para atendimento (para combobox de busca).
     */
    public function regioes()
    {
        // Coleta todas as áreas disponíveis a partir do perfil dos fisioterapeutas ativos, aprovados e disponíveis
        $rows = \App\Models\Fisioterapeuta::query()
            ->where('status', 'approved')
            ->where('available', true)
            ->whereHas('user', function($q) {
                $q->where('active', true)
                  ->where('banned', false)
                  ->where('suspended', false);
            })
            ->pluck('available_areas')
            ->all();

        $all = [];
        foreach ($rows as $item) {
            if (is_string($item)) {
                $decoded = json_decode($item, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $item = $decoded;
                }
            }
            if (is_array($item)) {
                foreach ($item as $a) {
                    if (is_string($a)) {
                        $all[] = trim($a);
                    }
                }
            } elseif (is_string($item)) {
                $all[] = trim($item);
            }
        }

        $unique = collect($all)
            ->filter(fn($v) => $v !== '')
            ->unique()
            ->sort()
            ->values()
            ->map(fn($v) => [
                'value' => $v,
                'label' => $v,
            ]);

        return response()->json([
            'success' => true,
            'options' => $unique,
        ]);
    }
}
