import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { safeRoute } from '@/utils/route-helper';
import { Head, Link, useForm } from '@inertiajs/react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { AlertTriangle, ArrowLeft, Calendar, CheckCircle, Clock, CreditCard, DollarSign, Edit, Trash2, User, XCircle } from 'lucide-react';
import React from 'react';

interface Pagamento {
    id: number;
    amount: number;
    status: 'pendente' | 'pago' | 'falhou' | 'cancelado';
    method: string;
    due_date: string;
    paid_at?: string;
    data_vencimento?: string;
    data_pagamento?: string;
    transaction_id?: string;
    notes?: string;
    gateway_response?: any;
    created_at: string;
    updated_at: string;
    formatted_amount: string;
    formatted_due_date: string;
    formatted_paid_at?: string;
    assinatura: {
        id: number;
        status: string;
        start_date: string;
        end_date?: string;
        monthly_price: number;
        user: {
            id: number;
            name: string;
            email: string;
            phone?: string;
        };
        plano: {
            id: number;
            name: string;
            description: string;
            price: number;
            sessions_per_month: number;
        };
    };
}

interface Props {
    pagamento: Pagamento;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Pagamentos',
        href: '/admin/pagamentos',
    },
    {
        title: 'Detalhes do Pagamento',
        href: '#',
    },
];

export default function PagamentoShow({ pagamento }: Props) {
    const [showMarkAsPaidDialog, setShowMarkAsPaidDialog] = React.useState(false);
    const [showMarkAsFailedDialog, setShowMarkAsFailedDialog] = React.useState(false);
    const [showCancelDialog, setShowCancelDialog] = React.useState(false);
    const [showEditDialog, setShowEditDialog] = React.useState(false);

    const {
        data: paidData,
        setData: setPaidData,
        post: postPaid,
        processing: processingPaid,
    } = useForm({
        transaction_id: '',
        observacoes: '',
    });

    const {
        data: failedData,
        setData: setFailedData,
        post: postFailed,
        processing: processingFailed,
    } = useForm({
        observacoes: '',
    });

    const {
        data: cancelData,
        setData: setCancelData,
        post: postCancel,
        processing: processingCancel,
    } = useForm({
        observacoes: '',
    });

    const {
        data: editData,
        setData: setEditData,
        put,
        processing: processingEdit,
    } = useForm({
        valor: pagamento.amount,
        data_vencimento: pagamento.data_vencimento ? pagamento.data_vencimento.split('T')[0] : '',
        forma_pagamento: pagamento.method || '',
        observacoes: pagamento.notes || '',
    });

    const handleMarkAsPaid = (e: React.FormEvent) => {
        e.preventDefault();
        try {
            const routeUrl = safeRoute('admin.pagamentos.mark-as-paid', pagamento.id);
            if (routeUrl !== '#') {
                postPaid(routeUrl, {
                    onSuccess: () => setShowMarkAsPaidDialog(false),
                    onError: (errors) => console.error('Erro ao marcar como pago:', errors),
                });
            }
        } catch (error) {
            console.error('Erro ao marcar pagamento como pago:', error);
        }
    };

    const handleMarkAsFailed = (e: React.FormEvent) => {
        e.preventDefault();
        try {
            const routeUrl = safeRoute('admin.pagamentos.mark-as-failed', pagamento.id);
            if (routeUrl !== '#') {
                postFailed(routeUrl, {
                    onSuccess: () => setShowMarkAsFailedDialog(false),
                    onError: (errors) => console.error('Erro ao marcar como falhado:', errors),
                });
            }
        } catch (error) {
            console.error('Erro ao marcar pagamento como falhado:', error);
        }
    };

    const handleCancel = (e: React.FormEvent) => {
        e.preventDefault();
        try {
            const routeUrl = safeRoute('admin.pagamentos.cancel', pagamento.id);
            if (routeUrl !== '#') {
                postCancel(routeUrl, {
                    onSuccess: () => setShowCancelDialog(false),
                    onError: (errors) => console.error('Erro ao cancelar:', errors),
                });
            }
        } catch (error) {
            console.error('Erro ao cancelar pagamento:', error);
        }
    };

    const handleEdit = (e: React.FormEvent) => {
        e.preventDefault();
        try {
            const routeUrl = safeRoute('admin.pagamentos.update', pagamento.id);
            if (routeUrl !== '#') {
                put(routeUrl, {
                    onSuccess: () => setShowEditDialog(false),
                    onError: (errors) => console.error('Erro ao editar:', errors),
                });
            }
        } catch (error) {
            console.error('Erro ao editar pagamento:', error);
        }
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            pendente: { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
            pago: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
            falhou: { variant: 'destructive' as const, icon: AlertTriangle, color: 'text-red-600' },
            cancelado: { variant: 'outline' as const, icon: XCircle, color: 'text-gray-600' },
        };

        const config = variants[status as keyof typeof variants] || variants.pendente;
        const Icon = config.icon;

        return (
            <Badge variant={config.variant} className="flex items-center gap-1">
                <Icon className="h-4 w-4" />
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const getFormaPagamentoLabel = (forma: string) => {
        const formas = {
            cartao_credito: 'Cartão de Crédito',
            cartao_debito: 'Cartão de Débito',
            pix: 'PIX',
            boleto: 'Boleto',
        };
        return formas[forma as keyof typeof formas] || forma;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Pagamento #${pagamento.id}`} />

            <div className="py-12">
                <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <Link href={safeRoute('admin.pagamentos.index')} preserveState>
                                    <Button variant="ghost" size="sm">
                                        <ArrowLeft className="mr-2 h-4 w-4" />
                                        Voltar
                                    </Button>
                                </Link>
                                <div>
                                    <h2 className="text-3xl font-bold tracking-tight">Pagamento #{pagamento.id}</h2>
                                    <p className="text-muted-foreground">Detalhes do pagamento de {pagamento.assinatura.user.name}</p>
                                </div>
                            </div>

                            <div className="flex items-center gap-2">
                                {pagamento.status === 'pendente' && (
                                    <>
                                        <Button onClick={() => setShowEditDialog(true)} variant="outline">
                                            <Edit className="mr-2 h-4 w-4" />
                                            Editar
                                        </Button>
                                        <Button onClick={() => setShowMarkAsPaidDialog(true)} variant="default">
                                            <CheckCircle className="mr-2 h-4 w-4" />
                                            Marcar como Pago
                                        </Button>
                                        <Button onClick={() => setShowMarkAsFailedDialog(true)} variant="destructive">
                                            <XCircle className="mr-2 h-4 w-4" />
                                            Marcar como Falhou
                                        </Button>
                                        <Button onClick={() => setShowCancelDialog(true)} variant="outline">
                                            <Trash2 className="mr-2 h-4 w-4" />
                                            Cancelar
                                        </Button>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="grid gap-6 md:grid-cols-2">
                        {/* Informações do Pagamento */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <DollarSign className="mr-2 h-5 w-5" />
                                    Informações do Pagamento
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Status:</span>
                                    {getStatusBadge(pagamento.status)}
                                </div>

                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Valor:</span>
                                    <span className="text-lg font-bold text-green-600">{pagamento.formatted_amount}</span>
                                </div>

                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Forma de Pagamento:</span>
                                    <Badge variant="outline" className="flex items-center gap-1">
                                        <CreditCard className="h-3 w-3" />
                                        {getFormaPagamentoLabel(pagamento.method)}
                                    </Badge>
                                </div>

                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Data de Vencimento:</span>
                                    <span className="flex items-center text-sm">
                                        <Calendar className="mr-1 h-4 w-4" />
                                        {pagamento.formatted_due_date}
                                    </span>
                                </div>

                                {pagamento.data_pagamento && (
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium text-gray-600">Data do Pagamento:</span>
                                        <span className="flex items-center text-sm text-green-600">
                                            <CheckCircle className="mr-1 h-4 w-4" />
                                            {pagamento.formatted_paid_at}
                                        </span>
                                    </div>
                                )}

                                {pagamento.transaction_id && (
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium text-gray-600">ID da Transação:</span>
                                        <span className="rounded bg-gray-100 px-2 py-1 font-mono text-sm">{pagamento.transaction_id}</span>
                                    </div>
                                )}

                                {pagamento.notes && (
                                    <div>
                                        <span className="text-sm font-medium text-gray-600">Observações:</span>
                                        <p className="mt-1 rounded bg-gray-50 p-2 text-sm text-gray-700">{pagamento.notes}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Informações do Cliente e Assinatura */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <User className="mr-2 h-5 w-5" />
                                    Cliente e Assinatura
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <span className="text-sm font-medium text-gray-600">Cliente:</span>
                                    <div className="mt-1">
                                        <p className="font-medium">{pagamento.assinatura.user.name}</p>
                                        <p className="text-sm text-gray-600">{pagamento.assinatura.user.email}</p>
                                        {pagamento.assinatura.user.phone && (
                                            <p className="text-sm text-gray-600">{pagamento.assinatura.user.phone}</p>
                                        )}
                                    </div>
                                </div>

                                <div>
                                    <span className="text-sm font-medium text-gray-600">Plano:</span>
                                    <div className="mt-1">
                                        <p className="font-medium">{pagamento.assinatura.plano.name}</p>
                                        <p className="text-sm text-gray-600">{pagamento.assinatura.plano.description}</p>
                                        <p className="text-sm text-gray-600">{pagamento.assinatura.plano.sessions_per_month} sessões/mês</p>
                                    </div>
                                </div>

                                <div>
                                    <span className="text-sm font-medium text-gray-600">Status da Assinatura:</span>
                                    <Badge className="ml-2" variant="outline">
                                        {pagamento.assinatura.status}
                                    </Badge>
                                </div>

                                <div>
                                    <span className="text-sm font-medium text-gray-600">Período da Assinatura:</span>
                                    <p className="mt-1 text-sm text-gray-700">
                                        {format(new Date(pagamento.assinatura.start_date), 'dd/MM/yyyy', { locale: ptBR })}
                                        {pagamento.assinatura.end_date && (
                                            <> até {format(new Date(pagamento.assinatura.end_date), 'dd/MM/yyyy', { locale: ptBR })}</>
                                        )}
                                    </p>
                                </div>

                                <div className="border-t pt-4">
                                    <div className="text-sm text-gray-600">
                                        <strong>ID da Assinatura:</strong> #{pagamento.assinatura.id}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Gateway Response */}
                    {pagamento.gateway_response && (
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle>Resposta do Gateway</CardTitle>
                                <CardDescription>Informações técnicas retornadas pelo gateway de pagamento</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <pre className="overflow-x-auto rounded-lg bg-gray-100 p-4 text-sm">
                                    {JSON.stringify(pagamento.gateway_response, null, 2)}
                                </pre>
                            </CardContent>
                        </Card>
                    )}

                    {/* Histórico */}
                    <Card className="mt-6">
                        <CardHeader>
                            <CardTitle>Histórico</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                    <span>Criado em:</span>
                                    <span>{format(new Date(pagamento.created_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Última atualização:</span>
                                    <span>{format(new Date(pagamento.updated_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Dialogs */}
                    {showMarkAsPaidDialog && (
                        <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
                            <div className="mx-4 w-full max-w-md rounded-lg bg-white p-6">
                                <h3 className="mb-4 text-lg font-semibold">Marcar como Pago</h3>
                                <form onSubmit={handleMarkAsPaid} className="space-y-4">
                                    <div>
                                        <Label htmlFor="transaction_id">ID da Transação (opcional)</Label>
                                        <Input
                                            id="transaction_id"
                                            value={paidData.transaction_id}
                                            onChange={(e) => setPaidData('transaction_id', e.target.value)}
                                            placeholder="Ex: TXN_123456789"
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="observacoes">Observações (opcional)</Label>
                                        <Textarea
                                            id="observacoes"
                                            value={paidData.observacoes}
                                            onChange={(e) => setPaidData('observacoes', e.target.value)}
                                            placeholder="Observações sobre o pagamento..."
                                            rows={3}
                                        />
                                    </div>
                                    <div className="flex justify-end space-x-4">
                                        <Button type="button" variant="outline" onClick={() => setShowMarkAsPaidDialog(false)}>
                                            Cancelar
                                        </Button>
                                        <Button type="submit" disabled={processingPaid}>
                                            {processingPaid ? 'Processando...' : 'Confirmar'}
                                        </Button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    )}

                    {/* Outros dialogs similares... */}
                </div>
            </div>
        </AppLayout>
    );
}
