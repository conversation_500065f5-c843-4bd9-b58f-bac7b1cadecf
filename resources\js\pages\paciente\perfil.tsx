import { AvatarUpload } from '@/components/avatar-upload';
import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { Heart, Save } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/paciente/dashboard',
    },
    {
        title: 'Perfil',
        href: '/paciente/perfil',
    },
];

interface User {
    id: number;
    name: string;
    email: string;
    phone?: string;
    avatar?: string;
    birth_date?: string;
    gender?: string;
    address?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    medical_history?: string;
    emergency_contact?: string;
}

interface Props {
    user: User;
    estados: Record<string, string>;
}

export default function PacientePerfil({ user, estados }: Props) {
    const { data, setData, put, processing, errors, isDirty } = useForm({
        // Dados pessoais
        name: user.name || '',
        phone: user.phone || '',
        birth_date: user.birth_date || '',
        gender: user.gender || '',

        // Endereço
        address: user.address || '',
        city: user.city || '',
        state: user.state || '',
        zip_code: user.zip_code || '',

        // Informações médicas
        medical_history: user.medical_history || '',
        emergency_contact: user.emergency_contact || '',

        // Configurações de privacidade
        privacy_profile_visible: (user as any).privacy_profile_visible ?? true,
        privacy_contact_visible: (user as any).privacy_contact_visible ?? true,
        privacy_medical_visible: (user as any).privacy_medical_visible ?? false,
        privacy_allow_marketing: (user as any).privacy_allow_marketing ?? false,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('paciente.perfil.update'));
    };

    const formatCEP = (value: string) => {
        const numbers = value.replace(/\D/g, '');
        if (numbers.length <= 5) {
            return numbers;
        }
        return `${numbers.slice(0, 5)}-${numbers.slice(5, 8)}`;
    };

    const formatPhone = (value: string) => {
        const numbers = value.replace(/\D/g, '');
        if (numbers.length <= 2) {
            return `(${numbers}`;
        } else if (numbers.length <= 7) {
            return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
        } else if (numbers.length <= 11) {
            return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7)}`;
        }
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Meu Perfil" />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold">Meu Perfil</h1>
                        <p className="text-muted-foreground">Gerencie suas informações pessoais e médicas</p>
                    </div>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Avatar */}
                    <div className="lg:col-span-1">
                        <AvatarUpload
                            user={user}
                            uploadUrl={route('paciente.perfil.avatar.upload')}
                            removeUrl={route('paciente.perfil.avatar.remove')}
                        />
                    </div>

                    {/* Formulário */}
                    <div className="lg:col-span-2">
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Informações Pessoais */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Informações Pessoais</CardTitle>
                                    <CardDescription>Seus dados básicos de identificação</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="name">Nome Completo</Label>
                                            <Input
                                                id="name"
                                                value={data.name}
                                                onChange={(e) => setData('name', e.target.value)}
                                                placeholder="Seu nome completo"
                                            />
                                            <InputError message={errors.name} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="phone">Telefone</Label>
                                            <Input
                                                id="phone"
                                                value={data.phone}
                                                onChange={(e) => setData('phone', formatPhone(e.target.value))}
                                                placeholder="(11) 99999-9999"
                                                maxLength={15}
                                            />
                                            <InputError message={errors.phone} />
                                        </div>
                                    </div>

                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="birth_date">Data de Nascimento</Label>
                                            <Input
                                                id="birth_date"
                                                type="date"
                                                value={data.birth_date}
                                                onChange={(e) => setData('birth_date', e.target.value)}
                                            />
                                            <InputError message={errors.birth_date} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="gender">Gênero</Label>
                                            <Select value={data.gender} onValueChange={(value) => setData('gender', value)}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione seu gênero" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="masculino">Masculino</SelectItem>
                                                    <SelectItem value="feminino">Feminino</SelectItem>
                                                    <SelectItem value="outro">Outro</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <InputError message={errors.gender} />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Endereço */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Endereço</CardTitle>
                                    <CardDescription>Informações de localização para atendimento domiciliar</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="address">Endereço Completo</Label>
                                        <Input
                                            id="address"
                                            value={data.address}
                                            onChange={(e) => setData('address', e.target.value)}
                                            placeholder="Rua, número, complemento"
                                        />
                                        <InputError message={errors.address} />
                                    </div>

                                    <div className="grid gap-4 sm:grid-cols-3">
                                        <div className="space-y-2">
                                            <Label htmlFor="city">Cidade</Label>
                                            <Input
                                                id="city"
                                                value={data.city}
                                                onChange={(e) => setData('city', e.target.value)}
                                                placeholder="São Paulo"
                                            />
                                            <InputError message={errors.city} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="state">Estado</Label>
                                            <Select value={data.state} onValueChange={(value) => setData('state', value)}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="UF" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(estados).map(([uf, nome]) => (
                                                        <SelectItem key={uf} value={uf}>
                                                            {uf} - {nome}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <InputError message={errors.state} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="zip_code">CEP</Label>
                                            <Input
                                                id="zip_code"
                                                value={data.zip_code}
                                                onChange={(e) => setData('zip_code', formatCEP(e.target.value))}
                                                placeholder="00000-000"
                                                maxLength={9}
                                            />
                                            <InputError message={errors.zip_code} />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Informações Médicas */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Heart className="h-5 w-5 text-red-500" />
                                        Informações Médicas
                                    </CardTitle>
                                    <CardDescription>Histórico médico e contato de emergência para melhor atendimento</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="medical_history">Histórico Médico</Label>
                                        <Textarea
                                            id="medical_history"
                                            value={data.medical_history}
                                            onChange={(e) => setData('medical_history', e.target.value)}
                                            placeholder="Descreva seu histórico médico, condições existentes, cirurgias anteriores, medicamentos em uso, alergias, etc."
                                            rows={4}
                                        />
                                        <InputError message={errors.medical_history} />
                                        <p className="text-sm text-muted-foreground">{data.medical_history.length}/2000 caracteres</p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="emergency_contact">Contato de Emergência</Label>
                                        <Textarea
                                            id="emergency_contact"
                                            value={data.emergency_contact}
                                            onChange={(e) => setData('emergency_contact', e.target.value)}
                                            placeholder="Nome, parentesco, telefone e endereço de uma pessoa para contato em caso de emergência"
                                            rows={3}
                                        />
                                        <InputError message={errors.emergency_contact} />
                                        <p className="text-sm text-muted-foreground">{data.emergency_contact.length}/500 caracteres</p>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Configurações de Privacidade */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Heart className="h-5 w-5 text-blue-600" />
                                        Configurações de Privacidade
                                    </CardTitle>
                                    <CardDescription>Controle quem pode ver suas informações e como você deseja ser contatado</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="privacy_profile_visible">Perfil Público</Label>
                                                <p className="text-sm text-muted-foreground">Permitir que outros usuários vejam seu perfil básico</p>
                                            </div>
                                            <input
                                                type="checkbox"
                                                id="privacy_profile_visible"
                                                checked={data.privacy_profile_visible}
                                                onChange={(e) => setData('privacy_profile_visible', e.target.checked)}
                                                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="privacy_contact_visible">Informações de Contato</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Permitir que fisioterapeutas vejam suas informações de contato
                                                </p>
                                            </div>
                                            <input
                                                type="checkbox"
                                                id="privacy_contact_visible"
                                                checked={data.privacy_contact_visible}
                                                onChange={(e) => setData('privacy_contact_visible', e.target.checked)}
                                                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="privacy_medical_visible">Histórico Médico</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Permitir que fisioterapeutas vejam seu histórico médico para melhor atendimento
                                                </p>
                                            </div>
                                            <input
                                                type="checkbox"
                                                id="privacy_medical_visible"
                                                checked={data.privacy_medical_visible}
                                                onChange={(e) => setData('privacy_medical_visible', e.target.checked)}
                                                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                                            />
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <div className="space-y-0.5">
                                                <Label htmlFor="privacy_allow_marketing">Comunicações de Marketing</Label>
                                                <p className="text-sm text-muted-foreground">
                                                    Receber emails sobre novos serviços, promoções e dicas de saúde
                                                </p>
                                            </div>
                                            <input
                                                type="checkbox"
                                                id="privacy_allow_marketing"
                                                checked={data.privacy_allow_marketing}
                                                onChange={(e) => setData('privacy_allow_marketing', e.target.checked)}
                                                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                                            />
                                        </div>
                                    </div>

                                    <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
                                        <div className="flex items-start gap-3">
                                            <Heart className="mt-0.5 h-5 w-5 text-amber-600" />
                                            <div>
                                                <h4 className="font-medium text-amber-900">Sobre suas Informações</h4>
                                                <p className="mt-1 text-sm text-amber-700">
                                                    Suas informações médicas são sempre mantidas em sigilo e só são compartilhadas com fisioterapeutas
                                                    quando você autoriza explicitamente.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Botão de Salvar */}
                            <div className="flex justify-end">
                                <Button type="submit" disabled={processing || !isDirty} className="gap-2">
                                    <Save className="h-4 w-4" />
                                    {processing ? 'Salvando...' : 'Salvar Alterações'}
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
