<?php
require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

try {
    // Check if the columns exist in the pagamentos table
    $columns = DB::select("SHOW COLUMNS FROM pagamentos");
    
    $requiredColumns = ['amount', 'method', 'notes', 'gateway_response', 'status', 'data_vencimento', 'transaction_id'];
    $existingColumns = [];
    
    foreach ($columns as $column) {
        $existingColumns[] = $column->Field;
    }
    
    echo "Existing columns in pagamentos table:\n";
    foreach ($existingColumns as $col) {
        echo "- $col\n";
    }
    
    echo "\nChecking required columns:\n";
    foreach ($requiredColumns as $required) {
        if (in_array($required, $existingColumns)) {
            echo "✓ $required - EXISTS\n";
        } else {
            echo "✗ $required - MISSING\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
