<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Exception;

class EmailVerificationNotificationController extends Controller
{
    /**
     * Send a new email verification notification.
     */
    public function store(Request $request): RedirectResponse
    {
        if ($request->user()->hasVerifiedEmail()) {
            return redirect()->intended(route('dashboard', absolute: false));
        }

        // Verificar se o usuário pode solicitar verificação
        if (!$request->user()->canRequestEmailVerification()) {
            $remainingTime = $request->user()->getEmailVerificationThrottleTime();
            if ($remainingTime > 0) {
                return back()->with('status', 'verification-link-throttled')
                            ->with('remaining_time', $remainingTime)
                            ->with('message', 'Aguarde ' . $remainingTime . ' segundos antes de solicitar outro email de verificação.');
            } else {
                return back()->with('status', 'verification-link-limit-reached')
                            ->with('message', 'Limite de emails de verificação atingido. Tente novamente em 1 hora.');
            }
        }

        $emailSent = $request->user()->sendEmailVerificationNotification();
        
        if ($emailSent) {
            Log::info('Email verification notification sent', [
                'user_id' => $request->user()->id,
                'email' => $request->user()->email
            ]);

            return back()->with('status', 'verification-link-sent')
                        ->with('message', 'Um novo link de verificação foi enviado para o seu email.');
        } else {
            // Se não foi enviado devido ao throttling ou limite
            $remainingTime = $request->user()->getEmailVerificationThrottleTime();
            if ($remainingTime > 0) {
                return back()->with('status', 'verification-link-throttled')
                            ->with('remaining_time', $remainingTime)
                            ->with('message', 'Aguarde ' . $remainingTime . ' segundos antes de solicitar outro email de verificação.');
            } else {
                return back()->with('status', 'verification-link-limit-reached')
                            ->with('message', 'Limite de emails de verificação atingido. Tente novamente em 1 hora.');
            }
        }
    }
}
