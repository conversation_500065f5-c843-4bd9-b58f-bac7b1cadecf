import { Link, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface PaginationProps {
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
    meta: {
        current_page: number;
        from: number;
        last_page: number;
        path: string;
        per_page: number;
        to: number;
        total: number;
    };
}

export default function Pagination({ links, meta }: PaginationProps) {
    if (links.length <= 3) return null;

    return (
        <div className="flex items-center justify-between px-2">
            <div className="text-sm text-muted-foreground">
                Mostrando <span className="font-medium">{meta.from}</span> a <span className="font-medium">{meta.to}</span> de{' '}
                <span className="font-medium">{meta.total}</span> resultados
            </div>
            <div className="flex items-center space-x-1">
                {links.map((link, index) => {
                    if (index === 0) {
                        return (
                            <Button
                                key={index}
                                variant="outline"
                                size="sm"
                                onClick={() => link.url && router.get(link.url)}
                                disabled={!link.url}
                                className="px-2"
                            >
                                <ChevronLeft className="h-4 w-4" />
                            </Button>
                        );
                    }

                    if (index === links.length - 1) {
                        return (
                            <Button
                                key={index}
                                variant="outline"
                                size="sm"
                                onClick={() => link.url && router.get(link.url)}
                                disabled={!link.url}
                                className="px-2"
                            >
                                <ChevronRight className="h-4 w-4" />
                            </Button>
                        );
                    }

                    return (
                        <Button
                            key={index}
                            variant={link.active ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => link.url && router.get(link.url)}
                            className={link.active ? 'bg-primary text-primary-foreground' : ''}
                        >
                            {link.label}
                        </Button>
                    );
                })}
            </div>
        </div>
    );
}
