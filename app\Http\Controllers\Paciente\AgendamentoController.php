<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Agendamento;
use App\Models\Avaliacao;
use App\Models\Fisioterapeuta;
use App\Models\HorarioBase;
use App\Models\HorarioExcecao;
use App\Models\Assinatura;
use App\Models\Pagamento;
use App\Services\UnifiedPaymentService;
use App\Models\User;
use App\Services\NotificacaoService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AgendamentoController extends Controller
{
    /**
     * Display a listing of patient's appointments.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        $query = Agendamento::where('paciente_id', $user->id)
            ->with(['fisioterapeuta.user', 'relatorioSessao', 'avaliacao', 'pagamento']);

        // Filtro por período
        if ($request->filled('periodo') && !in_array($request->periodo, ['all', '#'], true)) {
            switch ($request->periodo) {
                case 'proximos':
                    $query->where('scheduled_at', '>=', Carbon::now());
                    break;
                case 'passados':
                    $query->where('scheduled_at', '<', Carbon::now());
                    break;
                case 'mes_atual':
                    $query->whereMonth('scheduled_at', Carbon::now()->month)
                          ->whereYear('scheduled_at', Carbon::now()->year);
                    break;
            }
        }

        $agendamentos = $query->orderBy('scheduled_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        // Verificar e atualizar status de pagamentos pendentes
        foreach ($agendamentos as $agendamento) {
            if ($agendamento->hasPayment()) {
                // Se o pagamento está pendente, garantir que o agendamento também esteja pendente
                if ($agendamento->pagamento->status === 'pendente') {
                    $agendamento->status = 'pendente';
                } 
                // Se o pagamento foi confirmado, atualizar status para agendado
                elseif ($agendamento->pagamento->status === 'pago' && $agendamento->status === 'pendente') {
                    $agendamento->update(['status' => 'agendado']);
                }
            }
        }

        // Aplicar filtro de status após a verificação de pagamento
        if ($request->filled('status') && !in_array($request->status, ['all', '#'], true)) {
            $filteredAgendamentos = $agendamentos->getCollection()->filter(function ($agendamento) use ($request) {
                return $agendamento->status === $request->status;
            });
            
            // Recreate pagination with filtered results
            $agendamentos->setCollection($filteredAgendamentos);
        }

        // Transformar os dados para o formato esperado pelo frontend
        $transformedAgendamentos = $agendamentos->through(function ($agendamento) {
            return [
                'id' => $agendamento->id,
                'data_hora' => $agendamento->scheduled_at ? $agendamento->scheduled_at->toDateTimeString() : null,
                'duracao' => $agendamento->duration,
                'status' => $agendamento->status,
                'observacoes' => $agendamento->notes,
                'relatorio_sessao' => $agendamento->relatorioSessao,
                'avaliacao' => $agendamento->avaliacao,
                'fisioterapeuta' => $agendamento->fisioterapeuta ? [
                    'id' => $agendamento->fisioterapeuta->id,
                    'user' => [
                        'name' => $agendamento->fisioterapeuta->user->name,
                        'email' => $agendamento->fisioterapeuta->user->email,
                    ],
                ] : null,
            ];
        });

        // Estatísticas
        $stats = [
            'total' => Agendamento::where('paciente_id', $user->id)->count(),
            'pendentes' => Agendamento::where('paciente_id', $user->id)->where('status', 'pendente')->count(),
            'agendados' => Agendamento::where('paciente_id', $user->id)->where('status', 'agendado')->count(),
            'confirmados' => Agendamento::where('paciente_id', $user->id)->where('status', 'confirmado')->count(),
            'a_caminho' => Agendamento::where('paciente_id', $user->id)->where('status', 'a caminho')->count(),
            'em_andamento' => Agendamento::where('paciente_id', $user->id)->where('status', 'em_andamento')->count(),
            'concluidos' => Agendamento::where('paciente_id', $user->id)->where('status', 'concluido')->count(),
            'cancelados' => Agendamento::where('paciente_id', $user->id)->where('status', 'cancelado')->count(),
        ];

        return Inertia::render('paciente/agendamentos', [
            'agendamentos' => $transformedAgendamentos,
            'filters' => $request->only(['status', 'periodo']),
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for creating a new appointment.
     */
    public function create(Request $request)
    {
        $user = auth()->user();
        Log::debug('[AgendamentoController@create] Acessando formulário de criação', [
            'user_id' => $user?->id,
            'query' => $request->query(),
            'route' => $request->route()?->getName(),
            'url' => $request->fullUrl(),
        ]);

        // Fisioterapeuta pré-selecionado (vindo da busca)
        $fisioterapeutaSelecionado = null;
        if ($request->filled('fisioterapeuta')) {
            $fisioterapeutaSelecionado = User::with(['fisioterapeuta', 'horariosBase'])
                ->where('id', $request->fisioterapeuta)
                ->where('role', 'fisioterapeuta')
                ->where('active', true)
                ->where('banned', false)
                ->where('suspended', false)
                ->whereHas('fisioterapeuta', function($q) {
                    $q->where('status', 'approved')
                      ->where('available', true);
                })
                ->first();

            Log::debug('[AgendamentoController@create] Fisioterapeuta pré-selecionado', [
                'fisioterapeuta_id' => $request->fisioterapeuta,
                'encontrado' => $fisioterapeutaSelecionado ? true : false,
                'fisioterapeuta_data' => $fisioterapeutaSelecionado ? [
                    'name' => $fisioterapeutaSelecionado->name,
                    'crefito' => $fisioterapeutaSelecionado->fisioterapeuta?->crefito,
                    'specializations' => $fisioterapeutaSelecionado->fisioterapeuta?->specializations,
                ] : null,
            ]);
        }

        // Buscar todos os fisioterapeutas disponíveis
        $fisioterapeutas = User::with('fisioterapeuta')
            ->where('role', 'fisioterapeuta')
            ->get()
            ->map(function ($fisioterapeuta) {
                $f = $fisioterapeuta->fisioterapeuta;
                $hourlyRate = $f->hourly_rate ?? 0;
                
                return [
                    'id' => $fisioterapeuta->id,
                    'user' => [
                        'name' => $fisioterapeuta->name,
                        'avatar' => null,
                    ],
                    'crefito' => $f->crefito ?? null,
                    'specializations' => $f->specializations ?? $f->especialidades ?? [],
                    'hourly_rate' => (float) $hourlyRate, // Garantir que é tratado como número
                    'pricing_mode' => 'por_hora', // Forçar modo de cobrança por hora
                    'service_rates' => $f->service_rates ?? null,
                    'rating' => $f->rating ?? 0,
                    'total_reviews' => $f->total_reviews ?? $f->total_avaliacoes ?? 0,
                ];
            });

        // Gerar horários disponíveis - se há fisioterapeuta pré-selecionado, calcular dinamicamente
        $horariosDisponiveis = [];
        if ($request->filled('fisioterapeuta') && $request->filled('data')) {
            $horariosDisponiveis = $this->getHorariosDisponiveis($request->fisioterapeuta, $request->data);
        } elseif ($request->filled('fisioterapeuta')) {
            // Se só há fisioterapeuta, gerar horários padrão mas que serão validados depois
            $horariosDisponiveis = $this->getHorariosDisponiveis(null, null);
        } else {
            // Sem fisioterapeuta, horários padrão
            $horariosDisponiveis = $this->getHorariosDisponiveis();
        }

        Log::debug('[AgendamentoController@create] Renderizando página', [
            'fisioterapeutas_qtd' => $fisioterapeutas->count(),
            'pre_selecionado' => $fisioterapeutaSelecionado?->id,
        ]);

        return Inertia::render('paciente/agendamentos/create', [
            'user' => [
                'name' => $user->name,
                'address' => $user->address,
                'address_line2' => $user->address_line2,
                'city' => $user->city,
                'state' => $user->state,
                'zip_code' => $user->zip_code,
            ],
            'fisioterapeutas' => $fisioterapeutas,
            'fisioterapeutaSelecionado' => $fisioterapeutaSelecionado ? (function() use ($fisioterapeutaSelecionado) {
                $f = $fisioterapeutaSelecionado->fisioterapeuta;
                $hourlyRate = $f->hourly_rate ?? 0;

                return [
                    'id' => $fisioterapeutaSelecionado->id,
                    'user' => [
                        'name' => $fisioterapeutaSelecionado->name,
                        'email' => $fisioterapeutaSelecionado->email,
                        'phone' => $fisioterapeutaSelecionado->phone,
                        'avatar' => null,
                    ],
                    'crefito' => $f->crefito ?? 'CREFITO-N/I',
                    'specializations' => $f->specializations ?? [],
                    'especialidades' => $f->specializations ?? [], // Manter compatibilidade
                    'bio' => $f->bio ?? '',
                    'hourly_rate' => (float) $hourlyRate,
                    'session_rate' => $f->session_rate ?? null,
                    'travel_fee' => $f->travel_fee ?? null,
                    'pricing_mode' => $f->pricing_mode ?? 'por_hora',
                    'service_rates' => $f->service_rates ?? null,
                    'rating' => $f->rating ?? 0,
                    'total_reviews' => $f->total_reviews ?? 0,
                    'available_areas' => $f->available_areas ?? [],
                    'working_hours' => $f->working_hours ?? [],
                    'available' => $f->available ?? true,
                    'horarios_base' => $fisioterapeutaSelecionado->horariosBase ?? [],
                ];
            })() : null,
            'horariosDisponiveis' => $horariosDisponiveis,
        ]);
    }

    /**
     * Get available time slots using the main HorarioDisponibilidadeService
     */
    private function getHorariosDisponiveis($fisioterapeutaId = null, $data = null)
    {
        Log::debug('[PacienteAgendamentoController@getHorariosDisponiveis] Solicitando horários', [
            'fisioterapeuta_id' => $fisioterapeutaId,
            'data' => $data
        ]);

        // Se não fornecido fisioterapeuta ou data, retorna array vazio
        if (!$fisioterapeutaId || !$data) {
            return [];
        }

        try {
            // Usar o serviço oficial de disponibilidade
            $horarioService = new \App\Services\HorarioDisponibilidadeService();
            $horariosDisponiveis = $horarioService->calcularHorariosDisponiveis($fisioterapeutaId, $data);

            Log::debug('[PacienteAgendamentoController@getHorariosDisponiveis] Horários retornados pelo serviço', [
                'horarios_disponiveis' => $horariosDisponiveis,
                'quantidade' => count($horariosDisponiveis)
            ]);

            return $horariosDisponiveis;
        } catch (\Exception $e) {
            Log::error('[PacienteAgendamentoController@getHorariosDisponiveis] Erro ao obter horários', [
                'error' => $e->getMessage()
            ]);

            // Em caso de erro, retornar array vazio
            return [];
        }
    }

    /**
     * Store a newly created appointment.
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        Log::debug('[AgendamentoController@store] Iniciando criação de agendamento', [
            'user_id' => $user?->id,
            'payload' => $request->only(['fisioterapeuta_id', 'data_hora', 'observacoes']),
        ]);

        $validated = $request->validate([
            'fisioterapeuta_id' => 'required|exists:users,id',
            'data_hora' => 'required|date|after:now|before:' . Carbon::now()->addDays(60)->toDateString(),
            'appointment_type' => 'required|in:domicilio,clinica,outro',
            'appointment_type_notes' => 'nullable|string|max:1000',
            'observacoes' => 'nullable|string|max:500',
            'address' => 'required_if:appointment_type,domicilio|string|max:255',
            'address_line2' => 'nullable|string|max:255',
            'address_city' => 'required_if:appointment_type,domicilio|string|max:255',
            'address_state' => 'required_if:appointment_type,domicilio|string|size:2',
            'address_zip_code' => 'required_if:appointment_type,domicilio|string|max:10',
            'address_type' => 'nullable|in:residencia,trabalho,outro',
        ], [
            'fisioterapeuta_id.required' => 'Selecione um fisioterapeuta.',
            'fisioterapeuta_id.exists' => 'Fisioterapeuta não encontrado.',
            'data_hora.required' => 'Selecione uma data e horário.',
            'data_hora.date' => 'Data e horário inválidos.',
            'data_hora.after' => 'A data deve ser futura.',
            'data_hora.before' => 'Não é possível agendar com mais de 60 dias de antecedência.',
            'appointment_type.required' => 'Tipo de atendimento é obrigatório.',
            'appointment_type.in' => 'Seleção de tipo de atendimento inválida.',
            'appointment_type_notes.max' => 'Detalhes não podem ter mais de 1000 caracteres.',
            'observacoes.max' => 'As observações não podem ter mais de 500 caracteres.',
            'address.required_if' => 'Logradouro é obrigatório para atendimento domiciliar.',
            'address.max' => 'Logradouro não pode ter mais de 255 caracteres.',
            'address_line2.max' => 'Complemento não pode ter mais de 255 caracteres.',
            'address_city.required_if' => 'Cidade é obrigatória para atendimento domiciliar.',
            'address_city.max' => 'Cidade não pode ter mais de 255 caracteres.',
            'address_state.required_if' => 'Estado é obrigatório para atendimento domiciliar.',
            'address_state.size' => 'Estado deve ter exatamente 2 letras.',
            'address_zip_code.required_if' => 'CEP é obrigatório para atendimento domiciliar.',
            'address_zip_code.max' => 'CEP não pode ter mais de 10 caracteres.',
        ]);

        Log::debug('[AgendamentoController@store] Dados validados', [
            'validated' => $validated,
        ]);

        $dataHora = Carbon::parse($validated['data_hora']);

        // Validações adicionais de negócio

        // 1. Verificar se não é domingo
        if ($dataHora->dayOfWeek === 0) {
            return back()->withErrors(['data_hora' => 'Não é possível agendar aos domingos.']);
        }

        // 2. Verificar horário comercial (8h às 18h)
        $hora = $dataHora->hour;
        if ($hora < 8 || $hora >= 18) {
            return back()->withErrors(['data_hora' => 'Horário deve ser entre 8h e 18h.']);
        }

        // 3. Verificar se o fisioterapeuta existe e está ativo
        $fisioterapeuta = User::with('fisioterapeuta')
            ->where('id', $validated['fisioterapeuta_id'])
            ->whereHas('fisioterapeuta', function($q) {
                $q->where('available', true);
            })
            ->first();

        if (!$fisioterapeuta) {
            return back()->withErrors(['fisioterapeuta_id' => 'Fisioterapeuta não disponível.']);
        }

        // 4. Verificar se o horário ainda está disponível
        $conflito = Agendamento::where('fisioterapeuta_id', $validated['fisioterapeuta_id'])
            ->where('scheduled_at', $validated['data_hora'])
            ->where('status', '!=', 'cancelado')
            ->exists();

        if ($conflito) {
            return back()->withErrors(['data_hora' => 'Este horário não está mais disponível.']);
        }

        // 5. Verificar se o paciente não tem outro agendamento no mesmo horário
        $conflitoPaciente = Agendamento::where('paciente_id', $user->id)
            ->where('scheduled_at', $validated['data_hora'])
            ->where('status', '!=', 'cancelado')
            ->exists();

        if ($conflitoPaciente) {
            return back()->withErrors(['data_hora' => 'Você já possui um agendamento neste horário.']);
        }

        // 6. Verificar limite de agendamentos por dia (máximo 3)
        $agendamentosDia = Agendamento::where('paciente_id', $user->id)
            ->whereDate('scheduled_at', $dataHora->toDateString())
            ->where('status', '!=', 'cancelado')
            ->count();

        if ($agendamentosDia >= 3) {
            return back()->withErrors(['data_hora' => 'Limite de 3 agendamentos por dia atingido.']);
        }

        try {
            DB::beginTransaction();

            // Log dos dados recebidos
            Log::info('[AgendamentoController@store] Dados validados recebidos', [
                'validated' => $validated,
                'user_id' => $user->id,
                'user_email' => $user->email,
            ]);

            // Verificar se o fisioterapeuta está ativo, aprovado e disponível
            $fisioterapeuta = User::where('id', $validated['fisioterapeuta_id'])
                ->where('active', true)
                ->where('banned', false)
                ->where('suspended', false)
                ->whereHas('fisioterapeuta', function($q) {
                    $q->where('status', 'approved')
                      ->where('available', true);
                })
                ->with('fisioterapeuta')
                ->first();

            if (!$fisioterapeuta) {
                return back()->withErrors(['fisioterapeuta_id' => 'Fisioterapeuta não disponível para agendamento no momento.']);
            }

            // Obter a duração e preço da sessão
            $duration = 60; // 1 hora de duração
            $price = $fisioterapeuta->fisioterapeuta->hourly_rate ?? 90.00; // Usar o preço por hora do fisioterapeuta ou 90 como fallback

            // Preparar dados de endereço - usar dados do formulário ou do usuário como fallback
            $enderecoDados = [
                'address' => $validated['address'] ?? $user->address ?? 'Endereço não informado',
                'address_line2' => $validated['address_line2'] ?? $user->address_line2 ?? null,
                'address_city' => $validated['address_city'] ?? $user->city ?? null,
                'address_state' => $validated['address_state'] ?? $user->state ?? null,
                'address_zip_code' => $validated['address_zip_code'] ?? $user->zip_code ?? null,
                'address_type' => $validated['address_type'] ?? 'residencia',
            ];

            // Definir tipo de atendimento como domiciliar por padrão
            $appointmentType = 'domicilio';
            $appointmentTypeNotes = null;

            // Nota: futura expansão permitirá alteração do tipo e endereço

            // 1) Criar agendamento com status pendente
            $agendamento = Agendamento::create([
                'paciente_id' => $user->id,
                'fisioterapeuta_id' => $validated['fisioterapeuta_id'],
                'scheduled_at' => $validated['data_hora'],
                'duration' => $duration,
                'status' => 'pendente', // Status inicial pendente até pagamento ser confirmado
                'service_type' => request('tipo', 'sessao'), // Usar valor do request
                'appointment_type' => $appointmentType,
                'appointment_type_notes' => $appointmentTypeNotes,
                'notes' => $validated['observacoes'],
                'price' => $price,
            ] + $enderecoDados);
            Log::info('[AgendamentoController@store] Agendamento criado (pendente de preferência)', [
                'agendamento_id' => $agendamento->id,
            ]);

            // 2) Criar pagamento
            $pagamento = Pagamento::create([
                'assinatura_id' => null,
                'agendamento_id' => $agendamento->id,
                'amount' => $agendamento->price,
                'status' => 'pendente',
                'method' => null,
                'transaction_id' => null,
                'due_date' => Carbon::now()->toDateString(),
                'notes' => 'Pagamento de sessão (agendamento #' . $agendamento->id . ')',
            ]);
            Log::debug('[AgendamentoController@store] Pagamento criado', [ 'pagamento_id' => $pagamento->id ]);

            /** @var UnifiedPaymentService $paymentService */
            $paymentService = app(UnifiedPaymentService::class);

            // 3) Criar preferência MP
            $preference = $paymentService->createUnifiedPayment([
                'title' => 'Sessão de Fisioterapia',
                'description' => 'Pagamento do agendamento #' . $agendamento->id,
                'amount' => (float) $agendamento->price,
                'payer' => [ 'name' => $user->name, 'email' => $user->email ],
                'payment_methods' => 'all',
                'success_url' => route('paciente.pagamentos.success'),
                'failure_url' => route('paciente.pagamentos.failure'),
                'pending_url' => route('paciente.pagamentos.pending'),
                'external_reference' => (string) $pagamento->id,
                'notification_url' => route('mercadopago.webhook'),
            ]);

            Log::info('[AgendamentoController@store] Preferência Mercado Pago (resposta)', [
                'success' => $preference['success'] ?? null,
                'preference_id' => $preference['preference_id'] ?? null,
                'message' => $preference['message'] ?? null,
                'redirect_whatsapp' => $preference['redirect_whatsapp'] ?? false,
            ]);

            if (!($preference['success'] ?? false)) {
                DB::rollBack();

                if (($preference['redirect_whatsapp'] ?? false) === true) {
                    $whatsappUrl = config('app.whatsapp_url', 'https://wa.me/5511978196207');
                    $message = urlencode('Olá! Gostaria de realizar o pagamento do meu agendamento.');
                    return redirect($whatsappUrl . '?text=' . $message);
                }
                return back()->with('error', $preference['message'] ?? 'Não foi possível iniciar o pagamento. Tente novamente.');
            }

            // 4) Persistir preference_id e commit
            $pagamento->update([
                'preference_id' => $preference['preference_id'] ?? null,
            ]);

            DB::commit();

            // 5) Notificar fisioterapeuta (best-effort)
            try {
                $notificacaoService = new NotificacaoService();
                $notificacaoService->notificarNovoAgendamento($agendamento);
            } catch (\Throwable $notifyEx) {
                Log::warning('[AgendamentoController@store] Falha ao notificar fisioterapeuta', ['error' => $notifyEx->getMessage()]);
            }

            // 6) Redirecionar para checkout
            $checkoutUrl = $preference['init_point'] ?? null;
            Log::debug('[AgendamentoController@store] Redirecionando para checkout', ['checkout_url' => $checkoutUrl]);
            if ($checkoutUrl) {
                return Inertia::location($checkoutUrl);
            }

            return back()->with('error', 'Não foi possível abrir o checkout de pagamento.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Falha ao criar agendamento/pagamento e iniciar checkout', [ 'error' => $e->getMessage() ]);
            return back()->with('error', 'Erro ao iniciar pagamento. Tente novamente.');
        }
    }

    /**
     * Show the specified appointment.
     */
    public function show(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        $user = auth()->user();
        if ($agendamento->paciente_id !== $user->id) {
            abort(403);
        }

        $agendamento->load(['fisioterapeuta.user', 'avaliacao', 'pagamento']);

        // Verificar e atualizar status do pagamento se estiver pendente
        if ($agendamento->hasPayment() && $agendamento->pagamento->status === 'pendente') {
            $agendamento->verificarEConfirmarPagamento();
            
            // Recarregar o agendamento para obter os dados atualizados
            $agendamento->refresh();
        }

        // Verificar status do pagamento
        $pagamentoPendente = false;
        $paymentLink = null;
        $paymentStatus = null;
        $paymentMethod = null;
        $paymentAmount = null;

        if ($agendamento->hasPayment()) {
            $pagamentoPendente = $agendamento->pagamento->status === 'pendente';
            $paymentStatus = $agendamento->pagamento->status;
            $paymentMethod = $agendamento->pagamento->method;
            $paymentAmount = $agendamento->pagamento->amount;
            
            // Se pagamento pendente, obter link de pagamento
            if ($pagamentoPendente) {
                $paymentLink = $agendamento->getPaymentLink();
            }
        }

        // Se pagamento está pendente, mostrar status como pendente independentemente do status atual
        $statusParaExibir = $pagamentoPendente ? 'pendente' : $agendamento->status;
        
        // Formatar dados para o frontend
        $agendamentoFormatado = [
            'id' => $agendamento->id,
            'data_agendamento' => $agendamento->scheduled_at ? $agendamento->scheduled_at->format('Y-m-d') : null,
            'horario' => $agendamento->scheduled_at ? $agendamento->scheduled_at->format('H:i:s') : null,
            'status' => $statusParaExibir,
            'tipo' => $agendamento->service_type ?? 'sessao',
            'observacoes' => $agendamento->notes,
            'appointment_type' => $agendamento->appointment_type,
            'appointment_type_notes' => $agendamento->appointment_type_notes,
            'endereco_atendimento' => [
                'logradouro' => $agendamento->address ?: 'Endereço não informado',
                'numero' => '', // Pode ser extraído do logradouro se necessário
                'complemento' => $agendamento->address_line2 ?: '',
                'bairro' => '', // Campo não disponível no modelo atual
                'cidade' => $agendamento->address_city ?: '',
                'estado' => $agendamento->address_state ?: '',
                'cep' => $agendamento->address_zip_code ?: '',
                'tipo' => $agendamento->address_type ?: 'residencia',
            ],
            'fisioterapeuta' => [
                'id' => $agendamento->fisioterapeuta?->id ?? null,
                'user' => [
                    'name' => $agendamento->fisioterapeuta?->user?->name ?? 'Fisioterapeuta',
                    'email' => $agendamento->fisioterapeuta?->user?->email ?? '',
                    'phone' => $agendamento->fisioterapeuta?->user?->phone ?? '',
                ],
                'especialidades' => $agendamento->fisioterapeuta?->specializations ?? [],
                'specializations' => $agendamento->fisioterapeuta?->specializations ?? [], // Manter compatibilidade
                'crefito' => $agendamento->fisioterapeuta?->crefito ?? 'CREFITO-N/I',
                'bio' => $agendamento->fisioterapeuta?->bio ?? '',
                'rating' => $agendamento->fisioterapeuta?->rating ?? 0,
                'total_reviews' => $agendamento->fisioterapeuta?->total_reviews ?? 0,
                'hourly_rate' => $agendamento->fisioterapeuta?->hourly_rate ?? 0,
                'session_rate' => $agendamento->fisioterapeuta?->session_rate ?? null,
                'travel_fee' => $agendamento->fisioterapeuta?->travel_fee ?? null,
                'pricing_mode' => $agendamento->fisioterapeuta?->pricing_mode ?? 'por_hora',
                'available_areas' => $agendamento->fisioterapeuta?->available_areas ?? [],
            ],
            'pagamento' => [
                'pendente' => $pagamentoPendente,
                'status' => $paymentStatus,
                'method' => $paymentMethod,
                'amount' => $paymentAmount ? 'R$ ' . number_format($paymentAmount, 2, ',', '.') : null,
                'payment_link' => $paymentLink,
            ],
            'created_at' => $agendamento->created_at->toISOString(),
            'updated_at' => $agendamento->updated_at->toISOString(),
        ];

        return Inertia::render('paciente/agendamentos/show', [
            'agendamento' => $agendamentoFormatado,
            'podeEditar' => $agendamento->status === 'agendado',
            'podeCancelar' => in_array($agendamento->status, ['agendado', 'confirmado']),
            'pagamentoPendente' => $pagamentoPendente,
        ]);
    }

    /**
     * Cancel an appointment.
     */
    public function cancel(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Nova política de cancelamento:
        // 1) Dentro de 24h após a criação (created_at + 24h) pode cancelar sempre.
        // 2) Após esse período, só pode cancelar se faltarem pelo menos 24h para o horário marcado (scheduled_at - now >= 24h).
        $now = Carbon::now();
        $withinCreationGrace = $agendamento->created_at && $now->lessThanOrEqualTo($agendamento->created_at->copy()->addHours(24));
        $has24hBefore = Carbon::parse($agendamento->scheduled_at)->diffInHours($now) >= 24;
        if (!($withinCreationGrace || $has24hBefore)) {
            return back()->withErrors(['cancel' => 'Cancelamento permitido até 24h após a criação do agendamento ou, após esse período, apenas com 24h de antecedência do horário marcado.']);
        }

        if (!in_array($agendamento->status, ['agendado', 'confirmado'])) {
            return back()->withErrors(['cancel' => 'Este agendamento não pode ser cancelado.']);
        }

        $agendamento->update([
            'status' => 'cancelado',
            'notes' => ($agendamento->notes ?? '') . "\n\nCancelado pelo paciente em " . Carbon::now()->format('d/m/Y H:i'),
        ]);

        // Enviar notificação para o fisioterapeuta
        $notificacaoService = new NotificacaoService();
        $notificacaoService->notificarAgendamentoCancelado($agendamento, 'Cancelado pelo paciente');

        return redirect()->route('paciente.agendamentos.index')
            ->with('success', 'Agendamento cancelado com sucesso.');
    }

    /**
     * Show reschedule form
     */
    public function reschedule(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se pode reagendar (pelo menos 24h de antecedência)
        if (Carbon::parse($agendamento->scheduled_at)->diffInHours(Carbon::now()) < 24) {
            return back()->withErrors(['reschedule' => 'Agendamentos só podem ser reagendados com pelo menos 24 horas de antecedência.']);
        }

        if (!in_array($agendamento->status, ['agendado', 'confirmado'])) {
            return back()->withErrors(['reschedule' => 'Este agendamento não pode ser reagendado.']);
        }

        $agendamento->load(['fisioterapeuta.user', 'fisioterapeuta.fisioterapeuta']);

        return Inertia::render('paciente/agendamentos/reschedule', [
            'agendamento' => $agendamento,
            'fisioterapeuta' => $agendamento->fisioterapeuta,
        ]);
    }

    /**
     * Update reschedule
     */
    public function updateReschedule(Request $request, Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se pode reagendar
        if (Carbon::parse($agendamento->scheduled_at)->diffInHours(Carbon::now()) < 24) {
            return back()->withErrors(['reschedule' => 'Agendamentos só podem ser reagendados com pelo menos 24 horas de antecedência.']);
        }

        if (!in_array($agendamento->status, ['agendado', 'confirmado'])) {
            return back()->withErrors(['reschedule' => 'Este agendamento não pode ser reagendado.']);
        }

        $validated = $request->validate([
            'data_hora' => 'required|date|after:now',
            'observacoes' => 'nullable|string|max:1000',
        ]);

        // Verificar disponibilidade do fisioterapeuta
        $conflito = Agendamento::where('fisioterapeuta_id', $agendamento->fisioterapeuta_id)
            ->where('id', '!=', $agendamento->id)
            ->where('scheduled_at', $validated['data_hora'])
            ->whereIn('status', ['agendado', 'confirmado', 'em_andamento'])
            ->exists();

        if ($conflito) {
            return back()->withErrors(['data_hora' => 'Este horário não está disponível.']);
        }

        $dataHoraAntiga = $agendamento->scheduled_at;

        $agendamento->update([
            'scheduled_at' => $validated['data_hora'],
            'status' => 'agendado', // Resetar para agendado se estava confirmado
            'notes' => $validated['observacoes'] ?? $agendamento->notes,
        ]);

        // Enviar notificação para o fisioterapeuta
        $notificacaoService = new NotificacaoService();
        $notificacaoService->notificarAgendamentoReagendado($agendamento, $dataHoraAntiga);

        return redirect()->route('paciente.agendamentos.index')
            ->with('success', 'Agendamento reagendado com sucesso!');
    }

    /**
     * Buscar horários disponíveis via AJAX
     */
    public function horariosDisponiveis(Request $request)
    {
        $validated = $request->validate([
            'fisioterapeuta_id' => 'required|exists:users,id',
            'data_agendamento' => 'required|date|after_or_equal:today',
        ]);

        $fisioterapeutaId = $validated['fisioterapeuta_id'];
        $dataAgendamento = Carbon::parse($validated['data_agendamento']);

        // Verificar se é domingo - por padrão não há atendimento aos domingos
        // a menos que o fisioterapeuta tenha horários base específicos configurados
        if ($dataAgendamento->dayOfWeek === 0) {
            // Verificar se existem horários base específicos para domingos
            $temHorariosDomingo = HorarioBase::where('fisioterapeuta_id', $fisioterapeutaId)
                ->where('dia_semana', 0) // 0 = domingo
                ->where('ativo', true)
                ->exists();

            if (!$temHorariosDomingo) {
                return response()->json([
                    'success' => false,
                    'error' => 'Não há atendimento aos domingos. Selecione outro dia.',
                    'horarios' => [],
                    'data' => $dataAgendamento->format('d/m/Y'),
                    'dia_semana' => $dataAgendamento->locale('pt_BR')->dayName,
                ]);
            }
        }

        // Usar o método de cálculo de disponibilidade que retorna APENAS horários disponíveis
        $horarios = $this->getHorariosDisponiveis($fisioterapeutaId, $validated['data_agendamento']);

        return response()->json([
            'success' => true,
            'horarios' => $horarios,
            'data' => $dataAgendamento->format('d/m/Y'),
            'dia_semana' => $dataAgendamento->locale('pt_BR')->dayName,
        ]);
    }

    /**
     * Get available time slots for appointments
     */
    private function getHorariosDisponiveisOld()
    {
        $horarios = [];
        $dataInicio = Carbon::now()->addDay();
        $dataFim = Carbon::now()->addDays(30);

        while ($dataInicio <= $dataFim) {
            // Pular finais de semana
            if ($dataInicio->isWeekend()) {
                $dataInicio->addDay();
                continue;
            }

            // Horários de 8h às 18h
            for ($hora = 8; $hora <= 17; $hora++) {
                $horario = $dataInicio->copy()->setTime($hora, 0);
                
                // Verificar se não há conflitos
                $ocupado = Agendamento::where('scheduled_at', $horario)
                    ->where('status', '!=', 'cancelado')
                    ->exists();

                if (!$ocupado) {
                    $horarios[] = [
                        'data_hora' => $horario->toISOString(),
                        'data_formatada' => $horario->format('d/m/Y'),
                        'hora_formatada' => $horario->format('H:i'),
                        'dia_semana' => $horario->locale('pt_BR')->dayName,
                    ];
                }
            }

            $dataInicio->addDay();
        }

        return collect($horarios)->groupBy('data_formatada')->toArray();
    }

    /**
     * Show session report
     */
    public function showReport(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se a sessão foi concluída e tem relatório
        if ($agendamento->status !== 'concluido' || !$agendamento->relatorioSessao) {
            return back()->withErrors(['report' => 'Relatório não disponível para esta sessão.']);
        }

        $agendamento->load(['fisioterapeuta.user', 'relatorioSessao', 'avaliacao']);

        return Inertia::render('paciente/agendamentos/report', [
            'agendamento' => $agendamento,
            'relatorio' => $agendamento->relatorioSessao,
            'avaliacao' => $agendamento->avaliacao,
        ]);
    }

    /**
     * Show evaluation form
     */
    public function showEvaluation(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se a sessão foi concluída
        if ($agendamento->status !== 'concluido') {
            return back()->withErrors(['evaluation' => 'Só é possível avaliar sessões concluídas.']);
        }

        // Verificar se já foi avaliada
        if ($agendamento->avaliacao) {
            return redirect()->route('paciente.agendamentos.report', $agendamento)
                ->with('info', 'Esta sessão já foi avaliada.');
        }

        $agendamento->load(['fisioterapeuta.user', 'relatorioSessao']);

        return Inertia::render('paciente/agendamentos/evaluate', [
            'agendamento' => $agendamento,
        ]);
    }

    /**
     * Store evaluation
     */
    public function storeEvaluation(Request $request, Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se a sessão foi concluída
        if ($agendamento->status !== 'concluido') {
            return back()->withErrors(['evaluation' => 'Só é possível avaliar sessões concluídas.']);
        }

        // Verificar se já foi avaliada
        if ($agendamento->avaliacao) {
            return back()->withErrors(['evaluation' => 'Esta sessão já foi avaliada.']);
        }

        $validated = $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
            'recommend' => 'required|boolean',
        ], [
            'rating.required' => 'A avaliação é obrigatória.',
            'rating.integer' => 'A avaliação deve ser um número.',
            'rating.min' => 'A avaliação deve ser no mínimo 1 estrela.',
            'rating.max' => 'A avaliação deve ser no máximo 5 estrelas.',
            'comment.max' => 'O comentário não pode ter mais de 1000 caracteres.',
            'recommend.required' => 'Informe se recomenda o fisioterapeuta.',
            'recommend.boolean' => 'Valor inválido para recomendação.',
        ]);

        // Criar avaliação
        Avaliacao::create([
            'agendamento_id' => $agendamento->id,
            'paciente_id' => $agendamento->paciente_id,
            'fisioterapeuta_id' => $agendamento->fisioterapeuta_id,
            'rating' => $validated['rating'],
            'comment' => $validated['comment'],
            'recommend' => $validated['recommend'],
        ]);

        // Atualizar rating médio do fisioterapeuta
        $this->updateFisioterapeutaRating($agendamento->fisioterapeuta_id);

        return redirect()->route('paciente.agendamentos.report', $agendamento)
            ->with('success', 'Avaliação enviada com sucesso! Obrigado pelo seu feedback.');
    }

    /**
     * Update fisioterapeuta average rating
     */
    private function updateFisioterapeutaRating($fisioterapeutaId)
    {
        $averageRating = Avaliacao::where('fisioterapeuta_id', $fisioterapeutaId)->avg('rating');

        $fisioterapeuta = \App\Models\Fisioterapeuta::where('user_id', $fisioterapeutaId)->first();
        if ($fisioterapeuta) {
            $fisioterapeuta->update(['rating' => round($averageRating, 2)]);
        }
    }

    /**
     * Show form to edit appointment address
     */
    public function editAddress(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se o agendamento pode ter o endereço editado
        if (!in_array($agendamento->status, ['pendente', 'agendado', 'confirmado'])) {
            return back()->withErrors(['message' => 'Não é possível editar o endereço deste agendamento.']);
        }

        return Inertia::render('paciente/agendamentos/edit-address', [
            'agendamento' => [
                'id' => $agendamento->id,
                'data_agendamento' => $agendamento->scheduled_at ? $agendamento->scheduled_at->format('Y-m-d') : null,
                'horario' => $agendamento->scheduled_at ? $agendamento->scheduled_at->format('H:i:s') : null,
                'status' => $agendamento->status,
                'endereco_atendimento' => [
                    'logradouro' => $agendamento->address ?: '',
                    'complemento' => $agendamento->address_line2 ?: '',
                    'cidade' => $agendamento->address_city ?: '',
                    'estado' => $agendamento->address_state ?: '',
                    'cep' => $agendamento->address_zip_code ?: '',
                    'tipo' => $agendamento->address_type ?: 'residencia',
                ],
            ],
        ]);
    }

    /**
     * Update appointment address
     */
    public function updateAddress(Request $request, Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se o agendamento pode ter o endereço editado
        if (!in_array($agendamento->status, ['pendente', 'agendado', 'confirmado'])) {
            return back()->withErrors(['message' => 'Não é possível editar o endereço deste agendamento.']);
        }

        $validated = $request->validate([
            'address' => 'required|string|max:255',
            'address_line2' => 'nullable|string|max:255',
            'address_city' => 'required|string|max:255',
            'address_state' => 'required|string|size:2',
            'address_zip_code' => 'required|string|max:10',
            'address_type' => 'required|in:residencia,trabalho,outro',
        ], [
            'address.required' => 'Logradouro é obrigatório.',
            'address.max' => 'Logradouro não pode ter mais de 255 caracteres.',
            'address_line2.max' => 'Complemento não pode ter mais de 255 caracteres.',
            'address_city.required' => 'Cidade é obrigatória.',
            'address_city.max' => 'Cidade não pode ter mais de 255 caracteres.',
            'address_state.required' => 'Estado é obrigatório.',
            'address_state.size' => 'Estado deve ter exatamente 2 letras.',
            'address_zip_code.required' => 'CEP é obrigatório.',
            'address_zip_code.max' => 'CEP não pode ter mais de 10 caracteres.',
            'address_type.required' => 'Tipo de endereço é obrigatório.',
            'address_type.in' => 'Tipo de endereço inválido.',
        ]);

        $agendamento->update([
            'address' => $validated['address'],
            'address_line2' => $validated['address_line2'],
            'address_city' => $validated['address_city'],
            'address_state' => $validated['address_state'],
            'address_zip_code' => $validated['address_zip_code'],
            'address_type' => $validated['address_type'],
        ]);

        return redirect()->route('paciente.agendamentos.show', $agendamento)
            ->with('success', 'Endereço do agendamento atualizado com sucesso!');
    }

    /**
     * Buscar fisioterapeutas disponíveis via AJAX
     */
    public function fisioterapeutasDisponiveis(Request $request)
    {
        $validated = $request->validate([
            'data' => 'required|date|after:today',
            'horario' => 'required|date_format:H:i',
            'area' => 'nullable|string',
            'especializacao' => 'nullable|string',
        ]);

        // Usar o novo sistema de agendamento
        $agendamentoController = new \App\Http\Controllers\AgendamentoController();
        $response = $agendamentoController->fisioterapeutasDisponiveis($request);

        return $response;
    }

    /**
     * Verificar disponibilidade via AJAX
     */
    public function verificarDisponibilidadeApi(Request $request)
    {
        $validated = $request->validate([
            'fisioterapeuta_id' => 'required|exists:users,id',
            'data_hora' => 'required|date|after:now',
        ]);

        // Usar o novo sistema de agendamento
        $agendamentoController = new \App\Http\Controllers\AgendamentoController();
        $response = $agendamentoController->verificarDisponibilidadeApi($request);

        return $response;
    }

}
