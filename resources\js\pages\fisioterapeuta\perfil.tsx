import { AvatarUpload } from '@/components/avatar-upload';
import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { useMemo, useState } from 'react';
import { Save, User } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/fisioterapeuta/dashboard',
    },
    {
        title: 'Perfil',
        href: '/fisioterapeuta/perfil',
    },
];

interface User {
    id: number;
    name: string;
    email: string;
    phone?: string;
    avatar?: string;
}

interface Fisioterapeuta {
    id: number;
    crefito: string;
    specializations: string[];
    bio: string;
    hourly_rate: number;
    session_rate?: number | null;
    service_rates?: {
        avaliacao?: number | null;
        sessao?: number | null;
        teleatendimento?: number | null;
    } | null;
    available_areas: string[];
    available: boolean;
    rating: number;
    total_reviews: number;
}

interface Props {
    fisioterapeuta: Fisioterapeuta;
    user: User;
    especialidades: string[];
    areas: string[];
    // Percentual de taxa de serviço vinda do backend (ex.: 20 para 20%)
    serviceFeePercent: number;
}

export default function FisioterapeutaPerfil({ fisioterapeuta, user, especialidades, areas, serviceFeePercent }: Props) {
    // Helpers para normalizar números vindos do backend (decimal cast -> string)
    const toNum = (v: unknown, def = 0): number => {
        if (v === null || v === undefined || v === '') return def;
        const n = typeof v === 'string' ? Number(v) : (v as number);
        return Number.isFinite(n) ? n : def;
    };
    const toOptNum = (v: unknown): number | undefined => {
        if (v === null || v === undefined || v === '') return undefined;
        const n = typeof v === 'string' ? Number(v) : (v as number);
        return Number.isFinite(n) ? n : undefined;
    };
    const { data, setData, put, processing, errors, isDirty, transform } = useForm({
        // Dados do usuário
        name: user.name || '',
        phone: user.phone || '',

        // Dados do fisioterapeuta
        crefito: fisioterapeuta.crefito || '',
        specializations: fisioterapeuta.specializations || [],
        bio: fisioterapeuta.bio || '',
        hourly_rate: toNum(fisioterapeuta.hourly_rate, 0),
        session_rate: toOptNum(fisioterapeuta.session_rate),
        service_rates: {
            ...(fisioterapeuta.service_rates ?? {}),
            avaliacao: toOptNum((fisioterapeuta.service_rates as any)?.avaliacao),
            sessao: toOptNum((fisioterapeuta.service_rates as any)?.sessao),
            teleatendimento: toOptNum((fisioterapeuta.service_rates as any)?.teleatendimento),
        },
        available_areas: fisioterapeuta.available_areas || [],
        available: fisioterapeuta.available || false,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Normaliza os números para envio
        transform((d: typeof data) => ({
            ...d,
            hourly_rate: toNum(d.hourly_rate, 0),
            session_rate: toOptNum(d.session_rate),
            service_rates: {
                ...(d.service_rates ?? {}),
                avaliacao: toOptNum((d.service_rates as any)?.avaliacao),
                sessao: toOptNum((d.service_rates as any)?.sessao),
                teleatendimento: toOptNum((d.service_rates as any)?.teleatendimento),
            },
        }));
        put(route('fisioterapeuta.perfil.update'));
    };

    const format2 = (v: number | undefined) => (typeof v === 'number' && !isNaN(v) ? v.toFixed(2) : '');
    const to2 = (v: number) => Number(Number(v || 0).toFixed(2));

    // Currency helpers (pt-BR)
    const nf = useMemo(() => new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }), []);
    const formatBRL = (val?: number) => (typeof val === 'number' && !isNaN(val) ? nf.format(val) : '');
    const onlyDigits = (s: string) => (s.match(/\d+/g)?.join('') ?? '');
    // Taxa de serviço (percentual -> multiplicador)
    const serviceFeeMultiplier = useMemo(() => 1 + (Number(serviceFeePercent || 0) / 100), [serviceFeePercent]);

    // Display states for masked inputs
    const [hourlyDisplay, setHourlyDisplay] = useState<string>(formatBRL(toNum(data.hourly_rate)));
    const [sessionDisplay, setSessionDisplay] = useState<string>(
        data.session_rate != null ? formatBRL(toNum(data.session_rate)) : ''
    );
    // Service rates displays
    const [avaliacaoDisplay, setAvaliacaoDisplay] = useState<string>(
        data.service_rates?.avaliacao != null ? formatBRL(toNum(data.service_rates?.avaliacao as number)) : ''
    );
    const [sessaoDisplay, setSessaoDisplay] = useState<string>(
        data.service_rates?.sessao != null ? formatBRL(toNum(data.service_rates?.sessao as number)) : ''
    );
    const [teleDisplay, setTeleDisplay] = useState<string>(
        data.service_rates?.teleatendimento != null ? formatBRL(toNum(data.service_rates?.teleatendimento as number)) : ''
    );

    const handleCurrencyChange = (
        raw: string,
        setDisplay: (s: string) => void,
        setNumeric: (n?: number) => void,
        allowEmpty = false
    ) => {
        if (allowEmpty && raw.trim() === '') {
            setDisplay('');
            setNumeric(undefined);
            return;
        }
        const digits = onlyDigits(raw);
        if (digits.length === 0) {
            setDisplay('');
            setNumeric(allowEmpty ? undefined : 0);
            return;
        }
        const cents = parseInt(digits, 10);
        const value = cents / 100;
        setDisplay(nf.format(value));
        setNumeric(value);
    };

    const handleSpecializationChange = (especialidade: string, checked: boolean) => {
        if (checked) {
            setData('specializations', [...data.specializations, especialidade]);
        } else {
            setData(
                'specializations',
                data.specializations.filter((s) => s !== especialidade),
            );
        }
    };

    const handleAreaChange = (area: string, checked: boolean) => {
        if (checked) {
            setData('available_areas', [...data.available_areas, area]);
        } else {
            setData(
                'available_areas',
                data.available_areas.filter((a) => a !== area),
            );
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Meu Perfil" />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold">Meu Perfil</h1>
                        <p className="text-muted-foreground">Gerencie suas informações profissionais</p>
                    </div>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Avatar */}
                    <div className="lg:col-span-1">
                        <AvatarUpload
                            user={user}
                            uploadUrl={route('fisioterapeuta.perfil.avatar.upload')}
                            removeUrl={route('fisioterapeuta.perfil.avatar.remove')}
                        />
                    </div>

                    {/* Formulário */}
                    <div className="lg:col-span-2">
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Informações Pessoais */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Informações Pessoais</CardTitle>
                                    <CardDescription>Suas informações básicas de contato</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="name">Nome Completo</Label>
                                            <Input
                                                id="name"
                                                value={data.name}
                                                onChange={(e) => setData('name', e.target.value)}
                                                placeholder="Seu nome completo"
                                            />
                                            <InputError message={errors.name} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="phone">Telefone</Label>
                                            <Input
                                                id="phone"
                                                value={data.phone}
                                                onChange={(e) => setData('phone', e.target.value)}
                                                placeholder="(11) 99999-9999"
                                            />
                                            <InputError message={errors.phone} />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Informações Profissionais */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Informações Profissionais</CardTitle>
                                    <CardDescription>Dados sobre sua formação e experiência</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="crefito">CREFITO</Label>
                                            <Input
                                                id="crefito"
                                                value={data.crefito}
                                                onChange={(e) => setData('crefito', e.target.value)}
                                                placeholder="CREFITO-3/123456-F"
                                            />
                                            <InputError message={errors.crefito} />
                                        </div>

                                        {/* Campos de preços foram movidos para a seção "Preços por Serviço" */}
                                        {/* Taxa de deslocamento removida da interface */}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="bio">Biografia Profissional</Label>
                                        <Textarea
                                            id="bio"
                                            value={data.bio}
                                            onChange={(e) => setData('bio', e.target.value)}
                                            placeholder="Conte um pouco sobre sua experiência, formação e especialidades..."
                                            rows={4}
                                        />
                                        <InputError message={errors.bio} />
                                        <p className="text-sm text-muted-foreground">{data.bio.length}/1000 caracteres</p>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="available"
                                            checked={data.available}
                                            onCheckedChange={(checked) => setData('available', checked)}
                                        />
                                        <Label htmlFor="available">Disponível para novos agendamentos</Label>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Preços por Serviço (opcional) */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Preços por Serviço</CardTitle>
                                    <CardDescription>Defina valores específicos para cada tipo de atendimento (opcional)</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {/* Valores gerais */}
                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="hourly_rate">Valor da Hora (R$)</Label>
                                            <Input
                                                id="hourly_rate"
                                                type="text"
                                                inputMode="numeric"
                                                value={hourlyDisplay}
                                                onChange={(e) => handleCurrencyChange(e.target.value, setHourlyDisplay, (n) => setData('hourly_rate', n ?? 0))}
                                                onBlur={() => setHourlyDisplay(formatBRL(data.hourly_rate))}
                                                placeholder="R$ 80,00"
                                            />
                                            <InputError message={errors.hourly_rate} />
                                            <p className={`text-sm ${data.hourly_rate < 10 ? 'text-destructive' : 'text-muted-foreground'}`}>
                                                {data.hourly_rate < 10 ? 'Mínimo permitido: R$ 10,00' : 'Valor mínimo permitido: R$ 10,00'}
                                            </p>
                                            {/* Exibição da taxa e valor final ao paciente */}
                                            {typeof data.hourly_rate === 'number' && data.hourly_rate > 0 && (
                                                <div className="text-sm text-gray-700 space-y-0.5">
                                                    <p>
                                                        <span className="font-medium">Taxa de serviço:</span> {serviceFeePercent}%
                                                    </p>
                                                    <p>
                                                        <span className="font-medium">Total cobrado do paciente:</span> {formatBRL(to2(data.hourly_rate * serviceFeeMultiplier))}
                                                        <span className="text-muted-foreground"> (você recebe {formatBRL(to2(data.hourly_rate))})</span>
                                                    </p>
                                                </div>
                                            )}
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="session_rate">Valor por Sessão (R$) — opcional</Label>
                                            <Input
                                                id="session_rate"
                                                type="text"
                                                inputMode="numeric"
                                                value={sessionDisplay}
                                                onChange={(e) => handleCurrencyChange(e.target.value, setSessionDisplay, (n) => setData('session_rate', n), true)}
                                                onBlur={() => setSessionDisplay(formatBRL(data.session_rate))}
                                                placeholder="Ex: R$ 120,00"
                                            />
                                            {errors.session_rate && <p className="text-sm text-destructive">{errors.session_rate}</p>}
                                            {data.session_rate != null && (
                                                <p className={`text-sm ${data.session_rate < 10 ? 'text-destructive' : 'text-muted-foreground'}`}>
                                                    {data.session_rate < 10 ? 'Mínimo permitido: R$ 10,00' : 'Valor mínimo recomendado: R$ 10,00'}
                                                </p>
                                            )}
                                            {/* Exibição da taxa e valor final ao paciente para sessão */}
                                            {typeof data.session_rate === 'number' && data.session_rate > 0 && (
                                                <div className="text-sm text-gray-700 space-y-0.5">
                                                    <p>
                                                        <span className="font-medium">Taxa de serviço:</span> {serviceFeePercent}%
                                                    </p>
                                                    <p>
                                                        <span className="font-medium">Total cobrado do paciente:</span> {formatBRL(to2(data.session_rate * serviceFeeMultiplier))}
                                                        <span className="text-muted-foreground"> (você recebe {formatBRL(to2(data.session_rate))})</span>
                                                    </p>
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    {/* Valores específicos por serviço */}
                                    <div className="grid gap-4 sm:grid-cols-3">
                                        <div className="space-y-2">
                                            <Label htmlFor="service_avaliacao">Avaliação (R$)</Label>
                                            <Input
                                                id="service_avaliacao"
                                                type="text"
                                                inputMode="numeric"
                                                value={avaliacaoDisplay}
                                                onChange={(e) =>
                                                    handleCurrencyChange(
                                                        e.target.value,
                                                        setAvaliacaoDisplay,
                                                        (n) => setData('service_rates', { ...(data.service_rates ?? {}), avaliacao: n }),
                                                        true,
                                                    )
                                                }
                                                onBlur={() => setAvaliacaoDisplay(formatBRL(data.service_rates?.avaliacao as number))}
                                                placeholder="Ex: R$ 150,00"
                                            />
                                            {(errors as any)['service_rates.avaliacao'] && (
                                                <p className="text-sm text-destructive">{(errors as any)['service_rates.avaliacao']}</p>
                                            )}
                                            {/* Exibição da taxa e valor final ao paciente para avaliação */}
                                            {typeof data.service_rates?.avaliacao === 'number' && data.service_rates.avaliacao > 0 && (
                                                <div className="text-sm text-gray-700 space-y-0.5 mt-1">
                                                    <p>
                                                        <span className="font-medium">Total:</span> {formatBRL(to2(data.service_rates.avaliacao * serviceFeeMultiplier))}
                                                        <span className="text-muted-foreground"> (você recebe {formatBRL(to2(data.service_rates.avaliacao))})</span>
                                                    </p>
                                                </div>
                                            )}
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="service_sessao">Sessão Padrão (R$)</Label>
                                            <Input
                                                id="service_sessao"
                                                type="text"
                                                inputMode="numeric"
                                                value={sessaoDisplay}
                                                onChange={(e) =>
                                                    handleCurrencyChange(
                                                        e.target.value,
                                                        setSessaoDisplay,
                                                        (n) => setData('service_rates', { ...(data.service_rates ?? {}), sessao: n }),
                                                        true,
                                                    )
                                                }
                                                onBlur={() => setSessaoDisplay(formatBRL(data.service_rates?.sessao as number))}
                                                placeholder="Ex: R$ 120,00"
                                            />
                                            {(errors as any)['service_rates.sessao'] && (
                                                <p className="text-sm text-destructive">{(errors as any)['service_rates.sessao']}</p>
                                            )}
                                            {/* Exibição da taxa e valor final ao paciente para sessao */}
                                            {typeof data.service_rates?.sessao === 'number' && data.service_rates.sessao > 0 && (
                                                <div className="text-sm text-gray-700 space-y-0.5 mt-1">
                                                    <p>
                                                        <span className="font-medium">Total:</span> {formatBRL(to2(data.service_rates.sessao * serviceFeeMultiplier))}
                                                        <span className="text-muted-foreground"> (você recebe {formatBRL(to2(data.service_rates.sessao))})</span>
                                                    </p>
                                                </div>
                                            )}
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="service_tele">Teleatendimento (R$)</Label>
                                            <Input
                                                id="service_tele"
                                                type="text"
                                                inputMode="numeric"
                                                value={teleDisplay}
                                                onChange={(e) =>
                                                    handleCurrencyChange(
                                                        e.target.value,
                                                        setTeleDisplay,
                                                        (n) => setData('service_rates', { ...(data.service_rates ?? {}), teleatendimento: n }),
                                                        true,
                                                    )
                                                }
                                                onBlur={() => setTeleDisplay(formatBRL(data.service_rates?.teleatendimento as number))}
                                                placeholder="Ex: R$ 100,00"
                                            />
                                            {(errors as any)['service_rates.teleatendimento'] && (
                                                <p className="text-sm text-destructive">{(errors as any)['service_rates.teleatendimento']}</p>
                                            )}
                                            {/* Exibição da taxa e valor final ao paciente para teleatendimento */}
                                            {typeof data.service_rates?.teleatendimento === 'number' && data.service_rates.teleatendimento > 0 && (
                                                <div className="text-sm text-gray-700 space-y-0.5 mt-1">
                                                    <p>
                                                        <span className="font-medium">Total:</span> {formatBRL(to2(data.service_rates.teleatendimento * serviceFeeMultiplier))}
                                                        <span className="text-muted-foreground"> (você recebe {formatBRL(to2(data.service_rates.teleatendimento))})</span>
                                                    </p>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                    <p className="text-sm text-muted-foreground">Se não definir um valor específico, será usado o valor da sessão ou da hora.</p>
                                </CardContent>
                            </Card>

                            {/* Especialidades */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Especialidades</CardTitle>
                                    <CardDescription>Selecione suas áreas de especialização</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
                                        {especialidades.map((especialidade) => (
                                            <div key={especialidade} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={`especialidade-${especialidade}`}
                                                    checked={data.specializations.includes(especialidade)}
                                                    onCheckedChange={(checked) => handleSpecializationChange(especialidade, checked as boolean)}
                                                />
                                                <Label htmlFor={`especialidade-${especialidade}`} className="text-sm font-normal">
                                                    {especialidade}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                    {errors.specializations && <p className="mt-2 text-sm text-destructive">{errors.specializations}</p>}
                                </CardContent>
                            </Card>

                            {/* Áreas de Atendimento */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Áreas de Atendimento</CardTitle>
                                    <CardDescription>Selecione as regiões onde você atende</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid gap-3 sm:grid-cols-2">
                                        {areas.map((area) => (
                                            <div key={area} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={`area-${area}`}
                                                    checked={data.available_areas.includes(area)}
                                                    onCheckedChange={(checked) => handleAreaChange(area, checked as boolean)}
                                                />
                                                <Label htmlFor={`area-${area}`} className="text-sm font-normal">
                                                    {area}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                    {errors.available_areas && <p className="mt-2 text-sm text-destructive">{errors.available_areas}</p>}
                                </CardContent>
                            </Card>

                            {/* Botão de Salvar */}
                            <div className="flex justify-end">
                                <Button type="submit" disabled={processing || !isDirty} className="gap-2">
                                    <Save className="h-4 w-4" />
                                    {processing ? 'Salvando...' : 'Salvar Alterações'}
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
