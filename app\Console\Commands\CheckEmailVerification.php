<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckEmailVerification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-email-verification {email? : The email address to check} {--verify : Mark the email as verified}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and optionally update email verification status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email') ?: '<EMAIL>';
        
        $user = \App\Models\User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email {$email} not found!");
            return 1;
        }
        
        $this->info("User found:");
        $this->line("ID: " . $user->id);
        $this->line("Name: " . $user->name);
        $this->line("Email: " . $user->email);
        $this->line("Email Verified: " . ($user->hasVerifiedEmail() ? '✅ Yes' : '❌ No'));
        
        if ($user->email_verified_at) {
            $this->line("Verified At: " . $user->email_verified_at);
        }
        
        if ($this->option('verify') && !$user->hasVerifiedEmail()) {
            if ($this->confirm('Are you sure you want to mark this email as verified?')) {
                $user->markEmailAsVerified();
                $this->info("✅ Email marked as verified!");
            }
        } elseif ($this->option('verify')) {
            $this->info("ℹ️ Email is already verified.");
        }
        
        return 0;
    }
}
