import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { formatCurrency, formatDateTime } from '@/lib/utils';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Calendar, CalendarDays, CheckCircle, Clock, Filter, MapPin, Phone, Plus, Search, Trash2, User, XCircle } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/fisioterapeuta/dashboard',
    },
    {
        title: 'Agenda',
        href: '/fisioterapeuta/agenda',
    },
];

interface Agendamento {
    id: number;
    scheduled_at: string;
    duration: number;
    status: string;
    service_type: string;
    notes?: string;
    address: string;
    price: number;
    paciente: {
        id: number;
        name: string;
        phone?: string;
        avatar?: string;
    };
    assinatura?: {
        plano: {
            name: string;
        };
    };
}

interface Props {
    agendamentos: {
        data: Agendamento[];
        links: any[];
        meta: any;
    };
    filters: {
        date?: string;
        status?: string;
        search?: string;
    };
}

const statusColors = {
    agendado: 'bg-blue-100 text-blue-800',
    confirmado: 'bg-green-100 text-green-800',
    em_andamento: 'bg-yellow-100 text-yellow-800',
    concluido: 'bg-emerald-100 text-emerald-800',
    cancelado: 'bg-red-100 text-red-800',
    reagendado: 'bg-purple-100 text-purple-800',
};

const statusLabels = {
    agendado: 'Agendado',
    confirmado: 'Confirmado',
    em_andamento: 'Em Andamento',
    concluido: 'Concluído',
    cancelado: 'Cancelado',
    reagendado: 'Reagendado',
};

export default function FisioterapeutaAgenda({ agendamentos, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters?.search || '');
    const [selectedDate, setSelectedDate] = useState(filters?.date || '');
    const [selectedStatus, setSelectedStatus] = useState(filters?.status || '');

    // Estados para os modais
    const [reagendarData, setReagendarData] = useState({ dataHora: '', motivo: '' });
    const [cancelarMotivo, setCancelarMotivo] = useState('');

    const getStatusBadge = (status: string) => {
        const colorClass = statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800';
        const label = statusLabels[status as keyof typeof statusLabels] || status;

        return <Badge className={colorClass}>{label}</Badge>;
    };

    const handleSearch = () => {
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (selectedDate) params.append('date', selectedDate);
        if (selectedStatus && selectedStatus !== 'todos') params.append('status', selectedStatus);

        window.location.href = `/fisioterapeuta/agenda?${params.toString()}`;
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedDate('');
        setSelectedStatus('todos');
        window.location.href = '/fisioterapeuta/agenda';
    };

    const handleAceitar = (agendamento: Agendamento) => {
        router.post(
            `/fisioterapeuta/agenda/${agendamento.id}/aceitar`,
            {},
            {
                preserveScroll: true,
            },
        );
    };

    const handleRecusar = (agendamento: Agendamento, motivo: string) => {
        router.post(
            `/fisioterapeuta/agenda/${agendamento.id}/recusar`,
            {
                motivo: motivo,
            },
            {
                preserveScroll: true,
            },
        );
    };

    const handleReagendar = (agendamento: Agendamento, dataHora: string, motivo?: string) => {
        router.post(
            `/fisioterapeuta/agenda/${agendamento.id}/reagendar`,
            {
                data_hora: dataHora,
                motivo: motivo,
            },
            {
                preserveScroll: true,
            },
        );
    };

    const handleCancelar = (agendamento: Agendamento, motivo: string) => {
        router.post(
            `/fisioterapeuta/agenda/${agendamento.id}/cancelar`,
            {
                motivo: motivo,
            },
            {
                preserveScroll: true,
            },
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Agenda" />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold">Agenda</h1>
                        <p className="text-muted-foreground">Gerencie seus agendamentos e consultas</p>
                    </div>
                    <Button asChild>
                        <Link href="/fisioterapeuta/disponibilidade">
                            <Plus className="mr-2 h-4 w-4" />
                            Gerenciar Disponibilidade
                        </Link>
                    </Button>
                </div>

                {/* Filtros */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filtros
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-4">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Buscar paciente</label>
                                <div className="relative">
                                    <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                                    <Input
                                        placeholder="Nome do paciente..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Data</label>
                                <Input type="date" value={selectedDate} onChange={(e) => setSelectedDate(e.target.value)} />
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Status</label>
                                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Todos os status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="todos">Todos os status</SelectItem>
                                        <SelectItem value="agendado">Agendado</SelectItem>
                                        <SelectItem value="confirmado">Confirmado</SelectItem>
                                        <SelectItem value="em_andamento">Em Andamento</SelectItem>
                                        <SelectItem value="concluido">Concluído</SelectItem>
                                        <SelectItem value="cancelado">Cancelado</SelectItem>
                                        <SelectItem value="reagendado">Reagendado</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex items-end gap-2">
                                <Button onClick={handleSearch} className="flex-1">
                                    Filtrar
                                </Button>
                                <Button variant="outline" onClick={clearFilters}>
                                    Limpar
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Lista de Agendamentos */}
                {agendamentos?.data?.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Calendar className="h-12 w-12 text-muted-foreground" />
                            <h3 className="mt-4 text-lg font-semibold">Nenhum agendamento encontrado</h3>
                            <p className="text-muted-foreground">
                                {filters?.search || filters?.date || filters?.status
                                    ? 'Tente ajustar os filtros para encontrar agendamentos.'
                                    : 'Você ainda não possui agendamentos. Eles aparecerão aqui quando os pacientes agendarem consultas.'}
                            </p>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="space-y-4">
                        {agendamentos?.data?.map((agendamento) => (
                            <Card key={agendamento.id} className="transition-shadow hover:shadow-md">
                                <CardContent className="p-6">
                                    <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
                                        <div className="flex-1 space-y-3">
                                            <div className="flex items-start justify-between">
                                                <div className="flex items-center gap-3">
                                                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                                                        <User className="h-5 w-5 text-primary" />
                                                    </div>
                                                    <div>
                                                        <h3 className="font-semibold">{agendamento.paciente.name}</h3>
                                                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                                            <Calendar className="h-4 w-4" />
                                                            {formatDateTime(agendamento.scheduled_at)}
                                                        </div>
                                                    </div>
                                                </div>
                                                {getStatusBadge(agendamento.status)}
                                            </div>

                                            <div className="grid gap-2 sm:grid-cols-2 lg:grid-cols-3">
                                                <div className="flex items-center gap-2 text-sm">
                                                    <Clock className="h-4 w-4 text-muted-foreground" />
                                                    <span>{agendamento.duration} minutos</span>
                                                </div>
                                                {agendamento.paciente.phone && (
                                                    <div className="flex items-center gap-2 text-sm">
                                                        <Phone className="h-4 w-4 text-muted-foreground" />
                                                        <span>{agendamento.paciente.phone}</span>
                                                    </div>
                                                )}
                                                <div className="flex items-center gap-2 text-sm">
                                                    <MapPin className="h-4 w-4 text-muted-foreground" />
                                                    <span className="truncate">{agendamento.address}</span>
                                                </div>
                                            </div>

                                            {agendamento.notes && (
                                                <div className="rounded-md bg-muted p-3">
                                                    <p className="text-sm">{agendamento.notes}</p>
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex flex-col gap-2 lg:items-end">
                                            <div className="text-lg font-semibold">{formatCurrency(agendamento.price)}</div>
                                            {agendamento.assinatura && <Badge variant="outline">{agendamento.assinatura.plano.name}</Badge>}
                                            <div className="flex flex-wrap gap-2">
                                                <Button asChild size="sm" variant="outline">
                                                    <Link href={`/fisioterapeuta/agenda/${agendamento.id}`}>Ver Detalhes</Link>
                                                </Button>

                                                {/* Botões para agendamentos pendentes */}
                                                {agendamento.status === 'agendado' && (
                                                    <>
                                                        <Button
                                                            size="sm"
                                                            onClick={() => handleAceitar(agendamento)}
                                                            className="bg-green-600 hover:bg-green-700"
                                                        >
                                                            <CheckCircle className="mr-1 h-4 w-4" />
                                                            Aceitar
                                                        </Button>

                                                        <Dialog>
                                                            <DialogTrigger asChild>
                                                                <Button size="sm" variant="destructive">
                                                                    <XCircle className="mr-1 h-4 w-4" />
                                                                    Recusar
                                                                </Button>
                                                            </DialogTrigger>
                                                            <DialogContent>
                                                                <DialogHeader>
                                                                    <DialogTitle>Recusar Agendamento</DialogTitle>
                                                                    <DialogDescription>
                                                                        Por favor, informe o motivo da recusa do agendamento.
                                                                    </DialogDescription>
                                                                </DialogHeader>
                                                                <div className="space-y-4">
                                                                    <div>
                                                                        <Label htmlFor="motivo-recusa">Motivo da recusa</Label>
                                                                        <Textarea
                                                                            id="motivo-recusa"
                                                                            placeholder="Digite o motivo da recusa..."
                                                                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                                                                                const motivo = e.target.value;
                                                                                const button = e.target
                                                                                    .closest('.dialog-content')
                                                                                    ?.querySelector('[data-action="recusar"]') as HTMLButtonElement;
                                                                                if (button) button.dataset.motivo = motivo;
                                                                            }}
                                                                        />
                                                                    </div>
                                                                </div>
                                                                <DialogFooter>
                                                                    <Button
                                                                        variant="destructive"
                                                                        data-action="recusar"
                                                                        onClick={(e) => {
                                                                            const motivo = (e.target as HTMLButtonElement).dataset.motivo || '';
                                                                            if (motivo.trim()) {
                                                                                handleRecusar(agendamento, motivo);
                                                                            }
                                                                        }}
                                                                    >
                                                                        Confirmar Recusa
                                                                    </Button>
                                                                </DialogFooter>
                                                            </DialogContent>
                                                        </Dialog>
                                                    </>
                                                )}

                                                {/* Botão Iniciar para agendamentos confirmados */}
                                                {agendamento.status === 'confirmado' && (
                                                    <Button
                                                        size="sm"
                                                        onClick={() => {
                                                            router.post(
                                                                `/fisioterapeuta/agenda/${agendamento.id}/iniciar`,
                                                                {},
                                                                {
                                                                    preserveScroll: true,
                                                                },
                                                            );
                                                        }}
                                                    >
                                                        Iniciar
                                                    </Button>
                                                )}

                                                {/* Botões para reagendar e cancelar */}
                                                {['agendado', 'confirmado'].includes(agendamento.status) && (
                                                    <>
                                                        <Dialog>
                                                            <DialogTrigger asChild>
                                                                <Button size="sm" variant="outline">
                                                                    <CalendarDays className="mr-1 h-4 w-4" />
                                                                    Reagendar
                                                                </Button>
                                                            </DialogTrigger>
                                                            <DialogContent>
                                                                <DialogHeader>
                                                                    <DialogTitle>Reagendar Consulta</DialogTitle>
                                                                    <DialogDescription>
                                                                        Selecione uma nova data e horário para o agendamento.
                                                                    </DialogDescription>
                                                                </DialogHeader>
                                                                <div className="space-y-4">
                                                                    <div>
                                                                        <Label htmlFor="nova-data">Nova data e horário</Label>
                                                                        <Input
                                                                            id="nova-data"
                                                                            type="datetime-local"
                                                                            value={reagendarData.dataHora}
                                                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                                                setReagendarData((prev) => ({ ...prev, dataHora: e.target.value }));
                                                                            }}
                                                                        />
                                                                    </div>
                                                                    <div>
                                                                        <Label htmlFor="motivo-reagendamento">Motivo (opcional)</Label>
                                                                        <Textarea
                                                                            id="motivo-reagendamento"
                                                                            placeholder="Digite o motivo do reagendamento..."
                                                                            value={reagendarData.motivo}
                                                                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                                                                                setReagendarData((prev) => ({ ...prev, motivo: e.target.value }));
                                                                            }}
                                                                        />
                                                                    </div>
                                                                </div>
                                                                <DialogFooter>
                                                                    <Button
                                                                        onClick={() => {
                                                                            if (reagendarData.dataHora) {
                                                                                handleReagendar(
                                                                                    agendamento,
                                                                                    reagendarData.dataHora,
                                                                                    reagendarData.motivo,
                                                                                );
                                                                                setReagendarData({ dataHora: '', motivo: '' });
                                                                            }
                                                                        }}
                                                                        disabled={!reagendarData.dataHora}
                                                                    >
                                                                        Confirmar Reagendamento
                                                                    </Button>
                                                                </DialogFooter>
                                                            </DialogContent>
                                                        </Dialog>

                                                        <Dialog>
                                                            <DialogTrigger asChild>
                                                                <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                                                                    <Trash2 className="mr-1 h-4 w-4" />
                                                                    Cancelar
                                                                </Button>
                                                            </DialogTrigger>
                                                            <DialogContent>
                                                                <DialogHeader>
                                                                    <DialogTitle>Cancelar Agendamento</DialogTitle>
                                                                    <DialogDescription>
                                                                        Por favor, informe o motivo do cancelamento.
                                                                    </DialogDescription>
                                                                </DialogHeader>
                                                                <div className="space-y-4">
                                                                    <div>
                                                                        <Label htmlFor="motivo-cancelamento">Motivo do cancelamento</Label>
                                                                        <Textarea
                                                                            id="motivo-cancelamento"
                                                                            placeholder="Digite o motivo do cancelamento..."
                                                                            value={cancelarMotivo}
                                                                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                                                                                setCancelarMotivo(e.target.value);
                                                                            }}
                                                                        />
                                                                    </div>
                                                                </div>
                                                                <DialogFooter>
                                                                    <Button
                                                                        variant="destructive"
                                                                        onClick={() => {
                                                                            if (cancelarMotivo.trim()) {
                                                                                handleCancelar(agendamento, cancelarMotivo);
                                                                                setCancelarMotivo('');
                                                                            }
                                                                        }}
                                                                        disabled={!cancelarMotivo.trim()}
                                                                    >
                                                                        Confirmar Cancelamento
                                                                    </Button>
                                                                </DialogFooter>
                                                            </DialogContent>
                                                        </Dialog>
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}

                {/* Paginação */}
                {agendamentos?.links && agendamentos.links.length > 3 && (
                    <div className="flex justify-center">
                        <div className="flex gap-2">
                            {agendamentos.links.map((link: any, index: number) => (
                                <Button key={index} variant={link.active ? 'default' : 'outline'} size="sm" asChild={!!link.url} disabled={!link.url}>
                                    {link.url ? (
                                        <Link href={link.url} dangerouslySetInnerHTML={{ __html: link.label }} />
                                    ) : (
                                        <span dangerouslySetInnerHTML={{ __html: link.label }} />
                                    )}
                                </Button>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
