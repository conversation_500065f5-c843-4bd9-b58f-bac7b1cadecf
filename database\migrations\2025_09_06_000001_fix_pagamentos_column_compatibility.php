<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pagamentos', function (Blueprint $table) {
            // Adicionar colunas de compatibilidade para código legado
            // Estas colunas serão aliases/duplicatas das colunas principais
            
            // Adicionar 'data_pagamento' como alias para 'paid_at'
            if (!Schema::hasColumn('pagamentos', 'data_pagamento')) {
                $table->datetime('data_pagamento')->nullable()->after('paid_at');
            }
            
            // Adicionar índices se não existirem
            if (!Schema::hasIndex('pagamentos', 'pagamentos_data_pagamento_index')) {
                $table->index('data_pagamento', 'pagamentos_data_pagamento_index');
            }
        });
        
        // Sincronizar dados existentes
        $this->syncExistingData();
        
        // Criar triggers para manter sincronização automática
        $this->createSyncTriggers();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remover triggers
        DB::unprepared('DROP TRIGGER IF EXISTS pagamentos_sync_insert');
        DB::unprepared('DROP TRIGGER IF EXISTS pagamentos_sync_update');
        
        Schema::table('pagamentos', function (Blueprint $table) {
            // Remover índices
            if (Schema::hasIndex('pagamentos', 'pagamentos_data_pagamento_index')) {
                $table->dropIndex('pagamentos_data_pagamento_index');
            }
            
            // Remover colunas de compatibilidade
            if (Schema::hasColumn('pagamentos', 'data_pagamento')) {
                $table->dropColumn('data_pagamento');
            }
        });
    }
    
    /**
     * Sincronizar dados existentes
     */
    private function syncExistingData(): void
    {
        // Sincronizar paid_at -> data_pagamento
        DB::statement('UPDATE pagamentos SET data_pagamento = paid_at WHERE paid_at IS NOT NULL');
        
        echo "✅ Dados sincronizados: paid_at -> data_pagamento\n";
    }
    
    /**
     * Criar triggers para manter sincronização automática
     */
    private function createSyncTriggers(): void
    {
        // Trigger para INSERT
        DB::unprepared('
            CREATE TRIGGER pagamentos_sync_insert 
            BEFORE INSERT ON pagamentos 
            FOR EACH ROW 
            BEGIN 
                SET NEW.data_pagamento = NEW.paid_at;
            END
        ');
        
        // Trigger para UPDATE
        DB::unprepared('
            CREATE TRIGGER pagamentos_sync_update 
            BEFORE UPDATE ON pagamentos 
            FOR EACH ROW 
            BEGIN 
                SET NEW.data_pagamento = NEW.paid_at;
            END
        ');
        
        echo "✅ Triggers de sincronização criados\n";
    }
};
