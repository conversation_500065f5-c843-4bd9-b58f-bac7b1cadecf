import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Calendar, Clock, Star, ThumbsDown, ThumbsUp, User } from 'lucide-react';
import { useState } from 'react';

interface Agendamento {
    id: number;
    data_hora: string;
    duracao: number;
    status: string;
    observacoes?: string;
    fisioterapeuta: {
        id: number;
        user: {
            name: string;
            email: string;
        };
        especialidade?: string;
    };
}

interface EvaluateProps {
    agendamento: Agendamento;
}

export default function AgendamentoEvaluate({ agendamento }: EvaluateProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Início', href: '/paciente/dashboard' },
        { title: 'Agendamentos', href: '/paciente/agendamentos' },
        { title: 'Avaliar', href: '' },
    ];
    const [rating, setRating] = useState(0);
    const [hoverRating, setHoverRating] = useState(0);

    const { data, setData, post, processing, errors } = useForm<{
        rating: number;
        comment: string;
        recommend: boolean;
    }>({
        rating: 0,
        comment: '',
        recommend: true,
    });

    const formatDateTime = (dateTime: string) => {
        return new Date(dateTime).toLocaleString('pt-BR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const handleStarClick = (starRating: number) => {
        setRating(starRating);
        setData('rating', starRating);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('paciente.agendamentos.store-evaluation', agendamento.id));
    };

    const getRatingText = (rating: number) => {
        switch (rating) {
            case 1:
                return 'Muito insatisfeito';
            case 2:
                return 'Insatisfeito';
            case 3:
                return 'Neutro';
            case 4:
                return 'Satisfeito';
            case 5:
                return 'Muito satisfeito';
            default:
                return 'Selecione uma avaliação';
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Avaliar Sessão - ${formatDateTime(agendamento.data_hora)}`} />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                <div className="mb-8">
                    <div className="mb-4">
                        <Link href={route('paciente.agendamentos.index')}>
                            <Button variant="ghost" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar aos Agendamentos
                            </Button>
                        </Link>
                    </div>
                    <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">Avaliar Sessão</h1>
                    <p className="text-muted-foreground">Como foi sua experiência com esta sessão?</p>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Informações da Sessão */}
                    <Card className="lg:col-span-1">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Informações da Sessão
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-3">
                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                                    <User className="h-5 w-5 text-blue-600" />
                                </div>
                                <div>
                                    <p className="font-medium">{agendamento.fisioterapeuta.user.name}</p>
                                    {agendamento.fisioterapeuta.especialidade && (
                                        <p className="text-sm text-muted-foreground">{agendamento.fisioterapeuta.especialidade}</p>
                                    )}
                                </div>
                            </div>

                            <div className="space-y-3 border-t pt-4">
                                <div className="flex items-center gap-2 text-sm">
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                    <span>{formatDateTime(agendamento.data_hora)}</span>
                                </div>
                                <div className="flex items-center gap-2 text-sm">
                                    <Clock className="h-4 w-4 text-muted-foreground" />
                                    <span>{agendamento.duracao} minutos</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Formulário de Avaliação */}
                    <Card className="lg:col-span-2">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Star className="h-5 w-5" />
                                Sua Avaliação
                            </CardTitle>
                            <CardDescription>Sua opinião é muito importante para nós e ajuda outros pacientes</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Rating com Estrelas */}
                                <div className="space-y-3">
                                    <Label className="text-base font-medium">Como você avalia esta sessão? *</Label>
                                    <div className="flex items-center gap-2">
                                        {[1, 2, 3, 4, 5].map((star) => (
                                            <button
                                                key={star}
                                                type="button"
                                                className="transition-colors hover:scale-110"
                                                onMouseEnter={() => setHoverRating(star)}
                                                onMouseLeave={() => setHoverRating(0)}
                                                onClick={() => handleStarClick(star)}
                                            >
                                                <Star
                                                    className={`h-8 w-8 ${
                                                        star <= (hoverRating || rating) ? 'fill-current text-yellow-400' : 'text-gray-300'
                                                    }`}
                                                />
                                            </button>
                                        ))}
                                        <span className="ml-3 text-sm text-muted-foreground">{getRatingText(hoverRating || rating)}</span>
                                    </div>
                                    {errors.rating && <p className="text-sm text-red-600">{errors.rating}</p>}
                                </div>

                                {/* Comentário */}
                                <div className="space-y-3">
                                    <Label htmlFor="comment" className="text-base font-medium">
                                        Comentário (opcional)
                                    </Label>
                                    <Textarea
                                        id="comment"
                                        placeholder="Conte-nos mais sobre sua experiência..."
                                        value={data.comment}
                                        onChange={(e) => setData('comment', e.target.value)}
                                        rows={4}
                                        className="resize-none"
                                    />
                                    <p className="text-xs text-muted-foreground">Máximo de 1000 caracteres ({data.comment.length}/1000)</p>
                                    {errors.comment && <p className="text-sm text-red-600">{errors.comment}</p>}
                                </div>

                                {/* Recomendação */}
                                <div className="space-y-3">
                                    <Label className="text-base font-medium">Você recomendaria este fisioterapeuta? *</Label>
                                    <RadioGroup
                                        value={data.recommend.toString()}
                                        onValueChange={(value) => setData('recommend', value === 'true')}
                                        className="flex gap-6"
                                    >
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="true" id="recommend-yes" />
                                            <Label htmlFor="recommend-yes" className="flex cursor-pointer items-center gap-2">
                                                <ThumbsUp className="h-4 w-4 text-green-600" />
                                                Sim, recomendo
                                            </Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="false" id="recommend-no" />
                                            <Label htmlFor="recommend-no" className="flex cursor-pointer items-center gap-2">
                                                <ThumbsDown className="h-4 w-4 text-red-600" />
                                                Não recomendo
                                            </Label>
                                        </div>
                                    </RadioGroup>
                                    {errors.recommend && <p className="text-sm text-red-600">{errors.recommend}</p>}
                                </div>

                                {/* Botões */}
                                <div className="flex gap-4 pt-4">
                                    <Button type="submit" disabled={processing || rating === 0} className="flex-1">
                                        {processing ? 'Enviando...' : 'Enviar Avaliação'}
                                    </Button>
                                    <Link href={route('paciente.agendamentos.index')}>
                                        <Button type="button" variant="outline">
                                            Cancelar
                                        </Button>
                                    </Link>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
