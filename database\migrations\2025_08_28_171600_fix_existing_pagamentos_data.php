<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Atualizar registros existentes para corrigir os dados
        DB::table('pagamentos')->orderBy('id')->chunk(100, function ($pagamentos) {
            foreach ($pagamentos as $pagamento) {
                $updateData = [];
                
                // Se transaction_id não está vazio e payment_id está vazio,
                // provavelmente transaction_id contém o preference_id
                if (!empty($pagamento->transaction_id) && empty($pagamento->payment_id)) {
                    // Mover transaction_id para preference_id
                    $updateData['preference_id'] = $pagamento->transaction_id;
                    
                    // Limpar transaction_id apenas se ele não for um payment_id real
                    // (payment_ids geralmente são numéricos, preference_ids são alfanuméricos)
                    if (!is_numeric($pagamento->transaction_id)) {
                        $updateData['transaction_id'] = null;
                    }
                }
                
                // Se há dados para atualizar
                if (!empty($updateData)) {
                    DB::table('pagamentos')
                        ->where('id', $pagamento->id)
                        ->update($updateData);
                }
            }
        });
        
        Log::info('✅ [MIGRATION] Dados de pagamentos existentes corrigidos', [
            'total_updated' => DB::table('pagamentos')->whereNotNull('preference_id')->count()
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverter as alterações movendo preference_id de volta para transaction_id
        DB::table('pagamentos')->orderBy('id')->chunk(100, function ($pagamentos) {
            foreach ($pagamentos as $pagamento) {
                if (!empty($pagamento->preference_id) && empty($pagamento->payment_id)) {
                    DB::table('pagamentos')
                        ->where('id', $pagamento->id)
                        ->update([
                            'transaction_id' => $pagamento->preference_id,
                            'preference_id' => null
                        ]);
                }
            }
        });
    }
};
