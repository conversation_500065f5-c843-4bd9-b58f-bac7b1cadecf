<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pagamentos', function (Blueprint $table) {
            // Add amount column if it doesn't exist (to replace valor)
            if (!Schema::hasColumn('pagamentos', 'amount')) {
                $table->decimal('amount', 10, 2)->default(0.00)->after('id');
            }

            // Add method column if it doesn't exist
            if (!Schema::hasColumn('pagamentos', 'method')) {
                $table->string('method', 50)->nullable()->after('amount');
            }

            // Add notes column if it doesn't exist
            if (!Schema::hasColumn('pagamentos', 'notes')) {
                $table->text('notes')->nullable()->after('method');
            }

            // Add gateway_response column if it doesn't exist
            if (!Schema::hasColumn('pagamentos', 'gateway_response')) {
                $table->json('gateway_response')->nullable()->after('notes');
            }

            // Add status column if it doesn't exist
            if (!Schema::hasColumn('pagamentos', 'status')) {
                $table->enum('status', ['pendente', 'pago', 'cancelado', 'falhou', 'reembolsado'])->default('pendente')->after('gateway_response');
            }

            // Add data_vencimento column if it doesn't exist
            if (!Schema::hasColumn('pagamentos', 'data_vencimento')) {
                $table->date('data_vencimento')->nullable()->after('status');
            }

            // Add transaction_id column if it doesn't exist
            if (!Schema::hasColumn('pagamentos', 'transaction_id')) {
                $table->string('transaction_id', 255)->nullable()->after('data_vencimento');
            }

            // Add indexes for better performance
            if (!Schema::hasIndex('pagamentos', 'pagamentos_status_index')) {
                $table->index('status', 'pagamentos_status_index');
            }
            
            if (!Schema::hasIndex('pagamentos', 'pagamentos_data_vencimento_index')) {
                $table->index('data_vencimento', 'pagamentos_data_vencimento_index');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pagamentos', function (Blueprint $table) {
            // Drop indexes if they exist
            $table->dropIndex('pagamentos_status_index');
            $table->dropIndex('pagamentos_data_vencimento_index');
            
            // Drop columns if they exist
            if (Schema::hasColumn('pagamentos', 'amount')) {
                $table->dropColumn('amount');
            }
            if (Schema::hasColumn('pagamentos', 'method')) {
                $table->dropColumn('method');
            }
            if (Schema::hasColumn('pagamentos', 'notes')) {
                $table->dropColumn('notes');
            }
            if (Schema::hasColumn('pagamentos', 'gateway_response')) {
                $table->dropColumn('gateway_response');
            }
            if (Schema::hasColumn('pagamentos', 'status')) {
                $table->dropColumn('status');
            }
            if (Schema::hasColumn('pagamentos', 'data_vencimento')) {
                $table->dropColumn('data_vencimento');
            }
            if (Schema::hasColumn('pagamentos', 'transaction_id')) {
                $table->dropColumn('transaction_id');
            }
        });
    }
};
