import InputError from '@/components/input-error';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Head, useForm } from '@inertiajs/react';
import { CheckCircle, Clock, Stethoscope, User } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    phone?: string;
}

interface WorkingHours {
    [key: string]: {
        start: string;
        end: string;
        available: boolean;
    };
}

interface Fisioterapeuta {
    id: number;
    crefito: string;
    specializations: string[];
    bio: string;
    hourly_rate: number;
    session_rate?: number | null;
    travel_fee?: number | null;
    pricing_mode?: 'por_hora' | 'por_sessao';
    service_rates?: {
        avaliacao?: number | null;
        sessao?: number | null;
        teleatendimento?: number | null;
    } | null;
    available_areas: string[];
    working_hours?: WorkingHours;
}

interface Props {
    fisioterapeuta: Fisioterapeuta;
    user: User;
    especialidades: string[];
    areas: string[];
    // Percentual de taxa de serviço vinda do backend (ex.: 20 para 20%)
    serviceFeePercent: number;
}

export default function FisioterapeutaSetup({ fisioterapeuta, user, especialidades, areas, serviceFeePercent }: Props) {
    const [currentStep, setCurrentStep] = useState(1);
    const totalSteps = 5;

    // Default working hours (Monday to Friday, 9am to 6pm)
    const defaultWorkingHours = {
        monday: { start: '09:00', end: '18:00', available: true },
        tuesday: { start: '09:00', end: '18:00', available: true },
        wednesday: { start: '09:00', end: '18:00', available: true },
        thursday: { start: '09:00', end: '18:00', available: true },
        friday: { start: '09:00', end: '18:00', available: true },
        saturday: { start: '09:00', end: '13:00', available: false },
        sunday: { start: '09:00', end: '13:00', available: false },
    };

    const { data, setData, post, processing, errors } = useForm({
        // Dados do usuário
        name: user.name || '',
        phone: user.phone || '',

        // Dados do fisioterapeuta
        crefito: fisioterapeuta.crefito || '',
        specializations: fisioterapeuta.specializations || [],
        bio: fisioterapeuta.bio || '',
        pricing_mode: 'por_hora',
        hourly_rate: fisioterapeuta.hourly_rate || 0,
        session_rate: fisioterapeuta.session_rate ?? undefined,
        travel_fee: fisioterapeuta.travel_fee ?? undefined,
        service_rates: fisioterapeuta.service_rates ?? {},
        available_areas: fisioterapeuta.available_areas || [],
        // Se vier vazio (ex.: []), aplicamos padrão no useEffect abaixo
        working_hours: fisioterapeuta.working_hours || defaultWorkingHours,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Limpar dados desnecessários antes de enviar
        const cleanData = {
            ...data,
            // Garantir que campos opcionais sejam null em vez de undefined
            session_rate: data.session_rate || null,
            travel_fee: data.travel_fee || null,
            service_rates: Object.keys(data.service_rates || {}).length > 0 ? data.service_rates : null,
        };

        // Atualizar o estado do formulário com dados limpos
        Object.keys(cleanData).forEach((key) => {
            setData(key as keyof typeof data, cleanData[key as keyof typeof cleanData]);
        });

        // Aguardar um tick para garantir que o estado foi atualizado
        setTimeout(() => {
            post(route('fisioterapeuta.setup.store'), {
                onSuccess: () => {
                    console.log('Setup completed successfully');
                },
                onError: (errors: Record<string, string[]>) => {
                    console.error('Setup validation errors:', errors);
                    // Scroll to top to show errors
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                },
                preserveState: true,
            });
        }, 0);
    };

    const to2 = (v: number) => Number(Number(v || 0).toFixed(2));
    // Currency helpers (pt-BR)
    const nf = useMemo(() => new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }), []);
    const formatBRL = (val?: number) => (typeof val === 'number' && !isNaN(val) ? nf.format(val) : '');
    // Taxa de serviço (percentual -> multiplicador)
    const serviceFeeMultiplier = useMemo(() => 1 + Number(serviceFeePercent || 0) / 100, [serviceFeePercent]);
    const onlyDigits = (s: string) => s.match(/\d+/g)?.join('') ?? '';

    // Display states for masked inputs
    const [hourlyDisplay, setHourlyDisplay] = useState<string>(formatBRL(data.hourly_rate));
    // Removidos: campos opcionais (valor por sessão, taxas de serviços e deslocamento)

    const handleCurrencyChange = (raw: string, setDisplay: (s: string) => void, setNumeric: (n?: number) => void, allowEmpty = false) => {
        if (allowEmpty && raw.trim() === '') {
            setDisplay('');
            setNumeric(undefined);
            return;
        }
        const digits = onlyDigits(raw);
        if (digits.length === 0) {
            setDisplay('');
            setNumeric(allowEmpty ? undefined : 0);
            return;
        }
        const cents = parseInt(digits, 10);
        const value = cents / 100;
        setDisplay(nf.format(value));
        setNumeric(value);
    };

    const handleSpecializationChange = (especialidade: string, checked: boolean) => {
        if (checked) {
            setData('specializations', [...data.specializations, especialidade]);
        } else {
            setData(
                'specializations',
                data.specializations.filter((s) => s !== especialidade),
            );
        }
    };

    // Keep numeric hourly_rate in sync with masked display, to avoid validation glitches
    useEffect(() => {
        const digits = onlyDigits(hourlyDisplay || '');
        if (digits.length > 0) {
            const value = parseInt(digits, 10) / 100;
            if (typeof data.hourly_rate !== 'number' || isNaN(data.hourly_rate) || Number(data.hourly_rate.toFixed(2)) !== Number(value.toFixed(2))) {
                setData('hourly_rate', value);
            }
        }
    }, [hourlyDisplay]);

    const handleAreaChange = (area: string, checked: boolean) => {
        if (checked) {
            setData('available_areas', [...data.available_areas, area]);
        } else {
            setData(
                'available_areas',
                data.available_areas.filter((a) => a !== area),
            );
        }
    };

    // Verificação se objeto de horários é válido (tem chaves) e contém estrutura esperada
    const isValidWorkingHours = (wh: unknown): wh is WorkingHours => {
        if (!wh || typeof wh !== 'object') return false;
        const keys = Object.keys(wh);
        if (keys.length === 0) return false;
        return keys.every((k) => typeof (wh as Record<string, unknown>)[k]?.start === 'string' && typeof (wh as Record<string, unknown>)[k]?.end === 'string' && typeof (wh as Record<string, unknown>)[k]?.available === 'boolean');
    };

    // Garantir que se vier [] do backend, substituímos pelo padrão
    useEffect(() => {
        const wh: unknown = data.working_hours;
        const isArrayEmpty = Array.isArray(wh) && wh.length === 0;
        const isEmptyObject = !!wh && !Array.isArray(wh) && Object.keys(wh).length === 0;
        if (!isValidWorkingHours(wh) || isArrayEmpty || isEmptyObject) {
            setData('working_hours', defaultWorkingHours);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const getStepValidation = (step: number): boolean => {
        switch (step) {
            case 1:
                return data.name.length > 0 && data.phone.length > 0;
            case 2:
                return data.crefito.length > 0 && data.hourly_rate >= 10;
            case 3:
                return data.specializations.length > 0;
            case 4: {
                // Verifica ao menos um dia disponível e horários válidos (start < end)
                const workingHours = isValidWorkingHours(data.working_hours) ? data.working_hours : defaultWorkingHours;
                const entries = Object.values(workingHours);
                const hasAvailableDay = entries.some((day) => day.available);
                const timesValid = entries
                    .filter((d) => d.available)
                    .every((d) => {
                        // Comparar HH:MM
                        return (d.start ?? '') < (d.end ?? '');
                    });
                return hasAvailableDay && timesValid;
            }
            case 5: {
                const hasAreas = data.available_areas.length > 0;
                const hasBio = data.bio.length >= 20;
                return hasAreas && hasBio;
            }
            default:
                return false;
        }
    };

    const canProceedToNext = getStepValidation(currentStep);
    // Calculate progress based on completed steps
    let completedSteps = 0;
    for (let i = 1; i <= totalSteps; i++) {
        if (i < currentStep) {
            completedSteps++;
        } else if (i === currentStep && getStepValidation(i)) {
            completedSteps++;
        }
    }
    const progress = (completedSteps / totalSteps) * 100;

    const nextStep = () => {
        if (canProceedToNext && currentStep < totalSteps) {
            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };

    return (
        <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
            <Head title="Configuração do Perfil Profissional" />

            <div className="w-full max-w-2xl">
                {/* Header */}
                <div className="mb-8 text-center">
                    <div className="mb-4 flex justify-center">
                        <Badge size="size-12" variant="default" className="shadow-md">
                            <Stethoscope />
                        </Badge>
                    </div>
                    <h1 className="text-3xl font-bold text-gray-900">Bem-vindo à F4 Fisio!</h1>
                    <p className="mt-2 text-gray-600">Vamos configurar seu perfil profissional para que você possa começar a receber agendamentos</p>
                </div>

                {/* Progress */}
                <div className="mb-8">
                    <div className="mb-2 flex justify-between text-sm text-gray-600">
                        <span>
                            Passo {currentStep} de {totalSteps}
                        </span>
                        <span>{Math.round(progress)}% concluído</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                </div>

                {/* Form */}
                <Card>
                    <form onSubmit={handleSubmit}>
                        {/* Step 1: Dados Pessoais */}
                        {currentStep === 1 && (
                            <>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Badge size="icon-lg" variant="default" className="shadow-sm">
                                            <User />
                                        </Badge>
                                        Dados Pessoais
                                    </CardTitle>
                                    <CardDescription>Confirme suas informações básicas de contato</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Nome Completo *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Seu nome completo"
                                        />
                                        <InputError message={errors.name} />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="phone">Telefone *</Label>
                                        <Input
                                            id="phone"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                            placeholder="(11) 99999-9999"
                                        />
                                        <InputError message={errors.phone} />
                                    </div>
                                </CardContent>
                            </>
                        )}

                        {/* Step 2: Dados Profissionais */}
                        {currentStep === 2 && (
                            <>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Badge size="icon-lg" variant="default" className="shadow-sm">
                                            <CheckCircle />
                                        </Badge>
                                        Dados Profissionais
                                    </CardTitle>
                                    <CardDescription>Informações sobre sua formação e registro profissional</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="crefito">Número do CREFITO *</Label>
                                        <Input
                                            id="crefito"
                                            value={data.crefito}
                                            onChange={(e) => setData('crefito', e.target.value)}
                                            placeholder="CREFITO-3/123456-F"
                                        />
                                        <InputError message={errors.crefito} />
                                        <p className="text-sm text-muted-foreground">Exemplo: CREFITO-3/123456-F</p>
                                    </div>

                                    {/* Cobrança fixa por hora - seletor removido */}

                                    <div className="space-y-2">
                                        <Label htmlFor="hourly_rate">Valor da Hora (R$) *</Label>
                                        <Input
                                            id="hourly_rate"
                                            type="text"
                                            inputMode="numeric"
                                            value={hourlyDisplay}
                                            onChange={(e) =>
                                                handleCurrencyChange(e.target.value, setHourlyDisplay, (n) => setData('hourly_rate', n ?? 0))
                                            }
                                            onBlur={() => setHourlyDisplay(formatBRL(data.hourly_rate))}
                                            placeholder="R$ 80,00"
                                        />
                                        <InputError message={errors.hourly_rate} />
                                        <p className={`text-sm ${data.hourly_rate < 10 ? 'text-destructive' : 'text-muted-foreground'}`}>
                                            {data.hourly_rate < 10 ? 'Mínimo permitido: R$ 10,00' : 'Valor mínimo: R$ 10,00'}
                                        </p>
                                        {/* Exibição da taxa e valor final ao paciente */}
                                        {typeof data.hourly_rate === 'number' && data.hourly_rate > 0 && (
                                            <div className="space-y-0.5 text-sm text-gray-700">
                                                <p>
                                                    <span className="font-medium">Taxa de serviço:</span> {serviceFeePercent}%
                                                </p>
                                                <p>
                                                    <span className="font-medium">Total cobrado do paciente:</span>{' '}
                                                    {formatBRL(to2(data.hourly_rate * serviceFeeMultiplier))}
                                                    <span className="text-muted-foreground"> (você recebe {formatBRL(to2(data.hourly_rate))})</span>
                                                </p>
                                            </div>
                                        )}
                                    </div>

                                    {/* Campos opcionais removidos por enquanto */}
                                </CardContent>
                            </>
                        )}

                        {/* Step 3: Especialidades */}
                        {currentStep === 3 && (
                            <>
                                <CardHeader className="space-y-2">
                                    <CardTitle className="flex items-center gap-2">
                                        <Badge size="icon-lg" variant="default" className="shadow-sm">
                                            <Stethoscope />
                                        </Badge>
                                        Especialidades
                                    </CardTitle>
                                    <CardDescription>Selecione suas áreas de especialização (pelo menos uma)</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 gap-x-8 gap-y-4 sm:grid-cols-2">
                                        {especialidades.map((especialidade) => (
                                            <div key={especialidade} className="flex items-center space-x-2 py-0.5">
                                                <Checkbox
                                                    id={`especialidade-${especialidade}`}
                                                    checked={data.specializations.includes(especialidade)}
                                                    onCheckedChange={(checked) => handleSpecializationChange(especialidade, checked as boolean)}
                                                />
                                                <Label htmlFor={`especialidade-${especialidade}`} className="text-sm font-normal">
                                                    {especialidade}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                    <InputError message={errors.specializations} />
                                    {data.specializations.length > 0 && (
                                        <p className="mt-2 text-sm text-green-600">✓ {data.specializations.length} especialidade(s) selecionada(s)</p>
                                    )}
                                </CardContent>
                            </>
                        )}

                        {/* Step 4: Horários de Trabalho */}
                        {currentStep === 4 && (
                            <>
                                <CardHeader className="space-y-2">
                                    <CardTitle className="flex items-center gap-2">
                                        <Badge size="icon-lg" variant="default" className="shadow-sm">
                                            <Clock />
                                        </Badge>
                                        Horários de Trabalho
                                    </CardTitle>
                                    <CardDescription>Defina seus horários de atendimento</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-4">
                                        {Object.entries(data.working_hours || defaultWorkingHours).map(([day, hours]) => {
                                            const dayName =
                                                {
                                                    monday: 'Segunda-feira',
                                                    tuesday: 'Terça-feira',
                                                    wednesday: 'Quarta-feira',
                                                    thursday: 'Quinta-feira',
                                                    friday: 'Sexta-feira',
                                                    saturday: 'Sábado',
                                                    sunday: 'Domingo',
                                                }[day] || day;

                                            const typedHours = hours as { start: string; end: string; available: boolean };
                                            const typedWorkingHours = data.working_hours as Record<
                                                string,
                                                { start: string; end: string; available: boolean }
                                            >;

                                            return (
                                                <div key={day} className="flex items-center space-x-4">
                                                    <div className="flex w-32 items-center space-x-2">
                                                        <Checkbox
                                                            id={`${day}-available`}
                                                            checked={typedHours.available}
                                                            onCheckedChange={(checked) => {
                                                                const newWorkingHours = { ...typedWorkingHours };
                                                                newWorkingHours[day] = {
                                                                    ...newWorkingHours[day],
                                                                    available: checked as boolean,
                                                                };
                                                                setData('working_hours', newWorkingHours);
                                                            }}
                                                        />
                                                        <Label htmlFor={`${day}-available`} className="font-normal">
                                                            {dayName}
                                                        </Label>
                                                    </div>

                                                    <div className="flex items-center space-x-2">
                                                        <Input
                                                            type="time"
                                                            value={typedHours.start}
                                                            onChange={(e) => {
                                                                const newWorkingHours = { ...typedWorkingHours };
                                                                newWorkingHours[day] = {
                                                                    ...newWorkingHours[day],
                                                                    start: e.target.value,
                                                                };
                                                                setData('working_hours', newWorkingHours);
                                                            }}
                                                            disabled={!typedHours.available}
                                                            className="w-28"
                                                        />
                                                        <span>às</span>
                                                        <Input
                                                            type="time"
                                                            value={typedHours.end}
                                                            onChange={(e) => {
                                                                const newWorkingHours = { ...typedWorkingHours };
                                                                newWorkingHours[day] = {
                                                                    ...newWorkingHours[day],
                                                                    end: e.target.value,
                                                                };
                                                                setData('working_hours', newWorkingHours);
                                                            }}
                                                            disabled={!typedHours.available}
                                                            className="w-28"
                                                        />
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </CardContent>
                            </>
                        )}

                        {/* Step 5: Áreas e Bio */}
                        {currentStep === 5 && (
                            <>
                                <CardHeader className="space-y-2">
                                    <CardTitle className="flex items-center gap-2">
                                        <Badge size="icon-lg" variant="default" className="shadow-sm">
                                            <CheckCircle />
                                        </Badge>
                                        Finalização
                                    </CardTitle>
                                    <CardDescription>Áreas de atendimento e biografia profissional</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div>
                                        <Label className="mb-1 block text-base font-medium">Áreas de Atendimento *</Label>
                                        <p className="mb-3 text-sm text-muted-foreground">Selecione as regiões onde você atende</p>
                                        <div className="grid max-h-48 grid-cols-1 gap-x-8 gap-y-4 overflow-y-auto rounded-md border p-3 sm:grid-cols-2">
                                            {areas.map((area) => (
                                                <div key={area} className="flex items-center space-x-2 py-0.5">
                                                    <Checkbox
                                                        id={`area-${area}`}
                                                        checked={data.available_areas.includes(area)}
                                                        onCheckedChange={(checked) => handleAreaChange(area, checked as boolean)}
                                                    />
                                                    <Label htmlFor={`area-${area}`} className="text-sm font-normal">
                                                        {area}
                                                    </Label>
                                                </div>
                                            ))}
                                        </div>
                                        <InputError message={errors.available_areas} />
                                        {data.available_areas.length > 0 && (
                                            <p className="mt-2 text-sm text-green-600">✓ {data.available_areas.length} área(s) selecionada(s)</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="bio">Biografia Profissional *</Label>
                                        <Textarea
                                            id="bio"
                                            value={data.bio}
                                            onChange={(e) => setData('bio', e.target.value)}
                                            placeholder="Conte um pouco sobre sua experiência, formação e especialidades. Esta informação será exibida para os pacientes."
                                            rows={4}
                                        />
                                        <InputError message={errors.bio} />
                                        <p className={`text-sm ${data.bio.length >= 20 ? 'text-green-600' : 'text-muted-foreground'}`}>
                                            {data.bio.length >= 20 ? '✓' : ''} {data.bio.length}/1000 caracteres (mínimo 20)
                                        </p>
                                    </div>
                                </CardContent>
                            </>
                        )}

                        {/* Navigation */}
                        <div className="flex justify-between border-t p-6">
                            <Button type="button" variant="outline" onClick={prevStep} disabled={currentStep === 1}>
                                Anterior
                            </Button>

                            {currentStep < totalSteps ? (
                                <Button type="button" onClick={nextStep} disabled={!canProceedToNext}>
                                    Próximo
                                </Button>
                            ) : (
                                <Button type="submit" disabled={processing || !canProceedToNext} className="gap-2">
                                    {processing ? 'Finalizando...' : 'Finalizar Configuração'}
                                </Button>
                            )}
                        </div>
                    </form>
                </Card>
            </div>
        </div>
    );
}
