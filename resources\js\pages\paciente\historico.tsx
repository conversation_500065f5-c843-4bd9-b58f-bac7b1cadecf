import { Badge } from '@/components/ui/badge';
import PatientCard from '@/components/patient-card';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Activity, Calendar, Clock, Download, FileText, Filter, Heart, Home, TrendingUp, User, Search } from 'lucide-react';
import { useState } from 'react';

interface Props {
    agendamentos: {
        data: Array<{
            id: number;
            data_agendamento: string;
            horario: string;
            status: string;
            tipo: string;
            fisioterapeuta: {
                user: {
                    name: string;
                };
            };
            relatorio_sessao?: {
                observacoes: string;
                exercicios_realizados: string;
            };
        }>;
        links: any;
        meta: any;
    };
    stats: {
        totalSessoes: number;
        sessoesUltimoMes: number;
        avaliacoesRealizadas: number;
        tempoTratamento: number;
    };
    evolucaoMensal: Array<{
        mes: string;
        sessoes: number;
    }>;
    avaliacoes: Array<{
        id: number;
        created_at: string;
        observacoes: string;
        fisioterapeuta: {
            user: {
                name: string;
            };
        };
    }>;
    prescricoes: Array<{
        id: number;
        titulo: string;
        descricao: string;
        medicamentos?: string;
        exercicios?: string;
        cuidados_especiais?: string;
        data_inicio: string;
        data_fim?: string;
        status: string;
        created_at: string;
        fisioterapeuta: {
            name: string;
        };
    }>;
    orientacoes: Array<{
        id: number;
        titulo: string;
        descricao: string;
        exercicios_recomendados?: string;
        cuidados_posturais?: string;
        atividades_evitar?: string;
        dicas_gerais?: string;
        frequencia_dias?: number;
        horario_recomendado?: string;
        prioridade: string;
        created_at: string;
        fisioterapeuta: {
            name: string;
        };
    }>;
    filtros: {
        periodo: string;
        tipo: string;
        status: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/paciente/dashboard' },
    { title: 'Histórico', href: '/paciente/historico' },
];

export default function PacienteHistorico() {
    const pageProps = usePage().props as any;
    const { agendamentos, stats, evolucaoMensal, avaliacoes, prescricoes, orientacoes, filtros } = pageProps;
    const [filtrosPendentes, setFiltrosPendentes] = useState(filtros);
    const [search, setSearch] = useState<string>('');
    const [status, setStatus] = useState<string>(filtros?.status ?? 'todos');
    const [tipo, setTipo] = useState<string>(filtros?.tipo ?? 'todos');
    const [periodo, setPeriodo] = useState<string>(filtros?.periodo ?? 'todos');

    const aplicarFiltros = () => {
        router.get(
            route('paciente.historico.index'),
            { periodo, tipo, status, search },
            { preserveState: true, preserveScroll: true, replace: true },
        );
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        aplicarFiltros();
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            agendado: 'default',
            concluido: 'default',
            cancelado: 'destructive',
            em_andamento: 'secondary',
        } as const;

        const labels = {
            agendado: 'Agendado',
            concluido: 'Concluído',
            cancelado: 'Cancelado',
            em_andamento: 'Em Andamento',
        };

        return <Badge variant={variants[status as keyof typeof variants] || 'default'}>{labels[status as keyof typeof labels] || status}</Badge>;
    };

    const formatDate = (dateString: string | null | undefined) => {
        if (!dateString) return 'Data não informada';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'Data inválida';
            return date.toLocaleDateString('pt-BR');
        } catch {
            return 'Data inválida';
        }
    };

    const formatTime = (timeString: string | null | undefined) => {
        return timeString ? timeString.substring(0, 5) : '--:--';
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Histórico de Tratamento" />
            <div className="min-h-screen bg-background">
                {/* Header */}
                <section className="bg-gradient-to-b from-background to-muted/30 py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 className="text-3xl font-bold">Histórico de Tratamento</h1>
                                <p className="text-muted-foreground">Acompanhe sua evolução e histórico de sessões</p>
                            </div>
                            <Button
                                variant="outline"
                                onClick={() => window.open(route('paciente.historico.export'), '_blank')}
                                className="w-full sm:w-auto"
                            >
                                <Download className="mr-2 h-4 w-4" />
                                <span className="hidden sm:inline">Exportar Histórico</span>
                                <span className="sm:hidden">Exportar</span>
                            </Button>
                        </div>
                    </div>
                </section>

                {/* Stats Cards */}
                <section className="bg-muted/30 py-12 sm:-mx-6 lg:-mx-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid grid-cols-2 gap-4 sm:gap-6 lg:gap-8 lg:grid-cols-4">
                            {/* Total de Sessões */}
                            <PatientCard
                                icon={
                                    <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                        <Activity className="h-5 w-5" />
                                    </Badge>
                                }
                                title="Total de Sessões"
                                subtitle="Desde o início"
                                value={stats.totalSessoes}
                                helperText="Sessões realizadas"
                            />

                            {/* Último Mês */}
                            <PatientCard
                                icon={
                                    <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                        <Calendar className="h-5 w-5" />
                                    </Badge>
                                }
                                title="Último Mês"
                                subtitle="Sessões realizadas"
                                value={stats.sessoesUltimoMes}
                                helperText="No último mês"
                            />

                            {/* Avaliações */}
                            <PatientCard
                                icon={
                                    <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                        <FileText className="h-5 w-5" />
                                    </Badge>
                                }
                                title="Avaliações"
                                subtitle="Realizadas"
                                value={stats.avaliacoesRealizadas}
                                helperText="Avaliações completas"
                            />

                            {/* Tempo de Tratamento */}
                            <PatientCard
                                icon={
                                    <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                        <TrendingUp className="h-5 w-5" />
                                    </Badge>
                                }
                                title="Tempo de Tratamento"
                                subtitle="Duração"
                                value={stats.tempoTratamento}
                                helperText="meses de tratamento"
                            />
                        </div>
                    </div>
                </section>

                {/* Busca (estilo Agendamentos) */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <Card>
                            <CardContent className="p-6">
                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
                                        <div className="flex-1">
                                            <label htmlFor="search" className="mb-2 block text-sm font-medium text-gray-700">
                                                Buscar Histórico
                                            </label>
                                            <Input
                                                id="search"
                                                type="text"
                                                placeholder="Ex.: nome do fisioterapeuta, observações..."
                                                value={search}
                                                onChange={(e) => setSearch(e.target.value)}
                                            />
                                        </div>

                                        <div className="w-full sm:w-48">
                                            <label htmlFor="status" className="mb-2 block text-sm font-medium text-gray-700">
                                                Status
                                            </label>
                                            <Select value={status} onValueChange={setStatus}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Todos os status" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="todos">Todos os status</SelectItem>
                                                    <SelectItem value="concluido">Concluído</SelectItem>
                                                    <SelectItem value="cancelado">Cancelado</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="w-full sm:w-56">
                                            <label htmlFor="tipo" className="mb-2 block text-sm font-medium text-gray-700">
                                                Tipo
                                            </label>
                                            <Select value={tipo} onValueChange={setTipo}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Todos os tipos" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="todos">Todos</SelectItem>
                                                    <SelectItem value="sessoes">Sessões</SelectItem>
                                                    <SelectItem value="avaliacoes">Avaliações</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="w-full sm:w-56">
                                            <label htmlFor="periodo" className="mb-2 block text-sm font-medium text-gray-700">
                                                Período
                                            </label>
                                            <Select value={periodo} onValueChange={setPeriodo}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Todos os períodos" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="1_mes">Último mês</SelectItem>
                                                    <SelectItem value="3_meses">Últimos 3 meses</SelectItem>
                                                    <SelectItem value="6_meses">Últimos 6 meses</SelectItem>
                                                    <SelectItem value="1_ano">Último ano</SelectItem>
                                                    <SelectItem value="todos">Todos os períodos</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <Button type="submit" variant="default" className="w-full sm:w-auto bg-green-500 hover:bg-green-600 text-black">
                                            <Search className="mr-2 h-4 w-4" />
                                            Pesquisar
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Lista de Agendamentos */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader>
                                <CardTitle>Histórico de Sessões</CardTitle>
                            </CardHeader>
                            <CardContent>
                                {agendamentos.data.length > 0 ? (
                                    <>
                                        {/* Tabela para md+ */}
                                        <div className="hidden overflow-x-auto md:block">
                                            <Table>
                                                <TableHeader>
                                                    <TableRow>
                                                        <TableHead>Data/Hora</TableHead>
                                                        <TableHead>Fisioterapeuta</TableHead>
                                                        <TableHead>Tipo</TableHead>
                                                        <TableHead>Status</TableHead>
                                                        <TableHead className="text-right">Ações</TableHead>
                                                    </TableRow>
                                                </TableHeader>
                                                <TableBody>
                                                    {agendamentos.data.map((agendamento: any) => (
                                                        <TableRow key={agendamento.id}>
                                                            <TableCell>
                                                                <div className="flex flex-col">
                                                                    <div className="flex items-center gap-2">
                                                                        <Calendar className="h-4 w-4 text-muted-foreground" />
                                                                        <span className="font-medium">{formatDate(agendamento.data_agendamento)}</span>
                                                                    </div>
                                                                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                                                        <Clock className="h-4 w-4" />
                                                                        <span>{formatTime(agendamento.horario)}</span>
                                                                    </div>
                                                                </div>
                                                            </TableCell>
                                                            <TableCell>
                                                                <div className="flex items-center gap-2">
                                                                    <User className="h-4 w-4 text-muted-foreground" />
                                                                    <span className="font-medium">{agendamento.fisioterapeuta.user.name}</span>
                                                                </div>
                                                            </TableCell>
                                                            <TableCell>
                                                                <span className="text-sm">
                                                                    {agendamento.tipo === 'sessao' ? 'Sessão de Fisioterapia' : 'Avaliação'}
                                                                </span>
                                                            </TableCell>
                                                            <TableCell>{getStatusBadge(agendamento.status)}</TableCell>
                                                            <TableCell className="text-right">
                                                                {agendamento.status === 'concluido' && (
                                                                    <Link href={route('paciente.historico.show', agendamento.id)}>
                                                                        <Button variant="outline" size="sm">
                                                                            <FileText className="mr-2 h-4 w-4" />
                                                                            Ver Detalhes
                                                                        </Button>
                                                                    </Link>
                                                                )}
                                                            </TableCell>
                                                        </TableRow>
                                                    ))}
                                                </TableBody>
                                            </Table>
                                        </div>

                                        {/* Cartões no mobile */}
                                        <div className="space-y-3 md:hidden">
                                            {agendamentos.data.map((agendamento: any) => (
                                                <div key={agendamento.id} className="rounded-lg border bg-card p-4 shadow-sm">
                                                    <div className="mb-2 flex items-center justify-between gap-2">
                                                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                                            <Calendar className="h-4 w-4" />
                                                            <span>{formatDate(agendamento.data_agendamento)}</span>
                                                            <span className="mx-1">•</span>
                                                            <Clock className="h-4 w-4" />
                                                            <span>{formatTime(agendamento.horario)}</span>
                                                        </div>
                                                        <div>{getStatusBadge(agendamento.status)}</div>
                                                    </div>
                                                    <div className="mb-1 flex items-center gap-2">
                                                        <User className="h-4 w-4 text-muted-foreground" />
                                                        <span className="font-medium">{agendamento.fisioterapeuta.user.name}</span>
                                                    </div>
                                                    <div className="text-sm text-muted-foreground">
                                                        {agendamento.tipo === 'sessao' ? 'Sessão de Fisioterapia' : 'Avaliação'}
                                                    </div>
                                                    {agendamento.status === 'concluido' && (
                                                        <div className="mt-3">
                                                            <Link href={route('paciente.historico.show', agendamento.id)}>
                                                                <Button variant="outline" size="sm" className="w-full">
                                                                    <FileText className="mr-2 h-4 w-4" />
                                                                    Ver Detalhes
                                                                </Button>
                                                            </Link>
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    </>
                                ) : (
                                    <div className="py-8 text-center">
                                        <p className="text-muted-foreground">Nenhum agendamento encontrado</p>
                                    </div>
                                )}

                                {/* Paginação */}
                                {agendamentos.meta && agendamentos.meta.last_page > 1 && (
                                    <div className="mt-6 flex justify-center">
                                        <div className="flex flex-wrap items-center justify-center gap-2">
                                            {agendamentos.links.map((link: any, index: number) => (
                                                <Button
                                                    key={index}
                                                    variant={link.active ? 'default' : 'outline'}
                                                    size="sm"
                                                    className="h-9 px-3"
                                                    onClick={() => link.url && router.get(link.url)}
                                                    disabled={!link.url}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </section>
            </div>
        </AppLayout>
    );
}
