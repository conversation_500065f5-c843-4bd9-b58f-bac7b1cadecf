<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CreateTestUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-test-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cria um usuário de teste (paciente) e o marca como verificado.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = '<EMAIL>';
        $name = 'Paciente Teste';
        $passwordPlain = 'Password123';

        $user = User::updateOrCreate(
            ['email' => $email],
            [
                'name' => $name,
                'password' => $passwordPlain,
                'role' => 'paciente',
                'active' => true,
                'email_verified_at' => now(),
                'onboarding_completed' => false,
                'plan_selected' => false,
                'checkout_completed' => false,
                'has_subscription' => false,
            ]
        );

        $this->info('Usuário de teste criado/atualizado com sucesso!');
        $this->line("Email: {$email}");
        $this->line("Senha: {$passwordPlain}");
        $this->line('Acesse http://127.0.0.1:8000/register para criar um novo usuário, ou http://127.0.0.1:8000/login para entrar com o usuário de teste.');

        return 0;
    }
}
