<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Mail\Message;

class TestEmailCommand extends Command
{
    protected $signature = 'test:email {email=<EMAIL>}';
    protected $description = 'Test email sending functionality';

    public function handle()
    {
        try {
            $email = $this->argument('email');
            
            $this->info('=== Teste Laravel + Resend ===');
            $this->info('Email de destino: ' . $email);
            
            // Verificar configurações
            $this->info('MAIL_MAILER: ' . config('mail.default'));
            $this->info('RESEND_API_KEY: ' . (config('services.resend.key') ? 'Configurado' : 'Não configurado'));
            $this->info('MAIL_FROM: ' . config('mail.from.address'));
            $this->newLine();
            
            // Enviar email de teste
            Mail::raw('🎉 Teste de verificação de email do Laravel F4 Fisio - ' . now()->format('d/m/Y H:i:s'), function (Message $message) use ($email) {
                $message->to($email)
                        ->subject('Teste Laravel Email Verification - F4 Fisio')
                        ->from('<EMAIL>', 'F4 Fisio');
            });
            
            $this->info('✅ Email enviado com sucesso!');
            $this->info('Verifique sua caixa de entrada em: ' . $email);
            
        } catch (\Exception $e) {
            $this->error('❌ Erro ao enviar email: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
        }
    }
}
