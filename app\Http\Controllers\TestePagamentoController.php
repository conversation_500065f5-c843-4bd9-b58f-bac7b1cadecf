<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Pagamento;
use App\Models\User;
use App\Models\Plano;
use App\Services\UnifiedPaymentService;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class TestePagamentoController extends Controller
{
    /**
     * Exibe a página de teste de pagamento
     */
    public function index()
    {
        // Buscar pagamentos de teste recentes (últimos 7 dias)
        $pagamentosRecentes = Pagamento::whereNull('assinatura_id')
            ->whereNull('agendamento_id')
            ->where('notes', 'like', '%Pagamento de Teste%')
            ->where('created_at', '>=', now()->subDays(7))
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($pagamento) {
                return [
                    'id' => $pagamento->id,
                    'transaction_amount' => $pagamento->amount,
                    'description' => $pagamento->notes,
                    'payment_method_id' => $pagamento->method,
                    'payment_type_id' => $this->getPaymentTypeId($pagamento->method),
                    'status' => $this->getStatusMercadoPago($pagamento->status),
                    'status_detail' => 'pending',
                    'date_created' => $pagamento->created_at->toISOString(),
                    'date_approved' => $pagamento->paid_at?->toISOString(),
                    'init_point' => null, // Será preenchido quando verificar
                    'external_reference' => (string) $pagamento->id,
                    'user_name' => $pagamento->user->name ?? 'Usuário Teste',
                    'created_at_formatted' => $pagamento->created_at->format('d/m/Y H:i'),
                ];
            });

        return Inertia::render('teste-pagamento', [
            'pagamentosRecentes' => $pagamentosRecentes
        ]);
    }

    /**
     * Cria um pagamento de teste
     */
    public function criarPagamento(Request $request)
    {
        try {
            DB::beginTransaction();

            // Usa valores padrão para teste
            $valor = $request->input('valor', 29.90);
            $descricao = $request->input('descricao', 'Pagamento de Teste - F4 Fisio');

            // Busca um usuário de teste (paciente)
            $user = User::where('role', 'paciente')->first() ?? User::first();
            
            if (!$user) {
                return response()->json([
                    'error' => 'Nenhum usuário encontrado para teste'
                ], 404);
            }

            // Cria o registro de pagamento no banco usando a mesma estrutura do sistema de agendamento
            $pagamento = Pagamento::create([
                'assinatura_id' => null,
                'agendamento_id' => null,
                'user_id' => $user->id,
                'amount' => $valor,
                'status' => 'pendente',
                'method' => null,
                'transaction_id' => null,
                'due_date' => now()->toDateString(),
                'notes' => $descricao,
            ]);

            Log::info('[TestePagamentoController] Pagamento criado', ['pagamento_id' => $pagamento->id]);

            /** @var UnifiedPaymentService $paymentService */
            $paymentService = app(UnifiedPaymentService::class);

            // Verificar se APP_URL não é localhost para configurar URLs de redirecionamento
            $appUrl = env('APP_URL', 'http://localhost');
            $successUrl = route('paciente.pagamentos.success');
            $redirectUrl = null;
            
            if (!str_contains($appUrl, 'localhost') && !str_contains($appUrl, '127.0.0.1')) {
                // Configurar URL de sucesso para o checkout com o preference_id
                $successUrl = url('/checkout/' . $pagamento->id);
                $redirectUrl = $successUrl;
                
                Log::info('[TestePagamentoController] URLs de redirecionamento configuradas', [
                    'app_url' => $appUrl,
                    'success_url' => $successUrl,
                    'redirect_url' => $redirectUrl
                ]);
            }

            // Criar preferência MP usando o UnifiedPaymentService
            $preference = $paymentService->createUnifiedPayment([
                'title' => 'Pagamento de Teste - F4 Fisio',
                'description' => $descricao,
                'amount' => (float) $valor,
                'payer' => [
                    'name' => $user->name,
                    'email' => $user->email,
                ],
                'payment_methods' => 'all',
                'success_url' => $successUrl,
                'failure_url' => route('paciente.pagamentos.failure'),
                'pending_url' => route('paciente.pagamentos.pending'),
                'external_reference' => (string) $pagamento->id,
                'notification_url' => route('mercadopago.webhook'),
            ]);

            Log::info('[TestePagamentoController] Preferência Mercado Pago criada', [
                'success' => $preference['success'] ?? null,
                'preference_id' => $preference['preference_id'] ?? null,
                'message' => $preference['message'] ?? null,
            ]);

            if (!($preference['success'] ?? false)) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => $preference['message'] ?? 'Não foi possível iniciar o pagamento. Tente novamente.'
                ], 500);
            }

            // Persistir preference_id corretamente e manter transaction_id para compatibilidade
            $pagamento->update([
                'preference_id' => $preference['preference_id'] ?? null,
                'transaction_id' => $preference['preference_id'] ?? null, // Manter para compatibilidade
            ]);

            DB::commit();

            // Retornar resposta no formato esperado pelo frontend
            return response()->json([
                'success' => true,
                'message' => 'Pagamento criado com sucesso',
                'pagamento' => [
                    'id' => $preference['preference_id'] ?? $pagamento->id,
                    'transaction_amount' => $valor,
                    'description' => $descricao,
                    'payment_method_id' => 'all',
                    'payment_type_id' => 'credit_card',
                    'status' => 'pending',
                    'status_detail' => 'pending',
                    'date_created' => now()->toISOString(),
                    'date_approved' => null,
                    'init_point' => $preference['init_point'] ?? null,
                    'external_reference' => (string) $pagamento->id,
                ],
                'payment_data' => $preference,
                'redirect_url' => $redirectUrl
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erro ao criar pagamento de teste: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Erro ao criar pagamento',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verifica o status de um pagamento manualmente
     */
    public function verificarPagamento(Request $request)
    {
        try {
            $validated = $request->validate([
                'pagamento_id' => 'required|exists:pagamentos,id'
            ]);

            // Busca o pagamento
            $pagamento = Pagamento::findOrFail($validated['pagamento_id']);

            /** @var UnifiedPaymentService $paymentService */
            $paymentService = app(UnifiedPaymentService::class);

            // Primeiro, buscar informações da preferência (sempre deve existir)
            Log::info('[TestePagamentoController] Buscando informações da preferência', [
                'pagamento_id' => $pagamento->id,
                'preference_id' => $pagamento->preference_id
            ]);
            
            $preferenceInfo = $this->buscarInformacoesPreferencia($pagamento->preference_id);
            
            if (!$preferenceInfo) {
                return response()->json([
                    'success' => false,
                    'message' => 'Preferência não encontrada no Mercado Pago.',
                    'debug_info' => [
                        'preference_id' => $pagamento->preference_id,
                        'status_atual' => $pagamento->status
                    ]
                ], 404);
            }
            
            // Agora, verificar se existe pagamento realizado para essa preferência
            $paymentInfo = null;
            if ($pagamento->payment_id) {
                // Se já temos payment_id, buscar diretamente
                Log::info('[TestePagamentoController] Verificando pagamento com payment_id existente', [
                    'pagamento_id' => $pagamento->id,
                    'payment_id' => $pagamento->payment_id
                ]);
                
                $paymentInfo = $paymentService->getPaymentInfo($pagamento->payment_id);
            } else {
                // Se não tem payment_id, buscar pagamentos pelo external_reference (ID do pagamento)
                Log::info('[TestePagamentoController] Buscando pagamentos pelo external_reference', [
                    'pagamento_id' => $pagamento->id,
                    'external_reference' => $pagamento->id
                ]);
                
                $paymentInfo = $this->buscarPagamentosPorPreferenceId($pagamento->id);
            }

            // Se não encontrou pagamento, mas encontrou a preferência, retornar info da preferência
            if (!$paymentInfo) {
                Log::info('[TestePagamentoController] Pagamento não encontrado, mas preferência existe', [
                    'preference_id' => $pagamento->preference_id,
                    'preference_status' => $preferenceInfo['status'] ?? 'N/A'
                ]);
                
                // Retornar informações da preferência indicando que pagamento ainda não foi realizado
                return response()->json([
                    'success' => true,
                    'message' => 'Preferência encontrada, mas pagamento ainda não foi realizado.',
                    'pagamento' => [
                        'id' => $preferenceInfo['id'],
                        'transaction_amount' => $pagamento->amount,
                        'description' => $pagamento->notes,
                        'payment_method_id' => null,
                        'payment_type_id' => null,
                        'status' => 'pendente',
                        'status_detail' => 'pending',
                        'date_created' => $pagamento->created_at->toISOString(),
                        'date_approved' => null,
                        'init_point' => $preferenceInfo['init_point'] ?? null,
                        'external_reference' => (string) $pagamento->id,
                    ],
                    'payment_data' => $preferenceInfo,
                    'preference_found' => true,
                    'payment_found' => false
                ]);
            }

            Log::info('[TestePagamentoController] Pagamento encontrado no Mercado Pago', [
                'payment_id' => $paymentInfo['id'] ?? null,
                'status' => $paymentInfo['status'] ?? null,
                'external_reference' => $paymentInfo['external_reference'] ?? null
            ]);

            // Atualizar status do pagamento usando o UnifiedPaymentService
            $paymentService->processWebhook($paymentInfo['id'] ?? null);

            // Recarregar o pagamento do banco para obter os dados atualizados
            $pagamento->refresh();

            // Formatar resposta para o frontend
            return response()->json([
                'success' => true,
                'message' => 'Status do pagamento verificado com sucesso',
                'pagamento' => [
                    'id' => $paymentInfo['id'] ?? $pagamento->transaction_id,
                    'transaction_amount' => $pagamento->amount,
                    'description' => $pagamento->notes,
                    'payment_method_id' => $pagamento->method,
                    'payment_type_id' => $this->getPaymentTypeId($pagamento->method),
                    'status' => $this->getStatusMercadoPago($pagamento->status),
                    'status_detail' => $paymentInfo['status_detail'] ?? 'pending',
                    'date_created' => $pagamento->created_at->toISOString(),
                    'date_approved' => $pagamento->paid_at?->toISOString(),
                    'init_point' => $paymentInfo['init_point'] ?? null,
                    'external_reference' => (string) $pagamento->id,
                ],
                'payment_data' => $paymentInfo
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao verificar pagamento: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Erro ao verificar pagamento',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mapeia o status do MercadoPago para o status interno
     */
    private function mapearStatusPagamento($mercadoPagoStatus)
    {
        switch ($mercadoPagoStatus) {
            case 'approved':
                return 'aprovado';
            case 'pending':
                return 'pendente';
            case 'in_process':
                return 'processando';
            case 'rejected':
                return 'rejeitado';
            case 'cancelled':
                return 'cancelado';
            case 'refunded':
                return 'reembolsado';
            case 'charged_back':
                return 'estornado';
            default:
                return 'pendente';
        }
    }

    /**
     * Retorna o ID do método de pagamento do MercadoPago
     */
    private function getPaymentMethodId($metodoPagamento)
    {
        switch ($metodoPagamento) {
            case 'pix':
                return 'pix';
            case 'boleto':
                return 'bolbradesco';
            case 'credit_card':
                return 'master'; // Pode ser 'visa', 'master', etc.
            default:
                return 'pix';
        }
    }

    /**
     * Converte o método de pagamento interno para payment_type_id do MercadoPago
     */
    private function getPaymentTypeId($method)
    {
        switch ($method) {
            case 'pix':
                return 'bank_transfer';
            case 'boleto':
                return 'ticket';
            case 'cartao_credito':
                return 'credit_card';
            case 'cartao_debito':
                return 'debit_card';
            default:
                return 'credit_card';
        }
    }

    /**
     * Busca pagamentos no Mercado Pago usando o external_reference
     */
    private function buscarPagamentosPorPreferenceId($externalReference)
    {
        if (!$externalReference) {
            return null;
        }

        try {
            // Obter o serviço MercadoPago diretamente
            $mercadoPagoService = app(\App\Services\MercadoPagoService::class);
            
            // Montar a URL de busca com o filtro de external_reference
            $baseUrl = config('mercadopago.base_url');
            $accessToken = $mercadoPagoService->getAccessToken();
            
            $httpClient = \Illuminate\Support\Facades\Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ]);

            // Desabilitar verificação SSL em desenvolvimento
            $disableSslVerify = app()->environment('local', 'development') || env('MERCADOPAGO_DISABLE_SSL_VERIFY', false);
            if ($disableSslVerify) {
                $httpClient = $httpClient->withOptions(['verify' => false]);
            }

            // Buscar pagamentos filtrando por external_reference
            $response = $httpClient->get($baseUrl . '/v1/payments/search', [
                'external_reference' => $externalReference,
                'limit' => 10
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $pagamentos = $data['results'] ?? [];
                
                Log::info('[TestePagamentoController] Pagamentos encontrados para external_reference', [
                    'external_reference' => $externalReference,
                    'quantidade' => count($pagamentos)
                ]);
                
                // Retornar o pagamento mais recente (geralmente o que queremos)
                if (!empty($pagamentos)) {
                    // Ordenar por data de criação (mais recente primeiro)
                    usort($pagamentos, function($a, $b) {
                        $dateA = strtotime($a['date_created'] ?? 0);
                        $dateB = strtotime($b['date_created'] ?? 0);
                        return $dateB <=> $dateA;
                    });
                    
                    $payment = $pagamentos[0]; // Pegar o pagamento mais recente
                    
                    Log::info('[TestePagamentoController] Pagamento selecionado', [
                        'payment_id' => $payment['id'] ?? 'N/A',
                        'status' => $payment['status'] ?? 'N/A',
                        'status_detail' => $payment['status_detail'] ?? 'N/A',
                        'payment_method_id' => $payment['payment_method_id'] ?? 'N/A'
                    ]);
                    
                    return $payment;
                }
            }
            
            Log::warning('[TestePagamentoController] Nenhum pagamento encontrado para external_reference', [
                'external_reference' => $externalReference,
                'response_status' => $response->status(),
                'response_body' => $response->body()
            ]);
            
            return null;
            
        } catch (\Exception $e) {
            Log::error('[TestePagamentoController] Erro ao buscar pagamentos por external_reference', [
                'external_reference' => $externalReference,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Busca informações de uma preferência no Mercado Pago
     */
    private function buscarInformacoesPreferencia($preferenceId)
    {
        if (!$preferenceId) {
            return null;
        }

        try {
            // Obter o serviço MercadoPago diretamente
            $mercadoPagoService = app(\App\Services\MercadoPagoService::class);
            
            // Montar a URL para buscar a preferência
            $baseUrl = config('mercadopago.base_url');
            $accessToken = $mercadoPagoService->getAccessToken();
            
            $httpClient = \Illuminate\Support\Facades\Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ]);

            // Desabilitar verificação SSL em desenvolvimento
            $disableSslVerify = app()->environment('local', 'development') || env('MERCADOPAGO_DISABLE_SSL_VERIFY', false);
            if ($disableSslVerify) {
                $httpClient = $httpClient->withOptions(['verify' => false]);
            }

            // Buscar informações da preferência
            $response = $httpClient->get($baseUrl . '/checkout/preferences/' . $preferenceId);

            if ($response->successful()) {
                $preferenceData = $response->json();
                
                Log::info('[TestePagamentoController] Preferência encontrada', [
                    'preference_id' => $preferenceId,
                    'status' => $preferenceData['status'] ?? 'N/A'
                ]);
                
                return $preferenceData;
            }
            
            Log::warning('[TestePagamentoController] Preferência não encontrada', [
                'preference_id' => $preferenceId,
                'response_status' => $response->status(),
                'response_body' => $response->body()
            ]);
            
            return null;
            
        } catch (\Exception $e) {
            Log::error('[TestePagamentoController] Erro ao buscar informações da preferência', [
                'preference_id' => $preferenceId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Exibe a página de checkout para validação de pagamento
     */
    public function checkout($preferenceId)
    {
        try {
            Log::info('[TestePagamentoController] Acessando página de checkout', [
                'preference_id' => $preferenceId
            ]);

            // Buscar pagamento pelo preference_id
            $pagamento = Pagamento::where('preference_id', $preferenceId)
                ->orWhere('id', $preferenceId)
                ->first();

            if (!$pagamento) {
                Log::warning('[TestePagamentoController] Pagamento não encontrado para checkout', [
                    'preference_id' => $preferenceId
                ]);
                
                return Inertia::render('checkout-erro', [
                    'error' => 'Pagamento não encontrado',
                    'message' => 'Não foi possível encontrar as informações do pagamento.'
                ]);
            }

            // Buscar informações da preferência no Mercado Pago
            $preferenceInfo = $this->buscarInformacoesPreferencia($pagamento->preference_id);
            
            if (!$preferenceInfo) {
                Log::warning('[TestePagamentoController] Preferência não encontrada no Mercado Pago', [
                    'preference_id' => $pagamento->preference_id
                ]);
                
                return Inertia::render('checkout-erro', [
                    'error' => 'Preferência não encontrada',
                    'message' => 'A preferência de pagamento não foi encontrada no Mercado Pago.'
                ]);
            }

            // Verificar se existe pagamento realizado
            $paymentInfo = null;
            if ($pagamento->payment_id) {
                $paymentService = app(\App\Services\UnifiedPaymentService::class);
                $paymentInfo = $paymentService->getPaymentInfo($pagamento->payment_id);
            } else {
                $paymentInfo = $this->buscarPagamentosPorPreferenceId($pagamento->id);
            }

            // Se encontrou pagamento, atualizar status
            if ($paymentInfo) {
                $paymentService = app(\App\Services\UnifiedPaymentService::class);
                $paymentService->processWebhook($paymentInfo['id'] ?? null);
                $pagamento->refresh();
            }

            return Inertia::render('checkout-validacao', [
                'pagamento' => [
                    'id' => $pagamento->id,
                    'amount' => $pagamento->amount,
                    'description' => $pagamento->notes,
                    'status' => $pagamento->status,
                    'preference_id' => $pagamento->preference_id,
                    'payment_id' => $pagamento->payment_id,
                    'created_at' => $pagamento->created_at->toISOString(),
                    'paid_at' => $pagamento->paid_at?->toISOString(),
                ],
                'preference_info' => $preferenceInfo,
                'payment_info' => $paymentInfo,
                'app_url' => env('APP_URL', 'http://localhost')
            ]);

        } catch (\Exception $e) {
            Log::error('[TestePagamentoController] Erro ao acessar checkout', [
                'preference_id' => $preferenceId,
                'error' => $e->getMessage()
            ]);
            
            return Inertia::render('checkout-erro', [
                'error' => 'Erro ao processar checkout',
                'message' => 'Ocorreu um erro ao processar a validação do pagamento.'
            ]);
        }
    }

    /**
     * Converte o status interno para status do MercadoPago
     */
    private function getStatusMercadoPago($status)
    {
        switch ($status) {
            case 'pago':
                return 'approved';
            case 'pendente':
                return 'pending';
            case 'falhou':
                return 'rejected';
            case 'cancelado':
                return 'cancelled';
            default:
                return 'pending';
        }
    }
}
