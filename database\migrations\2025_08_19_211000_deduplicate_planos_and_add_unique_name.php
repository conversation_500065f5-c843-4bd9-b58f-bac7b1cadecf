<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // 1) Deduplicate by name: keep lowest id, re-point assinaturas, delete extras
        $duplicates = DB::table('planos')
            ->select('name')
            ->groupBy('name')
            ->havingRaw('COUNT(*) > 1')
            ->pluck('name');

        foreach ($duplicates as $name) {
            $planos = DB::table('planos')
                ->where('name', $name)
                ->orderBy('id')
                ->get();
            if ($planos->count() <= 1) {
                continue;
            }
            $keeper = $planos->first();
            $toDelete = $planos->slice(1)->pluck('id')->all();

            if (!empty($toDelete)) {
                // Re-point foreign keys in assinaturas
                DB::table('assinaturas')
                    ->whereIn('plano_id', $toDelete)
                    ->update(['plano_id' => $keeper->id]);

                // Delete duplicate planos
                DB::table('planos')->whereIn('id', $toDelete)->delete();
            }
        }

        // 2) Add unique index on name to prevent future duplicates
        Schema::table('planos', function (Blueprint $table) {
            // Guard against existing index
            if (!Schema::hasColumn('planos', 'name')) {
                return; // safety, schema should have it
            }
            $table->unique('name');
        });
    }

    public function down(): void
    {
        // Drop unique index on name
        Schema::table('planos', function (Blueprint $table) {
            $table->dropUnique(['name']);
        });
    }
};
