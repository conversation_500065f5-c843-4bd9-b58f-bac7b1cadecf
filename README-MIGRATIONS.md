# Script de Migrações para Produção

Este projeto contém scripts para executar migrações do Laravel no ambiente de produção de forma segura e controlada.

## Arquivos Criados

- `run-production-migrations.sh` - Versão para Linux/Mac/WSL
- `run-production-migrations.bat` - Versão para Windows
- `README-MIGRATIONS.md` - Este arquivo de instruções

## Pré-requisitos

- PHP instalado
- Composer instalado
- Laravel Artisan disponível
- Arquivo `.env.production` configurado com as credenciais do banco de produção

## Como Usar

### No Windows

1. Abra o terminal (CMD, PowerShell ou Git Bash)
2. Navegue até o diretório raiz do projeto
3. Execute o script:

```bash
run-production-migrations.bat
```

### No Linux/Mac/WSL

1. Abra o terminal
2. Navegue até o diretório raiz do projeto
3. Dê permissão de execução ao script (se ainda não tiver):

```bash
chmod +x run-production-migrations.sh
```

4. Execute o script:

```bash
./run-production-migrations.sh
```

## O que o script faz?

1. **Verificação do .env.production**: Confirma se o arquivo de configuração de produção existe
2. **Backup do .env atual**: Faz uma cópia de segurança do seu arquivo .env atual
3. **Carrega configurações de produção**: Copia o .env.production para .env
4. **Executa migrações**: Roda `php artisan migrate --force`
5. **Otimizações**: Limpa caches de configuração, rotas, views e cache geral
6. **Seeders (opcional)**: Executa seeders de produção se existirem
7. **Restauração (opcional)**: Pergunta se deseja restaurar o .env original

## Segurança

- O script **não hardcoda** credenciais do banco de dados
- Utiliza as variáveis do arquivo `.env.production`
- Faz backup automático do seu `.env` atual
- Permite restaurar o ambiente original após as migrações
- Usa `--force` para executar migrações em produção (sem confirmação interativa)

## Variáveis de Ambiente Utilizadas

O script utiliza as seguintes variáveis do `.env.production`:

```env
DB_CONNECTION=mysql
DB_HOST=***************
DB_PORT=3306
DB_DATABASE=luisf488_db
DB_USERNAME=luisf488_user
DB_PASSWORD="OZ8xgA}~b~kO"
```

## Fluxo de Execução

```
Início
  ↓
Verifica .env.production
  ↓
Backup do .env atual
  ↓
Copia .env.production → .env
  ↓
Executa php artisan migrate --force
  ↓
Se sucesso: Otimiza Laravel
  ↓
Pergunta: Restaurar .env original?
  ↓
Fim
```

## Troubleshooting

### Erro: "Arquivo .env.production não encontrado!"
- Verifique se o arquivo `.env.production` existe no diretório raiz
- Confirme se você está no diretório correto do projeto

### Erro: "php artisan migrate --force" falhou
- Verifique a conexão com o banco de dados
- Confirme as credenciais no `.env.production`
- Verifique se há migrações pendentes

### Erro de permissão (Linux/Mac)
- Execute `chmod +x run-production-migrations.sh`
- Verifique se você tem permissão para executar scripts

## Observações Importantes

- Sempre faça backup do seu banco de produção antes de executar migrações
- Teste as migrações em ambiente de desenvolvimento/homologação primeiro
- Mantenha seu `.env.production` atualizado e seguro
- Não commit o arquivo `.env.production` no versionamento (já está no .gitignore)
