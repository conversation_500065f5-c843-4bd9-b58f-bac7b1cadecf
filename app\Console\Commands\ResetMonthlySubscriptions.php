<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Assinatura;
use App\Models\Plano;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Services\MercadoPagoService;

class ResetMonthlySubscriptions extends Command
{
    protected $signature = 'assinaturas:reset-mensal';

    protected $description = 'Reseta a contagem de sessões das assinaturas ativas no primeiro dia do mês e atualiza o período atual';

    public function handle(): int
    {
        if (!config('features.plans_enabled')) {
            $this->info('Planos desabilitados. Comando ignorado.');
            return Command::SUCCESS;
        }

        $hoje = Carbon::now();
        $inicioMes = $hoje->copy()->startOfMonth();
        $fimMes = $hoje->copy()->endOfMonth();

        $this->info('Iniciando reset mensal das assinaturas: ' . $inicioMes->toDateString());

        // 1) Aplicar trocas de plano agendadas para este novo ciclo
        Assinatura::where('status', 'ativa')
            ->whereNotNull('scheduled_new_plano_id')
            ->whereDate('scheduled_change_date', '<=', $inicioMes)
            ->chunkById(500, function ($assinaturas) use ($inicioMes, $fimMes) {
                DB::transaction(function () use ($assinaturas, $inicioMes, $fimMes) {
                    $mp = new MercadoPagoService();
                    foreach ($assinaturas as $assinatura) {
                        $assinatura->plano_id = $assinatura->scheduled_new_plano_id;
                        $assinatura->scheduled_new_plano_id = null;
                        $assinatura->scheduled_change_date = null;
                        $assinatura->sessions_used = 0;
                        $assinatura->current_period_start = $inicioMes;
                        $assinatura->current_period_end = $fimMes;
                        $assinatura->start_date = $inicioMes;
                        $assinatura->end_date = $fimMes;
                        $assinatura->save();

                        // Atualizar valor recorrente no Mercado Pago, se houver assinatura MP
                        if (!empty($assinatura->mercadopago_subscription_id)) {
                            $novoPlano = Plano::find($assinatura->plano_id);
                            if ($novoPlano) {
                                $mp->updateSubscriptionAmount($assinatura->mercadopago_subscription_id, (float) $novoPlano->price);
                            }
                        }
                    }
                });
            });

        // 2) Finalizar assinaturas com cancelamento agendado cujo período terminou antes do início do mês atual
        Assinatura::where('status', 'ativa')
            ->where('cancel_at_period_end', true)
            ->whereDate('end_date', '<', $inicioMes)
            ->chunkById(500, function ($assinaturas) {
                DB::transaction(function () use ($assinaturas) {
                    foreach ($assinaturas as $assinatura) {
                        $assinatura->status = 'cancelada';
                        $assinatura->cancel_at_period_end = false;
                        $assinatura->save();
                    }
                });
            });

        // 3) Reset para as assinaturas que permanecem ativas neste novo ciclo
        Assinatura::where('status', 'ativa')
            ->chunkById(500, function ($assinaturas) use ($inicioMes, $fimMes) {
                DB::transaction(function () use ($assinaturas, $inicioMes, $fimMes) {
                    foreach ($assinaturas as $assinatura) {
                        $assinatura->sessions_used = 0; // zera consumo
                        $assinatura->current_period_start = $inicioMes;
                        $assinatura->current_period_end = $fimMes;
                        $assinatura->start_date = $inicioMes;
                        $assinatura->end_date = $fimMes;
                        $assinatura->save();
                    }
                });
            });

        $this->info('Reset mensal concluído.');
        return Command::SUCCESS;
    }
}
