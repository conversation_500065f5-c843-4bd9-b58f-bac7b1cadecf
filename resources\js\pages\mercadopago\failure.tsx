import { Head } from '@inertiajs/react';
import { XCircle, ArrowLeft, CreditCard, RefreshCw, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface Props {
    payment_id?: string;
    external_reference?: string;
    message: string;
}

export default function MercadoPagoFailure({ payment_id, external_reference, message }: Props) {
    const handleBackToSite = () => {
        window.location.href = '/';
    };

    const handleTryAgain = () => {
        window.location.href = '/planos';
    };

    return (
        <>
            <Head title="Pagamento Não Aprovado" />
            
            <div className="min-h-screen bg-gradient-to-br from-red-50 to-rose-100 flex items-center justify-center p-4">
                <div className="max-w-md w-full space-y-6">
                    {/* Ícone de Erro */}
                    <div className="text-center">
                        <div className="mx-auto w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mb-4">
                            <XCircle className="w-12 h-12 text-white" />
                        </div>
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">
                            Pagamento Não Aprovado
                        </h1>
                        <p className="text-gray-600">
                            {message}
                        </p>
                    </div>

                    {/* Detalhes do Pagamento */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg">
                                <CreditCard className="w-5 h-5" />
                                Detalhes da Tentativa
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {payment_id && (
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">ID da Tentativa:</span>
                                    <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                        {payment_id}
                                    </span>
                                </div>
                            )}
                            
                            {external_reference && (
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">Referência:</span>
                                    <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                        {external_reference}
                                    </span>
                                </div>
                            )}

                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Status:</span>
                                <span className="text-sm font-semibold text-red-600 bg-red-100 px-2 py-1 rounded">
                                    Rejeitado
                                </span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">Data:</span>
                                <span className="text-sm text-gray-900">
                                    {new Date().toLocaleDateString('pt-BR', {
                                        day: '2-digit',
                                        month: '2-digit',
                                        year: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    })}
                                </span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Possíveis Motivos */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg">
                                <HelpCircle className="w-5 h-5" />
                                Possíveis Motivos
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <ul className="space-y-2 text-sm text-gray-600">
                                <li className="flex items-start gap-2">
                                    <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span>Dados do cartão incorretos ou inválidos</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span>Limite insuficiente no cartão</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span>Cartão vencido ou bloqueado</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span>Problema temporário com o banco emissor</span>
                                </li>
                            </ul>
                        </CardContent>
                    </Card>

                    {/* Botões de Ação */}
                    <div className="space-y-3">
                        <Button 
                            onClick={handleTryAgain}
                            className="w-full bg-blue-600 hover:bg-blue-700"
                        >
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Tentar Novamente
                        </Button>
                        
                        <Button 
                            onClick={handleBackToSite}
                            variant="outline"
                            className="w-full"
                        >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Voltar ao Site
                        </Button>
                    </div>

                    {/* Informações de Suporte */}
                    <div className="text-center text-xs text-gray-500">
                        <p>Precisa de ajuda? Entre em contato conosco</p>
                        <p className="mt-1">
                            Processamento seguro pelo Mercado Pago
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
