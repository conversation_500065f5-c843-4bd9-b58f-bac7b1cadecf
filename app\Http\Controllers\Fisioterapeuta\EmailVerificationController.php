<?php

namespace App\Http\Controllers\Fisioterapeuta;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class EmailVerificationController extends Controller
{
    /**
     * Exibe a página de aviso para verificação de email
     */
    public function notice()
    {
        $user = Auth::user();
        
        // Se o email já está verificado, redireciona para o dashboard
        if ($user->hasVerifiedEmail()) {
            return redirect()->route('fisioterapeuta.dashboard');
        }

        return Inertia::render('fisioterapeuta/verificar-email', [
            'user' => $user,
            'canResendEmail' => true,
        ]);
    }

    /**
     * Reenvia o email de verificação
     */
    public function resend(Request $request)
    {
        $user = Auth::user();
        
        // Se o email já está verificado
        if ($user->hasVerifiedEmail()) {
            return redirect()->route('fisioterapeuta.dashboard');
        }

        // Verificar se o usuário pode solicitar verificação
        if (!$user->canRequestEmailVerification()) {
            $remainingTime = $user->getEmailVerificationThrottleTime();
            if ($remainingTime > 0) {
                return back()->with('status', 'verification-link-throttled')
                            ->with('remaining_time', $remainingTime)
                            ->with('message', 'Aguarde ' . $remainingTime . ' segundos antes de solicitar outro email de verificação.');
            } else {
                return back()->with('status', 'verification-link-limit-reached')
                            ->with('message', 'Limite de emails de verificação atingido. Tente novamente em 1 hora.');
            }
        }

        // Envia o email de verificação
        $emailSent = $user->sendEmailVerificationNotification();

        if ($emailSent) {
            return back()->with('status', 'verification-link-sent')
                        ->with('message', 'Email de verificação reenviado com sucesso!');
        } else {
            // Se não foi enviado devido ao throttling ou limite
            $remainingTime = $user->getEmailVerificationThrottleTime();
            if ($remainingTime > 0) {
                return back()->with('status', 'verification-link-throttled')
                            ->with('remaining_time', $remainingTime)
                            ->with('message', 'Aguarde ' . $remainingTime . ' segundos antes de solicitar outro email de verificação.');
            } else {
                return back()->with('status', 'verification-link-limit-reached')
                            ->with('message', 'Limite de emails de verificação atingido. Tente novamente em 1 hora.');
            }
        }
    }
}
