<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Pagamento;
use App\Models\Assinatura;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class PagamentoController extends Controller
{
    /**
     * Display a listing of payments
     */
    public function index(Request $request)
    {
        $query = Pagamento::with(['assinatura.user', 'assinatura.plano']);

        // Filtro por status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filtro por forma de pagamento
        if ($request->filled('forma_pagamento')) {
            $query->where('forma_pagamento', $request->forma_pagamento);
        }

        // Filtro por período
        if ($request->filled('periodo')) {
            switch ($request->periodo) {
                case 'hoje':
                    $query->whereDate('data_vencimento', Carbon::today());
                    break;
                case 'semana':
                    $query->whereBetween('data_vencimento', [
                        Carbon::now()->startOfWeek(),
                        Carbon::now()->endOfWeek()
                    ]);
                    break;
                case 'mes':
                    $query->whereMonth('data_vencimento', Carbon::now()->month)
                          ->whereYear('data_vencimento', Carbon::now()->year);
                    break;
                case 'vencidos':
                    $query->vencidos();
                    break;
            }
        }

        // Busca por nome do paciente
        if ($request->filled('search')) {
            $query->whereHas('assinatura.user', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        $pagamentos = $query->orderBy('data_vencimento', 'desc')
            ->paginate(20)
            ->withQueryString();

        // Estatísticas
        $stats = [
            'total_pendente' => Pagamento::pendentes()->sum('amount'),
            'total_pago_mes' => Pagamento::pagos()
                ->whereMonth('paid_at', Carbon::now()->month)
                ->whereYear('paid_at', Carbon::now()->year)
                ->sum('amount'),
            'total_vencido' => Pagamento::vencidos()->sum('amount'),
            'count_pendente' => Pagamento::pendentes()->count(),
            'count_pago_mes' => Pagamento::pagos()
                ->whereMonth('paid_at', Carbon::now()->month)
                ->whereYear('paid_at', Carbon::now()->year)
                ->count(),
            'count_vencido' => Pagamento::vencidos()->count(),
        ];

        return Inertia::render('admin/pagamentos/index', [
            'pagamentos' => $pagamentos,
            'filters' => $request->only(['status', 'forma_pagamento', 'periodo', 'search']),
            'stats' => $stats,
        ]);
    }

    /**
     * Display the specified payment
     */
    public function show(Pagamento $pagamento)
    {
        $pagamento->load(['assinatura.user', 'assinatura.plano']);

        return Inertia::render('admin/pagamentos/show', [
            'pagamento' => $pagamento,
        ]);
    }

    /**
     * Mark payment as paid
     */
    public function markAsPaid(Request $request, Pagamento $pagamento)
    {
        $request->validate([
            'transaction_id' => 'nullable|string|max:255',
            'observacoes' => 'nullable|string|max:1000',
            'gateway_response' => 'nullable|array',
        ]);

        if ($pagamento->status === 'pago') {
            return back()->with('error', 'Este pagamento já foi marcado como pago.');
        }

        $pagamento->update([
            'status' => 'pago',
            'data_pagamento' => Carbon::now(),
            'transaction_id' => $request->transaction_id,
            'observacoes' => $request->observacoes,
            'gateway_response' => $request->gateway_response,
        ]);

        return back()->with('success', 'Pagamento marcado como pago com sucesso!');
    }

    /**
     * Mark payment as failed
     */
    public function markAsFailed(Request $request, Pagamento $pagamento)
    {
        $request->validate([
            'observacoes' => 'nullable|string|max:1000',
        ]);

        if ($pagamento->status === 'pago') {
            return back()->with('error', 'Não é possível marcar um pagamento pago como falhou.');
        }

        $pagamento->update([
            'status' => 'falhou',
            'observacoes' => $request->observacoes,
        ]);

        return back()->with('success', 'Pagamento marcado como falhou.');
    }

    /**
     * Cancel payment
     */
    public function cancel(Request $request, Pagamento $pagamento)
    {
        $request->validate([
            'observacoes' => 'nullable|string|max:1000',
        ]);

        if ($pagamento->status === 'pago') {
            return back()->with('error', 'Não é possível cancelar um pagamento já pago.');
        }

        $pagamento->update([
            'status' => 'cancelado',
            'observacoes' => $request->observacoes,
        ]);

        return back()->with('success', 'Pagamento cancelado com sucesso.');
    }

    /**
     * Generate new payment for subscription
     */
    public function generatePayment(Request $request, Assinatura $assinatura)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'data_vencimento' => 'required|date|after_or_equal:today',
            'forma_pagamento' => 'required|in:cartao_credito,cartao_debito,pix,boleto',
            'observacoes' => 'nullable|string|max:1000',
        ]);

        $pagamento = Pagamento::create([
            'assinatura_id' => $assinatura->id,
            'amount' => $request->amount,
            'data_vencimento' => $request->data_vencimento,
            'forma_pagamento' => $request->forma_pagamento,
            'status' => 'pendente',
            'observacoes' => $request->observacoes,
        ]);

        return redirect()->route('admin.pagamentos.show', $pagamento)
            ->with('success', 'Pagamento gerado com sucesso!');
    }

    /**
     * Update payment details
     */
    public function update(Request $request, Pagamento $pagamento)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'data_vencimento' => 'required|date',
            'forma_pagamento' => 'required|in:cartao_credito,cartao_debito,pix,boleto',
            'observacoes' => 'nullable|string|max:1000',
        ]);

        if ($pagamento->status === 'pago') {
            return back()->with('error', 'Não é possível editar um pagamento já pago.');
        }

        $pagamento->update($request->only([
            'amount',
            'data_vencimento',
            'forma_pagamento',
            'observacoes'
        ]));

        return back()->with('success', 'Pagamento atualizado com sucesso!');
    }

    /**
     * Get payment statistics for dashboard
     */
    public function getStats()
    {
        $hoje = Carbon::today();
        $inicioMes = Carbon::now()->startOfMonth();
        $fimMes = Carbon::now()->endOfMonth();

        return [
            'receita_mes' => Pagamento::pagos()
                ->whereBetween('data_pagamento', [$inicioMes, $fimMes])
                ->sum('amount'),
            'pendentes_hoje' => Pagamento::pendentes()
                ->whereDate('data_vencimento', $hoje)
                ->sum('amount'),
            'vencidos' => Pagamento::vencidos()->sum('amount'),
            'total_recebido' => Pagamento::pagos()->sum('amount'),
            'count_pendentes' => Pagamento::pendentes()->count(),
            'count_vencidos' => Pagamento::vencidos()->count(),
        ];
    }

    /**
     * Export payments to CSV
     */
    public function export(Request $request)
    {
        $query = Pagamento::with(['assinatura.user', 'assinatura.plano']);

        // Aplicar mesmos filtros da listagem
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('forma_pagamento')) {
            $query->where('forma_pagamento', $request->forma_pagamento);
        }

        if ($request->filled('periodo')) {
            switch ($request->periodo) {
                case 'hoje':
                    $query->whereDate('data_vencimento', Carbon::today());
                    break;
                case 'semana':
                    $query->whereBetween('data_vencimento', [
                        Carbon::now()->startOfWeek(),
                        Carbon::now()->endOfWeek()
                    ]);
                    break;
                case 'mes':
                    $query->whereMonth('data_vencimento', Carbon::now()->month)
                          ->whereYear('data_vencimento', Carbon::now()->year);
                    break;
                case 'vencidos':
                    $query->vencidos();
                    break;
            }
        }

        $pagamentos = $query->orderBy('data_vencimento', 'desc')->get();

        $filename = 'pagamentos_' . Carbon::now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($pagamentos) {
            $file = fopen('php://output', 'w');
            
            // Cabeçalho CSV
            fputcsv($file, [
                'ID',
                'Paciente',
                'Email',
                'Plano',
                'Valor',
                'Status',
                'Forma Pagamento',
                'Data Vencimento',
                'Data Pagamento',
                'Transaction ID',
                'Observações'
            ]);

            // Dados
            foreach ($pagamentos as $pagamento) {
                fputcsv($file, [
                    $pagamento->id,
                    $pagamento->assinatura->user->name,
                    $pagamento->assinatura->user->email,
                    $pagamento->assinatura->plano->name,
                    $pagamento->amount,
                    $pagamento->status,
                    $pagamento->forma_pagamento,
                    $pagamento->data_vencimento->format('d/m/Y'),
                    $pagamento->data_pagamento ? $pagamento->data_pagamento->format('d/m/Y H:i') : '',
                    $pagamento->transaction_id ?? '',
                    $pagamento->observacoes ?? ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
