<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Fisioterapeuta;

class FisioterapeutaRejeitado extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public Fisioterapeuta $fisioterapeuta,
        public ?string $loginUrl = null
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Informações sobre sua conta - F4 Fisio',
            from: new Address(config('mail.from.address'), config('mail.from.name'))
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.fisioterapeuta-rejeitado',
            with: [
                'fisioterapeuta' => $this->fisioterapeuta,
                'nome' => $this->fisioterapeuta->user->name,
                'email' => $this->fisioterapeuta->user->email,
                'motivoRejeicao' => $this->fisioterapeuta->rejection_reason,
                'loginUrl' => $this->loginUrl ?? \App\Helpers\EmailUrlHelper::generateEmailUrl('login'),
            ],
        );
    }

    public function attachments(): array
    {
        return [];
    }
}
