import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Usuários',
        href: '/admin/usuarios',
    },
    {
        title: 'Novo Usuário',
        href: '/admin/usuarios/create',
    },
];

export default function CreateUsuario() {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: undefined as string | undefined,
        phone: '',
        birth_date: '',
        gender: undefined as string | undefined,
        address: '',
        medical_history: '',
        emergency_contact: '',
        active: true as boolean,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.usuarios.store'));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Novo Usuário" />
            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                <div className="flex items-center gap-4">
                    <Link href={route('admin.usuarios.index')}>
                        <Button variant="ghost" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Voltar
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Novo Usuário</h1>
                        <p className="text-gray-600">Cadastre um novo usuário no sistema</p>
                    </div>
                </div>

                <div className="max-w-2xl">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="rounded-lg border bg-card p-6">
                            <h2 className="mb-4 text-lg font-semibold">Informações Básicas</h2>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <Label htmlFor="name">Nome *</Label>
                                    <Input id="name" value={data.name} onChange={(e) => setData('name', e.target.value)} required />
                                    {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="email">Email *</Label>
                                    <Input id="email" type="email" value={data.email} onChange={(e) => setData('email', e.target.value)} required />
                                    {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="password">Senha *</Label>
                                    <Input
                                        id="password"
                                        type="password"
                                        value={data.password}
                                        onChange={(e) => setData('password', e.target.value)}
                                        required
                                    />
                                    {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="password_confirmation">Confirmar Senha *</Label>
                                    <Input
                                        id="password_confirmation"
                                        type="password"
                                        value={data.password_confirmation}
                                        onChange={(e) => setData('password_confirmation', e.target.value)}
                                        required
                                    />
                                    {errors.password_confirmation && <p className="mt-1 text-sm text-red-600">{errors.password_confirmation}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="role">Role *</Label>
                                    <Select value={data.role || undefined} onValueChange={(value) => setData('role', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione o role" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="admin">Administrador</SelectItem>
                                            <SelectItem value="fisioterapeuta">Fisioterapeuta</SelectItem>
                                            <SelectItem value="paciente">Paciente</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.role && <p className="mt-1 text-sm text-red-600">{errors.role}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="phone">Telefone</Label>
                                    <Input id="phone" value={data.phone} onChange={(e) => setData('phone', e.target.value)} />
                                    {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone}</p>}
                                </div>
                            </div>
                        </div>

                        <div className="rounded-lg border bg-card p-6">
                            <h2 className="mb-4 text-lg font-semibold">Informações Pessoais</h2>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <Label htmlFor="birth_date">Data de Nascimento</Label>
                                    <DatePicker
                                        value={data.birth_date ? new Date(data.birth_date) : undefined}
                                        onValueChange={(date: Date | undefined) => {
                                            if (date) {
                                                const formattedDate = date.toISOString().split('T')[0];
                                                setData('birth_date', formattedDate);
                                            } else {
                                                setData('birth_date', '');
                                            }
                                        }}
                                        placeholder="Selecione a data de nascimento"
                                        captionLayout="dropdown"
                                        fromYear={new Date().getFullYear() - 120}
                                        toYear={new Date().getFullYear()}
                                        disabledDates={(date: Date) => {
                                            const today = new Date();
                                            const minDate = new Date();
                                            minDate.setFullYear(today.getFullYear() - 120);
                                            return date > today || date < minDate;
                                        }}
                                    />
                                    {errors.birth_date && <p className="mt-1 text-sm text-red-600">{errors.birth_date}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="gender">Gênero</Label>
                                    <Select value={data.gender || undefined} onValueChange={(value) => setData('gender', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione o gênero" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="masculino">Masculino</SelectItem>
                                            <SelectItem value="feminino">Feminino</SelectItem>
                                            <SelectItem value="outro">Outro</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.gender && <p className="mt-1 text-sm text-red-600">{errors.gender}</p>}
                                </div>
                                <div className="md:col-span-2">
                                    <Label htmlFor="address">Endereço</Label>
                                    <Textarea id="address" value={data.address} onChange={(e) => setData('address', e.target.value)} rows={3} />
                                    {errors.address && <p className="mt-1 text-sm text-red-600">{errors.address}</p>}
                                </div>
                                <div className="md:col-span-2">
                                    <Label htmlFor="medical_history">Histórico Médico</Label>
                                    <Textarea
                                        id="medical_history"
                                        value={data.medical_history}
                                        onChange={(e) => setData('medical_history', e.target.value)}
                                        rows={3}
                                        placeholder="Informações relevantes sobre o histórico médico..."
                                    />
                                    {errors.medical_history && <p className="mt-1 text-sm text-red-600">{errors.medical_history}</p>}
                                </div>
                                <div className="md:col-span-2">
                                    <Label htmlFor="emergency_contact">Contato de Emergência</Label>
                                    <Input
                                        id="emergency_contact"
                                        value={data.emergency_contact}
                                        onChange={(e) => setData('emergency_contact', e.target.value)}
                                        placeholder="Nome e telefone do contato de emergência"
                                    />
                                    {errors.emergency_contact && <p className="mt-1 text-sm text-red-600">{errors.emergency_contact}</p>}
                                </div>
                            </div>
                        </div>

                        <div className="rounded-lg border bg-card p-6">
                            <div className="flex items-center space-x-2">
                                <Checkbox id="active" checked={data.active} onCheckedChange={(checked) => setData('active', Boolean(checked))} />
                                <Label htmlFor="active">Usuário ativo</Label>
                            </div>
                            <p className="mt-1 text-sm text-muted-foreground">Usuários inativos não conseguem fazer login no sistema</p>
                        </div>

                        <div className="flex gap-4">
                            <Button type="submit" disabled={processing}>
                                {processing ? 'Salvando...' : 'Salvar Usuário'}
                            </Button>
                            <Link href={route('admin.usuarios.index')}>
                                <Button type="button" variant="outline">
                                    Cancelar
                                </Button>
                            </Link>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}
