<?php

namespace App\Mail;

use App\Models\Agendamento;
use App\Services\ReactEmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class LembreteAgendamento extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public function __construct(
        public Agendamento $agendamento,
        public int $horasAntecedencia = 24
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Lembrete: Consulta Agendada - F4 Fisio',
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.react-email-wrapper',
            with: [
                'htmlContent' => $this->getReactEmailContent(),
                'fallbackView' => 'emails.lembrete-agendamento',
                'fallbackData' => [
                    'agendamento' => $this->agendamento,
                    'paciente' => $this->agendamento->paciente,
                    'fisioterapeuta' => $this->agendamento->fisioterapeuta,
                    'horasAntecedencia' => $this->horasAntecedencia,
                ],
            ],
        );
    }

    private function getReactEmailContent(): string
    {
        try {
            $reactEmailService = app(ReactEmailService::class);

            return $reactEmailService->renderLembreteAgendamento([
                'paciente_nome' => $this->agendamento->paciente->name,
                'fisioterapeuta_nome' => $this->agendamento->fisioterapeuta->name,
                'data_hora' => $this->agendamento->data_agendamento->format('d/m/Y \à\s H:i'),
                'endereco' => $this->agendamento->endereco ?? 'Endereço não informado',
                'horas_antecedencia' => $this->horasAntecedencia,
                'valor' => $this->agendamento->valor ? 'R$ ' . number_format($this->agendamento->valor, 2, ',', '.') : null,
                'agendamento_url' => \App\Helpers\EmailUrlHelper::generateEmailUrl('agendamentos.show', ['agendamento' => $this->agendamento->id]),
                'whatsapp_fisioterapeuta' => $this->agendamento->fisioterapeuta->whatsapp ?? null,
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao renderizar React Email para lembrete', [
                'error' => $e->getMessage(),
                'agendamento_id' => $this->agendamento->id,
            ]);

            return '';
        }
    }

    public function attachments(): array
    {
        return [];
    }
}
