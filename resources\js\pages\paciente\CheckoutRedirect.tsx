import { useEffect } from 'react';
import { Head } from '@inertiajs/react';

interface CheckoutRedirectProps {
    redirect_url: string;
    subscription_id: string;
}

export default function CheckoutRedirect({ redirect_url, subscription_id }: CheckoutRedirectProps) {
    useEffect(() => {
        // Redirecionar imediatamente para o Mercado Pago
        window.location.href = redirect_url;
    }, [redirect_url]);

    return (
        <>
            <Head title="Redirecionando para o Mercado Pago" />
            
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="max-w-md w-full space-y-8 text-center">
                    <div>
                        <div className="mx-auto h-12 w-12 text-blue-600">
                            <svg className="animate-spin h-12 w-12" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                        <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                            Redirecionando...
                        </h2>
                        <p className="mt-2 text-sm text-gray-600">
                            Você será redirecionado para o Mercado Pago para finalizar seu pagamento.
                        </p>
                        <p className="mt-4 text-xs text-gray-500">
                            Se o redirecionamento não funcionar automaticamente, 
                            <a 
                                href={redirect_url} 
                                className="text-blue-600 hover:text-blue-500 ml-1"
                            >
                                clique aqui
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
