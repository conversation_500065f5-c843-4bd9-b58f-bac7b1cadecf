# Configuração do Mercado Pago para Produção

## ⚠️ IMPORTANTE - CREDENCIAIS DE PRODUÇÃO

Para que a aplicação funcione com pagamentos reais do Mercado Pago, você precisa:

### 1. Obter Credenciais de Produção

1. Acesse o [Painel do Mercado Pago](https://www.mercadopago.com.br/developers/panel)
2. Vá em **Suas integrações** > **Credenciais**
3. Selecione **Produção** (não Teste)
4. Co<PERSON> as credenciais:
   - **Access Token de Produção** (começa com `APP_USR-`)
   - **Public Key de Produção** (começa com `APP_USR-`)

### 2. Configurar no .env

Substitua as credenciais no arquivo `.env`:

```env
# Configurações do Mercado Pago - PRODUÇÃO
MERCADOPAGO_ACCESS_TOKEN=APP_USR-SEU_ACCESS_TOKEN_DE_PRODUCAO_AQUI
MERCADOPAGO_PUBLIC_KEY=APP_USR-SEU_PUBLIC_KEY_DE_PRODUCAO_AQUI
MERCADOPAGO_SANDBOX=false
```

### 3. Configurar Webhook de Produção

1. No painel do Mercado Pago, vá em **Webhooks**
2. Adicione uma nova URL de webhook:
   ```
   https://seudominio.com/webhook/mercadopago
   ```
3. Selecione os eventos:
   - `payment`
   - `subscription_preapproval`
   - `subscription_preapproval_plan`

### 4. Verificar Configurações

Execute o comando para verificar se tudo está configurado:

```bash
php artisan tinker --execute="
echo 'ACCESS_TOKEN: ' . (config('services.mercadopago.access_token') ? 'CONFIGURADO' : 'NÃO CONFIGURADO') . '\n';
echo 'SANDBOX: ' . (config('services.mercadopago.sandbox') ? 'TRUE (TESTE)' : 'FALSE (PRODUÇÃO)') . '\n';
echo 'WEBHOOK_SECRET: ' . (config('services.mercadopago.webhook_secret') ? 'CONFIGURADO' : 'NÃO CONFIGURADO') . '\n';
"
```

## ✅ Verificações de Segurança

- [x] Simulador removido completamente
- [x] Dados simulados limpos do banco
- [x] Webhook configurado com validação de assinatura
- [x] CSRF excluído para webhook
- [x] Logs detalhados implementados

## 🔄 Fluxo de Pagamento Real

1. **Criação de Assinatura/Pagamento**: API real do Mercado Pago
2. **Redirecionamento**: Para checkout oficial do Mercado Pago
3. **Webhook**: Notificações reais do Mercado Pago
4. **Validação**: Assinatura verificada automaticamente

## 📊 Monitoramento

Monitore os logs em:
```bash
tail -f storage/logs/laravel.log | grep -E "(WEBHOOK|MP)"
```

## 🚨 Troubleshooting

### Problema: Webhook não recebe notificações
- Verifique se a URL está acessível publicamente
- Confirme que o SSL está funcionando
- Verifique os logs do Mercado Pago no painel

### Problema: Pagamentos não são processados
- Verifique se as credenciais são de produção
- Confirme que `MERCADOPAGO_SANDBOX=false`
- Verifique logs de erro na aplicação

## 📝 Próximos Passos

1. Configurar credenciais de produção
2. Testar pagamento real com valor baixo
3. Verificar webhook em produção
4. Monitorar logs por 24h
