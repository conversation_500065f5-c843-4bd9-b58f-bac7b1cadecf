import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { AlertTriangle, Calendar, CheckCircle, Clock, DollarSign, Download, Eye, Search, User } from 'lucide-react';
import { useState } from 'react';

interface Pagamento {
    id: number;
    amount: number;
    formatted_amount: string;
    status: string;
    method: string;
    due_date: string;
    formatted_due_date: string;
    paid_at?: string;
    data_pagamento?: string;
    formatted_paid_at?: string;
    assinatura?: {
        user?: {
            name: string;
        };
    };
}

interface PaginatedPagamentos {
    data: Pagamento[];
    meta?: {
        total: number;
    };
    links?: Array<{
        url?: string;
        label: string;
        active: boolean;
    }>;
}

interface Stats {
    total_pago_mes: number;
    count_pago_mes: number;
    total_pendente: number;
    count_pendente: number;
    total_vencido: number;
    count_vencido: number;
}

interface Props {
    pagamentos: PaginatedPagamentos;
    stats: Stats;
    filters: {
        search?: string;
        status?: string;
        periodo?: string;
    };
}

export default function PagamentosIndex({ pagamentos, stats, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(route('admin.pagamentos.index'), { search: searchTerm }, { preserveState: true });
    };

    const handleFilterChange = (key: string, value: string) => {
        const newFilters = { ...filters, [key]: value };
        router.get(route('admin.pagamentos.index'), newFilters, { preserveState: true });
    };

    const handleExport = () => {
        const params = new URLSearchParams();
        if (filters.search) params.append('search', filters.search);
        if (filters.status) params.append('status', filters.status);
        if (filters.periodo) params.append('periodo', filters.periodo);

        const exportUrl = route('admin.pagamentos.export') + (params.toString() ? '?' + params.toString() : '');
        window.open(exportUrl, '_blank');
    };

    const getStatusBadge = (status: string) => {
        const colors = {
            pendente: 'bg-yellow-100 text-yellow-800',
            pago: 'bg-green-100 text-green-800',
            cancelado: 'bg-red-100 text-red-800',
            vencido: 'bg-red-100 text-red-800',
        };
        const labels = {
            pendente: 'Pendente',
            pago: 'Pago',
            cancelado: 'Cancelado',
            vencido: 'Vencido',
        };
        return (
            <Badge className={colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>
                {labels[status as keyof typeof labels] || status}
            </Badge>
        );
    };

    const getFormaPagamentoBadge = (method: string) => {
        const colors = {
            credit_card: 'bg-blue-100 text-blue-800',
            debit_card: 'bg-purple-100 text-purple-800',
            pix: 'bg-green-100 text-green-800',
            boleto: 'bg-orange-100 text-orange-800',
        };
        const labels = {
            credit_card: 'Cartão de Crédito',
            debit_card: 'Cartão de Débito',
            pix: 'PIX',
            boleto: 'Boleto',
        };
        return (
            <Badge className={colors[method as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>
                {labels[method as keyof typeof labels] || method}
            </Badge>
        );
    };

    return (
        <AppLayout>
            <Head title="Gestão de Pagamentos" />
            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">Gestão de Pagamentos</h1>
                        <p className="text-gray-600">Gerencie todos os pagamentos e cobranças do sistema</p>
                    </div>
                </div>

                {/* Estatísticas */}
                <div className="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-3 lg:gap-8">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Receita do Mês</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">
                                R$ {stats.total_pago_mes.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                            </div>
                            <p className="text-xs text-muted-foreground">{stats.count_pago_mes} pagamentos confirmados</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-yellow-600">
                                R$ {stats.total_pendente.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                            </div>
                            <p className="text-xs text-muted-foreground">{stats.count_pendente} pagamentos pendentes</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Vencidos</CardTitle>
                            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">
                                R$ {stats.total_vencido.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                            </div>
                            <p className="text-xs text-muted-foreground">{stats.count_vencido} pagamentos vencidos</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Busca e Filtros */}
                <Card className="mb-6">
                    <CardContent className="p-6">
                        <form onSubmit={handleSearch} className="space-y-4">
                            <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
                                <div className="flex-1">
                                    <label htmlFor="search" className="mb-2 block text-sm font-medium text-gray-700">
                                        Buscar Pagamentos
                                    </label>
                                    <Input
                                        id="search"
                                        type="text"
                                        placeholder="Ex.: nome do usuário, email..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>
                                <Button type="submit">
                                    <Search className="mr-2 h-4 w-4" />
                                    Buscar
                                </Button>
                            </div>
                        </form>

                        {/* Filtros rápidos */}
                        <div className="mt-4 flex flex-wrap gap-2">
                            <Button
                                variant={filters.status === 'pendente' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => handleFilterChange('status', 'pendente')}
                            >
                                Pendentes
                            </Button>
                            <Button
                                variant={filters.status === 'pago' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => handleFilterChange('status', 'pago')}
                            >
                                Pagos
                            </Button>
                            <Button
                                variant={filters.periodo === 'vencidos' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => handleFilterChange('periodo', 'vencidos')}
                            >
                                Vencidos
                            </Button>
                            <Button
                                variant={filters.periodo === 'hoje' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => handleFilterChange('periodo', 'hoje')}
                            >
                                Hoje
                            </Button>
                            <Button
                                variant={filters.periodo === 'mes' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => handleFilterChange('periodo', 'mes')}
                            >
                                Este Mês
                            </Button>
                            <Button onClick={handleExport} variant="outline">
                                <Download className="mr-2 h-4 w-4" />
                                Exportar
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Lista de Pagamentos */}
                <Card>
                    <CardHeader>
                        <CardTitle>Pagamentos</CardTitle>
                        <CardDescription>{pagamentos.meta?.total ?? pagamentos.data.length} pagamentos encontrados</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {pagamentos.data.map((pagamento) => (
                                <div key={pagamento.id} className="flex items-center justify-between rounded-lg border p-4 hover:bg-gray-50">
                                    <div className="flex-1">
                                        <div className="mb-2 flex items-center gap-4">
                                            <div className="flex items-center text-sm text-gray-600">
                                                <User className="mr-1 h-4 w-4" />
                                                <span className="font-medium">
                                                    {pagamento.assinatura?.user?.name || 'Usuário não encontrado'}
                                                </span>
                                            </div>
                                            <div className="text-sm text-gray-500">
                                                Serviço de Fisioterapia
                                            </div>
                                            {getStatusBadge(pagamento.status)}
                                            {getFormaPagamentoBadge(pagamento.method)}
                                        </div>

                                        <div className="flex items-center gap-6 text-sm text-gray-600">
                                            <div className="flex items-center">
                                                <Calendar className="mr-1 h-4 w-4" />
                                                <span>Venc: {pagamento.formatted_due_date}</span>
                                            </div>
                                            {(pagamento.paid_at || pagamento.data_pagamento) && (
                                                <div className="flex items-center">
                                                    <CheckCircle className="mr-1 h-4 w-4 text-green-600" />
                                                    <span>Pago: {pagamento.formatted_paid_at}</span>
                                                </div>
                                            )}
                                            <div className="text-lg font-bold text-green-600">{pagamento.formatted_amount}</div>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-2">
                                        <Link href={route('admin.pagamentos.show', pagamento.id)}>
                                            <Button variant="outline" size="sm">
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                        </Link>
                                    </div>
                                </div>
                            ))}

                            {pagamentos.data.length === 0 && (
                                <div className="py-8 text-center text-gray-500">Nenhum pagamento encontrado com os filtros aplicados.</div>
                            )}
                        </div>

                        {/* Paginação */}
                        {pagamentos.links && pagamentos.links.length > 3 && (
                            <div className="mt-6 flex justify-center">
                                <div className="flex gap-2">
                                    {pagamentos.links.map((link, index) => (
                                        <Link
                                            key={index}
                                            href={link.url || '#'}
                                            className={`rounded-md px-3 py-2 text-sm ${
                                                link.active ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                            } ${!link.url ? 'cursor-not-allowed opacity-50' : ''}`}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
