<?php

// Mostrar apenas logs recentes pertinentes (ignorando logs do meu script de teste)
$logFile = __DIR__ . '/storage/logs/laravel.log';

if (!file_exists($logFile)) {
    die("Log file not found!\n");
}

$logContent = file($logFile);

// Filtrar apenas entradas dos últimos minutos que não sejam do meu script de teste
$filteredLines = [];
foreach ($logContent as $line) {
    // Pular entradas relacionadas ao meu script de teste
    if (str_contains($line, 'test_real_request.php') ||
        str_contains($line, 'Target class [web] does not exist')) {
        continue;
    }

    // Incluir apenas entradas de hoje (últimas 24h)
    if (str_contains($line, '[2025-08-29')) {
        $filteredLines[] = $line;
    }
}

// Mostrar últimas 30 linhas filtradas
$last30 = array_slice($filteredLines, -30);
foreach ($last30 as $line) {
    echo $line;
}

echo "\n=== LAST 10 ERRORS ONLY ===\n";

// Filtrar apenas linhas com ERROR
$errorLines = array_filter($last30, function($line) {
    return str_contains($line, 'local.ERROR') ||
           str_contains($line, 'ERROR:') ||
           str_contains($line, 'Fatal error') ||
           str_contains($line, 'PHP Fatal error');
});

if (empty($errorLines)) {
    echo "Nenhum erro encontrado nos logs recentes.\n";
} else {
    foreach (array_slice($errorLines, -10) as $errorLine) {
        echo $errorLine;
    }
}
