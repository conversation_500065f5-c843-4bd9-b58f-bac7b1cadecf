<?php

namespace App\Services;

use App\Models\Pagamento;
use App\Models\Assinatura;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class UnifiedPaymentService
{
    protected $mercadoPagoService;

    public function __construct(MercadoPagoService $mercadoPagoService)
    {
        $this->mercadoPagoService = $mercadoPagoService;
    }

    /**
     * Criar pagamento unificado com todos os métodos disponíveis
     */
    public function createUnifiedPayment(array $data)
    {
        try {
            Log::info('🔄 [UNIFIED] Criando pagamento unificado', [
                'data' => $data,
                'payment_methods' => $data['payment_methods'] ?? 'all'
            ]);

            // Configurar métodos de pagamento baseado na preferência
            $paymentMethods = $this->configurePaymentMethods($data['payment_methods'] ?? 'all');

            $preferenceData = [
                'items' => [
                    [
                        'title' => $data['title'],
                        'description' => $data['description'] ?? '',
                        'quantity' => 1,
                        'currency_id' => 'BRL',
                        'unit_price' => (float) $data['amount'],
                    ]
                ],
                'payer' => [
                    'name' => $data['payer']['name'] ?? '',
                    'email' => $data['payer']['email'] ?? '',
                ],
                'payment_methods' => $paymentMethods,
                'back_urls' => [
                    'success' => $data['success_url'] ?? url('/success'),
                    'failure' => $data['failure_url'] ?? url('/failure'),
                    'pending' => $data['pending_url'] ?? url('/pending'),
                ],
                'external_reference' => $data['external_reference'] ?? null,
                'notification_url' => $data['notification_url'] ?? url('/webhook/mercadopago'),
                'expires' => true,
                'expiration_date_from' => Carbon::now()->toISOString(),
                'expiration_date_to' => Carbon::now()->addHours(24)->toISOString(),
            ];

            // Criar preferência no Mercado Pago
            $preference = $this->mercadoPagoService->createPreference($preferenceData);

            if (!$preference['success']) {
                Log::error('❌ [UNIFIED] Erro ao criar preferência', [
                    'error' => $preference['message'] ?? 'Erro desconhecido'
                ]);
                return $preference;
            }

            Log::info('✅ [UNIFIED] Pagamento unificado criado com sucesso', [
                'preference_id' => $preference['preference_id'],
                'init_point' => $preference['init_point']
            ]);

            return $preference;

        } catch (\Exception $e) {
            Log::error('💥 [UNIFIED] Erro ao criar pagamento unificado', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno do servidor. Tente novamente.',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Configurar métodos de pagamento disponíveis
     */
    private function configurePaymentMethods($type = 'all')
    {
        $config = [
            'installments' => 12,
            'default_installments' => 1,
        ];

        switch ($type) {
            case 'credit_card':
                $config['excluded_payment_types'] = [
                    ['id' => 'debit_card'],
                    ['id' => 'bank_transfer'],
                    ['id' => 'ticket'],
                ];
                break;

            case 'debit_card':
                $config['excluded_payment_types'] = [
                    ['id' => 'credit_card'],
                    ['id' => 'bank_transfer'],
                    ['id' => 'ticket'],
                ];
                break;

            case 'pix':
                $config['excluded_payment_types'] = [
                    ['id' => 'credit_card'],
                    ['id' => 'debit_card'],
                    ['id' => 'ticket'],
                ];
                break;

            case 'boleto':
                $config['excluded_payment_types'] = [
                    ['id' => 'credit_card'],
                    ['id' => 'debit_card'],
                    ['id' => 'bank_transfer'],
                ];
                break;

            case 'all':
            default:
                // Todos os métodos disponíveis - incluindo PIX
                // Não excluímos nenhum método, permitindo PIX (bank_transfer), cartões e boleto
                break;
        }

        return $config;
    }

    /**
     * Criar pagamento específico para PIX
     */
    public function createPixPayment(array $data)
    {
        $data['payment_methods'] = 'pix';
        return $this->createUnifiedPayment($data);
    }

    /**
     * Criar pagamento específico para Boleto
     */
    public function createBoletoPayment(array $data)
    {
        $data['payment_methods'] = 'boleto';
        return $this->createUnifiedPayment($data);
    }

    /**
     * Criar pagamento específico para Cartão de Crédito
     */
    public function createCreditCardPayment(array $data)
    {
        $data['payment_methods'] = 'credit_card';
        return $this->createUnifiedPayment($data);
    }

    /**
     * Criar pagamento específico para Cartão de Débito
     */
    public function createDebitCardPayment(array $data)
    {
        $data['payment_methods'] = 'debit_card';
        return $this->createUnifiedPayment($data);
    }

    /**
     * Obter informações de um pagamento
     */
    public function getPaymentInfo($paymentId)
    {
        return $this->mercadoPagoService->getPayment($paymentId);
    }

    /**
     * Processar webhook de pagamento
     */
    public function processWebhook($paymentId, $requestId = null)
    {
        try {
            Log::info('🔔 [UNIFIED] Processando webhook', [
                'payment_id' => $paymentId,
                'request_id' => $requestId
            ]);

            $paymentInfo = $this->getPaymentInfo($paymentId);

            if (!$paymentInfo) {
                Log::warning('⚠️ [UNIFIED] Pagamento não encontrado', [
                    'payment_id' => $paymentId
                ]);
                return false;
            }

            // Buscar pagamento local
            $externalReference = $paymentInfo['external_reference'] ?? null;
            $pagamento = null;

            if ($externalReference) {
                $pagamento = Pagamento::find($externalReference);
            }

            if (!$pagamento) {
                // Tentar buscar por transaction_id
                $pagamento = Pagamento::where('transaction_id', $paymentInfo['preference_id'] ?? null)->first();
            }

            if (!$pagamento) {
                Log::warning('⚠️ [UNIFIED] Pagamento local não encontrado', [
                    'payment_id' => $paymentId,
                    'external_reference' => $externalReference
                ]);
                return false;
            }

            // Atualizar status do pagamento
            $this->updatePaymentStatus($pagamento, $paymentInfo);

            return true;

        } catch (\Exception $e) {
            Log::error('💥 [UNIFIED] Erro ao processar webhook', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Atualizar status do pagamento baseado na resposta do MP
     */
    private function updatePaymentStatus(Pagamento $pagamento, array $paymentInfo)
    {
        $status = $paymentInfo['status'] ?? 'unknown';
        $paymentMethod = $paymentInfo['payment_method_id'] ?? 'unknown';
        $paymentType = $paymentInfo['payment_type_id'] ?? 'unknown';
        $paymentId = $paymentInfo['id'] ?? null;
        $preferenceId = $paymentInfo['preference_id'] ?? null;

        $newStatus = $this->mapMercadoPagoStatus($status);

        Log::info('🔄 [UNIFIED] Atualizando status do pagamento', [
            'pagamento_id' => $pagamento->id,
            'old_status' => $pagamento->status,
            'new_status' => $newStatus,
            'mp_status' => $status,
            'payment_method' => $paymentMethod,
            'payment_type' => $paymentType,
            'payment_id' => $paymentId,
            'preference_id' => $preferenceId
        ]);

        $updateData = [
            'status' => $newStatus,
            'method' => $this->mapPaymentMethod($paymentMethod, $paymentType),
            'gateway_response' => $paymentInfo,
        ];

        // Atualizar payment_id quando o pagamento é efetivamente realizado
        if ($paymentId && $newStatus === 'pago') {
            $updateData['payment_id'] = $paymentId;
            $updateData['paid_at'] = Carbon::now();
        }

        // Atualizar preference_id se estiver disponível e não estiver salvo
        if ($preferenceId && !$pagamento->preference_id) {
            $updateData['preference_id'] = $preferenceId;
        }

        $pagamento->update($updateData);

        // Log da atualização
        Log::info('✅ [UNIFIED] Status do pagamento atualizado', [
            'pagamento_id' => $pagamento->id,
            'status' => $newStatus,
            'method' => $updateData['method'],
            'payment_id' => $updateData['payment_id'] ?? null,
            'preference_id' => $updateData['preference_id'] ?? null
        ]);
    }

    /**
     * Mapear status do Mercado Pago para status interno
     */
    private function mapMercadoPagoStatus($mpStatus)
    {
        return match($mpStatus) {
            'approved' => 'pago',
            'pending', 'in_process', 'in_mediation' => 'pendente',
            'rejected', 'cancelled', 'refunded', 'charged_back' => 'falhou',
            default => 'pendente'
        };
    }

    /**
     * Mapear método de pagamento do MP para formato interno
     */
    private function mapPaymentMethod($paymentMethodId, $paymentTypeId)
    {
        // PIX
        if ($paymentMethodId === 'pix' || $paymentTypeId === 'bank_transfer') {
            return 'pix';
        }

        // Boleto
        if ($paymentTypeId === 'ticket') {
            return 'boleto';
        }

        // Cartões de débito
        if ($paymentTypeId === 'debit_card') {
            return 'cartao_debito';
        }

        // Mapear bandeiras de cartão de crédito para o formato interno
        $creditCardMethods = ['visa', 'master', 'amex', 'elo', 'hipercard'];
        if (in_array($paymentMethodId, $creditCardMethods) || $paymentTypeId === 'credit_card') {
            return 'cartao_credito';
        }

        // Cartões de crédito (padrão)
        return 'cartao_credito';
    }

    /**
     * Obter métodos de pagamento disponíveis
     */
    public function getAvailablePaymentMethods()
    {
        return [
            'cartao_credito' => [
                'name' => 'Cartão de Crédito',
                'icon' => 'CreditCard',
                'description' => 'Visa, Mastercard, Elo, Amex',
                'installments' => true,
                'instant' => true,
            ],
            'cartao_debito' => [
                'name' => 'Cartão de Débito',
                'icon' => 'CreditCard',
                'description' => 'Débito online',
                'installments' => false,
                'instant' => true,
            ],
            'pix' => [
                'name' => 'PIX',
                'icon' => 'QrCode',
                'description' => 'Pagamento instantâneo',
                'installments' => false,
                'instant' => true,
            ],
            'boleto' => [
                'name' => 'Boleto Bancário',
                'icon' => 'FileText',
                'description' => 'Vencimento em 3 dias úteis',
                'installments' => false,
                'instant' => false,
            ],
        ];
    }
}
