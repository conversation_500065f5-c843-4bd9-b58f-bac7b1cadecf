/**
 * Helper function to generate routes without <PERSON>iggy dependency
 */
export function safeRoute(name: string, params?: any, absolute?: boolean): string {
    return generateFallbackRoute(name, params);
}

/**
 * Generate fallback route when Ziggy is not available
 */
function generateFallbackRoute(name: string, params?: any): string {
    const routeMap: Record<string, string> = {
        'admin.usuarios.index': '/admin/usuarios',
        'admin.usuarios.create': '/admin/usuarios/create',
        'admin.usuarios.store': '/admin/usuarios',
        'admin.usuarios.show': '/admin/usuarios/{id}',
        'admin.usuarios.edit': '/admin/usuarios/{id}/edit',
        'admin.usuarios.update': '/admin/usuarios/{id}',
        'admin.usuarios.destroy': '/admin/usuarios/{id}',
        'admin.usuarios.history': '/admin/usuarios/{id}/history',
        'admin.usuarios.export': '/admin/usuarios-export',
        'admin.fisioterapeutas.index': '/admin/fisioterapeutas',
        'admin.fisioterapeutas.create': '/admin/fisioterapeutas/create',
        'admin.fisioterapeutas.store': '/admin/fisioterapeutas',
        'admin.fisioterapeutas.show': '/admin/fisioterapeutas/{id}',
        'admin.fisioterapeutas.edit': '/admin/fisioterapeutas/{id}/edit',
        'admin.fisioterapeutas.update': '/admin/fisioterapeutas/{id}',
        'admin.fisioterapeutas.destroy': '/admin/fisioterapeutas/{id}',
        'admin.fisioterapeutas.approve': '/admin/fisioterapeutas/{id}/approve',
        'admin.fisioterapeutas.reject': '/admin/fisioterapeutas/{id}/reject',
        'admin.pagamentos.index': '/admin/pagamentos',
        'admin.pagamentos.show': '/admin/pagamentos/{id}',
        'admin.pagamentos.update': '/admin/pagamentos/{id}',
        'admin.pagamentos.mark-as-paid': '/admin/pagamentos/{id}/mark-as-paid',
        'admin.pagamentos.mark-as-failed': '/admin/pagamentos/{id}/mark-as-failed',
        'admin.pagamentos.cancel': '/admin/pagamentos/{id}/cancel',
        'admin.estabelecimentos.index': '/admin/estabelecimentos',
        'admin.estabelecimentos.create': '/admin/estabelecimentos/create',
        'admin.estabelecimentos.store': '/admin/estabelecimentos',
        'admin.estabelecimentos.show': '/admin/estabelecimentos/{id}',
        'admin.estabelecimentos.edit': '/admin/estabelecimentos/{id}/edit',
        'admin.estabelecimentos.update': '/admin/estabelecimentos/{id}',
        'admin.estabelecimentos.destroy': '/admin/estabelecimentos/{id}',
        'admin.estabelecimentos.toggle-ativo': '/admin/estabelecimentos/{id}/toggle-ativo',
        'admin.estabelecimentos.toggle-plano': '/admin/estabelecimentos/{id}/toggle-plano',
    };

    let route = routeMap[name];
    if (!route) {
        console.warn(`Fallback route not found for ${name}`);
        return '#';
    }

    // Replace parameters
    if (params) {
        if (typeof params === 'object') {
            Object.keys(params).forEach((key) => {
                route = route.replace(`{${key}}`, params[key]);
            });
        } else {
            // Single parameter (usually ID)
            route = route.replace('{id}', params);
        }
    }

    return route;
}

/**
 * Check if route function is available
 */
export function isRouteAvailable(): boolean {
    return true; // Always available with fallback routes
}
