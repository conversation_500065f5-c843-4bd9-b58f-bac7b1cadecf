import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import PublicLayout from '@/layouts/public-layout';
import { usePage } from '@inertiajs/react';
import {
    ArrowRight,
    Calculator,
    CheckCircle,
    Clock,
    DollarSign,
    FileText,
    Handshake,
    Link as LinkIcon,
    Settings,
    UserCheck,
    Users,
} from 'lucide-react';

export default function Afiliados() {
    const { auth } = usePage().props as any;
    const user = auth?.user;
    const beneficios = [
        'Botão para cadastro de afiliado com geração de link exclusivo',
        'Sistema básico de afiliado: cada indicação convertida atribui comissão fixa + ganhos recorrentes',
        'Acompanhamento em tempo real das suas indicações e comissões',
        'Pagamentos mensais automáticos via PIX',
        'Suporte dedicado para afiliados',
        'Material de divulgação personalizado',
    ];

    return (
        <PublicLayout
            title="Programa de Afiliados - F4 Fisio"
            description="Torne-se um afiliado F4 Fisio e ganhe comissões indicando nossos serviços de fisioterapia domiciliar."
        >
            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-20 md:py-36">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-5xl font-medium text-balance md:text-6xl">
                                Programa de<span className="block text-primary">Afiliados F4 Fisio</span>
                            </h1>
                            <p className="mx-auto my-8 max-w-3xl text-xl text-balance text-muted-foreground">
                                Ganhe comissões indicando nossos serviços de fisioterapia domiciliar. Quanto mais clientes você indica, mais você
                                ganha!
                            </p>
                            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
                                {user ? (
                                    // Usuário logado
                                    user.hasAfiliadoProfile ? (
                                        // Já tem perfil de afiliado
                                        user.canSwitchToAfiliadoMode ? (
                                            // Pode acessar modo afiliado
                                            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
                                                <Button size="lg" className="px-8 py-4 text-lg" asChild>
                                                    <a href={route('afiliado.dashboard')}>
                                                        <UserCheck className="mr-2 h-5 w-5" />
                                                        Acessar Dashboard
                                                    </a>
                                                </Button>
                                                <Button variant="outline" size="lg" className="px-8 py-4 text-lg" asChild>
                                                    <a href={route('afiliado.materiais')}>
                                                        <Calculator className="mr-2 h-5 w-5" />
                                                        Materiais de Divulgação
                                                    </a>
                                                </Button>
                                            </div>
                                        ) : (
                                            // Perfil pendente ou inativo
                                            <div className="text-center">
                                                <Badge variant="secondary" className="mb-4 px-4 py-2 text-lg">
                                                    Status: {user.afiliado?.status || 'Pendente'}
                                                </Badge>
                                                <p className="mb-4 text-muted-foreground">
                                                    Seu cadastro de afiliado está sendo analisado. Você receberá um e-mail quando for aprovado.
                                                </p>
                                                <Button variant="outline" size="lg" className="px-8 py-4 text-lg" asChild>
                                                    <a href={route('dashboard')}>
                                                        <Settings className="mr-2 h-5 w-5" />
                                                        Voltar ao Dashboard
                                                    </a>
                                                </Button>
                                            </div>
                                        )
                                    ) : (
                                        // Não tem perfil de afiliado
                                        <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
                                            <Button size="lg" className="px-8 py-4 text-lg" asChild>
                                                <a href={route('dashboard')}>
                                                    <Users className="mr-2 h-5 w-5" />
                                                    Acessar Dashboard
                                                </a>
                                            </Button>
                                            <Button variant="outline" size="lg" className="px-8 py-4 text-lg" asChild>
                                                <a href={route('contato')}>
                                                    <Settings className="mr-2 h-5 w-5" />
                                                    Falar com Suporte
                                                </a>
                                            </Button>
                                        </div>
                                    )
                                ) : (
                                    // Usuário não logado
                                    <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
                                        <Button size="lg" className="px-8 py-4 text-lg" asChild>
                                            <a href={route('login')}>
                                                <Users className="mr-2 h-5 w-5" />
                                                Fazer Login
                                            </a>
                                        </Button>
                                        <Button variant="outline" size="lg" className="px-8 py-4 text-lg" asChild>
                                            <a href={route('register')}>
                                                <Calculator className="mr-2 h-5 w-5" />
                                                Criar Conta
                                            </a>
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Como Funciona - Guia Passo a Passo */}
            <section className="bg-muted/30 py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mb-16 text-center">
                        <h2 className="mb-4 text-3xl font-medium text-balance md:text-4xl">Como Funciona o Programa de Afiliados</h2>
                        <p className="mx-auto max-w-2xl text-lg text-muted-foreground">Siga estes passos simples para começar a ganhar comissões indicando nossos serviços</p>
                    </div>

                    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
                        {/* Passo 1 */}
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="size-12" className="mb-6">
                                <FileText className="h-6 w-6" />
                            </Badge>
                            <h3 className="mb-3 text-xl font-semibold">1. Cadastre-se</h3>
                            <p className="text-sm leading-relaxed text-muted-foreground">
                                Preencha o formulário de cadastro com suas informações pessoais e experiência
                            </p>
                        </div>

                        {/* Passo 2 */}
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="size-12" className="mb-6">
                                <Clock className="h-6 w-6" />
                            </Badge>
                            <h3 className="mb-3 text-xl font-semibold">2. Aguarde Aprovação</h3>
                            <p className="text-sm leading-relaxed text-muted-foreground">
                                Nossa equipe analisará seu perfil em até 48 horas e você receberá um e-mail com o resultado
                            </p>
                        </div>

                        {/* Passo 3 */}
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="size-12" className="mb-6">
                                <Handshake className="h-6 w-6" />
                            </Badge>
                            <h3 className="mb-3 text-xl font-semibold">3. Comece a Vender</h3>
                            <p className="text-sm leading-relaxed text-muted-foreground">
                                Acesse seu dashboard, pegue seu link exclusivo e materiais de divulgação para começar
                            </p>
                        </div>

                        {/* Passo 4 */}
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="size-12" className="mb-6">
                                <DollarSign className="h-6 w-6" />
                            </Badge>
                            <h3 className="mb-3 text-xl font-semibold">4. Receba Comissões</h3>
                            <p className="text-sm leading-relaxed text-muted-foreground">
                                Ganhe comissões por cada indicação convertida e acompanhe seus ganhos em tempo real no dashboard
                            </p>
                        </div>
                    </div>

                    {/* CTA para começar */}
                    <div className="mt-16 text-center">
                        <Button size="lg" className="px-8 py-4 text-lg" asChild>
                            <a href={user?.hasAfiliadoProfile ? route('afiliado.dashboard') : user ? route('dashboard') : route('login')}>
                                {user?.hasAfiliadoProfile ? 'Acessar Dashboard' : user ? 'Ir para Dashboard' : 'Fazer Login'}
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </a>
                        </Button>
                    </div>
                </div>
            </section>

            {/* Seção de comissões (simplificada, sem planos) */}
            <section className="bg-background py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mb-10 text-center">
                        <h2 className="mb-4 text-3xl font-medium text-balance md:text-4xl">Comissões por Indicação</h2>
                        <p className="mx-auto max-w-3xl text-xl text-muted-foreground">
                            Receba comissão por cada cliente indicado que contrata nossos serviços. Acompanhe tudo em tempo real pelo dashboard.
                        </p>
                    </div>

                    <div className="grid gap-8 md:grid-cols-3">
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-xl">Consulta Inicial</CardTitle>
                                <CardDescription>Comissão fixa por cada consulta indicada concluída</CardDescription>
                            </CardHeader>
                        </Card>
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-xl">Pacotes de Atendimento</CardTitle>
                                <CardDescription>Comissões proporcionais conforme o pacote contratado</CardDescription>
                            </CardHeader>
                        </Card>
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-xl">Recorrência</CardTitle>
                                <CardDescription>Ganhos recorrentes enquanto o contrato estiver ativo</CardDescription>
                            </CardHeader>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Benefícios */}
            <section className="bg-background py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="grid gap-12 lg:grid-cols-2 lg:items-center">
                        <div>
                            <h2 className="mb-6 text-3xl font-medium text-balance md:text-4xl">Benefícios do Programa</h2>
                            <div className="space-y-4">
                                {beneficios.map((beneficio, index) => (
                                    <div key={index} className="flex items-start gap-3">
                                        <CheckCircle className="mt-0.5 h-6 w-6 flex-shrink-0 text-green-500" />
                                        <p className="text-muted-foreground">{beneficio}</p>
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div className="relative">
                            <Card className="p-8">
                                <div className="text-center">
                                    <div className="mb-6">
                                        <div className="mb-2 text-4xl font-bold text-primary">R$ 2.500+</div>
                                        <p className="text-lg text-muted-foreground">Ganho médio mensal</p>
                                    </div>
                                    <div className="grid grid-cols-2 gap-6 text-sm">
                                        <div>
                                            <div className="text-2xl font-bold text-primary">50+</div>
                                            <p className="text-muted-foreground">Afiliados Ativos</p>
                                        </div>
                                        <div>
                                            <div className="text-2xl font-bold text-primary">95%</div>
                                            <p className="text-muted-foreground">Satisfação</p>
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        </div>
                    </div>
                </div>
            </section>

            {/* Requisitos */}
            <section className="bg-background py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mb-16 text-center">
                        <h2 className="mb-4 text-3xl font-medium text-balance md:text-4xl">Requisitos para ser Afiliado</h2>
                        <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
                            Confira os requisitos básicos para participar do nosso programa de afiliados
                        </p>
                    </div>

                    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                    Idade Mínima
                                </CardTitle>
                                <CardDescription>Ter pelo menos 18 anos de idade</CardDescription>
                            </CardHeader>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                    Documentação
                                </CardTitle>
                                <CardDescription>CPF válido e dados pessoais atualizados</CardDescription>
                            </CardHeader>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                    Canais de Divulgação
                                </CardTitle>
                                <CardDescription>Ter pelo menos um canal para divulgar (redes sociais, blog, etc.)</CardDescription>
                            </CardHeader>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                    Comprometimento
                                </CardTitle>
                                <CardDescription>Disponibilidade para promover ativamente os serviços</CardDescription>
                            </CardHeader>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                    Ética Profissional
                                </CardTitle>
                                <CardDescription>Seguir nossas diretrizes de marketing e conduta</CardDescription>
                            </CardHeader>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                    Conta Bancária
                                </CardTitle>
                                <CardDescription>Conta bancária ou PIX para recebimento das comissões</CardDescription>
                            </CardHeader>
                        </Card>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="bg-muted/30 py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mx-auto max-w-4xl rounded-2xl border bg-card p-6 text-center shadow-sm sm:p-8">
                        <h2 className="mb-4 text-2xl font-medium text-card-foreground">Pronto para Começar a Ganhar?</h2>
                        <p className="mb-8 text-xl text-balance text-muted-foreground">
                            {user
                                ? 'Acesse sua dashboard para se cadastrar como afiliado e começar a ganhar comissões.'
                                : 'Faça login ou crie uma conta para se cadastrar como afiliado.'}
                        </p>
                        <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
                            {user && user.hasAfiliadoProfile && user.canSwitchToAfiliadoMode ? (
                                // Usuário já é afiliado aprovado
                                <Button size="lg" className="px-8 py-4 text-lg" asChild>
                                    <a href={route('afiliado.dashboard')}>
                                        <UserCheck className="mr-2 h-5 w-5" />
                                        Acessar Dashboard
                                    </a>
                                </Button>
                            ) : user ? (
                                // Usuário logado mas não é afiliado
                                <Button size="lg" className="px-8 py-4 text-lg" asChild>
                                    <a href={route('dashboard')}>
                                        <Users className="mr-2 h-5 w-5" />
                                        Ir para Dashboard
                                    </a>
                                </Button>
                            ) : (
                                // Usuário não logado
                                <Button size="lg" className="px-8 py-4 text-lg" asChild>
                                    <a href={route('login')}>
                                        <Users className="mr-2 h-5 w-5" />
                                        Fazer Login
                                    </a>
                                </Button>
                            )}
                            <Button variant="outline" size="lg" className="px-8 py-4 text-lg" asChild>
                                <a href={route('contato')}>
                                    <LinkIcon className="mr-2 h-5 w-5" />
                                    Falar com Suporte
                                </a>
                            </Button>
                        </div>
                    </div>
                </div>
            </section>

            {/* FAQ */}
            <section className="bg-muted/30 py-16">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-bold">Perguntas Frequentes</h2>
                        <p className="mx-auto mt-4 max-w-2xl text-lg text-muted-foreground">Tire suas dúvidas sobre o programa de afiliados</p>
                    </div>

                    <div className="mt-12 grid gap-8 md:grid-cols-2">
                        <div>
                            <h3 className="mb-2 text-lg font-semibold">Como funciona a comissão?</h3>
                            <p className="text-muted-foreground">
                                Você recebe uma comissão por cada contratação realizada através do seu link, além de ganhos recorrentes
                                enquanto o cliente mantiver os serviços ativos.
                            </p>
                        </div>

                        <div>
                            <h3 className="mb-2 text-lg font-semibold">Quando recebo o pagamento?</h3>
                            <p className="text-muted-foreground">
                                Os pagamentos são realizados mensalmente via PIX, sempre até o dia 5 do mês seguinte.
                            </p>
                        </div>

                        <div>
                            <h3 className="mb-2 text-lg font-semibold">Preciso ter experiência em vendas?</h3>
                            <p className="text-muted-foreground">
                                Não! Fornecemos todo o material de apoio e suporte necessário para você começar a indicar nossos serviços, independente
                                da sua experiência.
                            </p>
                        </div>

                        <div>
                            <h3 className="mb-2 text-lg font-semibold">Como alternar entre os modos?</h3>
                            <p className="text-muted-foreground">
                                Após aprovado, você verá um botão "Modo Afiliado/Normal" no menu lateral. Clique para alternar entre sua conta normal
                                e o painel de afiliado.
                            </p>
                        </div>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
