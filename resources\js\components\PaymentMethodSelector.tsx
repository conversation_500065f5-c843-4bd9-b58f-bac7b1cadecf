import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
    CreditCard, 
    QrCode, 
    FileText, 
    Clock, 
    Zap, 
    CheckCircle,
    AlertCircle,
    Loader2
} from 'lucide-react';

interface PaymentMethod {
    id: string;
    name: string;
    icon: string;
    description: string;
    installments: boolean;
    instant: boolean;
    fees?: string;
    processingTime?: string;
}

interface PaymentMethodSelectorProps {
    amount: number;
    onMethodSelect: (method: string) => void;
    selectedMethod?: string;
    loading?: boolean;
    disabled?: boolean;
    availableMethods?: string[];
}

const paymentMethods: Record<string, PaymentMethod> = {
    cartao_credito: {
        id: 'cartao_credito',
        name: '<PERSON><PERSON><PERSON> Crédito',
        icon: 'CreditCard',
        description: 'Visa, Mastercard, Elo, Amex',
        installments: true,
        instant: true,
        fees: 'Taxa: 3,99%',
        processingTime: 'Aprovação instantânea'
    },
    cartao_debito: {
        id: 'cartao_debito',
        name: 'Cartão de Débito',
        icon: 'CreditCard',
        description: 'Débito online',
        installments: false,
        instant: true,
        fees: 'Taxa: 2,99%',
        processingTime: 'Aprovação instantânea'
    },
    pix: {
        id: 'pix',
        name: 'PIX',
        icon: 'QrCode',
        description: 'Pagamento instantâneo',
        installments: false,
        instant: true,
        fees: 'Sem taxa',
        processingTime: 'Aprovação em segundos'
    },
    boleto: {
        id: 'boleto',
        name: 'Boleto Bancário',
        icon: 'FileText',
        description: 'Vencimento em 3 dias úteis',
        installments: false,
        instant: false,
        fees: 'Taxa: R$ 2,50',
        processingTime: 'Até 2 dias úteis'
    }
};

const iconMap = {
    CreditCard,
    QrCode,
    FileText,
    Clock,
    Zap,
    CheckCircle,
    AlertCircle
};

export default function PaymentMethodSelector({
    amount,
    onMethodSelect,
    selectedMethod,
    loading = false,
    disabled = false,
    availableMethods = ['cartao_credito', 'cartao_debito', 'pix', 'boleto']
}: PaymentMethodSelectorProps) {
    const [hoveredMethod, setHoveredMethod] = useState<string | null>(null);

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(value);
    };

    const getInstallmentOptions = (method: PaymentMethod) => {
        if (!method.installments) return null;

        const maxInstallments = 12;
        const installmentValue = amount / maxInstallments;

        return (
            <div className="mt-2 text-xs text-gray-600">
                <p>Até {maxInstallments}x de {formatCurrency(installmentValue)}</p>
            </div>
        );
    };

    const getMethodIcon = (iconName: string) => {
        const IconComponent = iconMap[iconName as keyof typeof iconMap];
        return IconComponent ? <IconComponent className="h-6 w-6" /> : <CreditCard className="h-6 w-6" />;
    };

    const getMethodBadges = (method: PaymentMethod) => {
        const badges = [];

        if (method.instant) {
            badges.push(
                <Badge key="instant" variant="secondary" className="bg-green-100 text-green-800">
                    <Zap className="mr-1 h-3 w-3" />
                    Instantâneo
                </Badge>
            );
        }

        if (method.installments) {
            badges.push(
                <Badge key="installments" variant="secondary" className="bg-blue-100 text-blue-800">
                    Parcelável
                </Badge>
            );
        }

        if (method.id === 'pix') {
            badges.push(
                <Badge key="no-fee" variant="secondary" className="bg-purple-100 text-purple-800">
                    Sem taxa
                </Badge>
            );
        }

        return badges;
    };

    return (
        <div className="space-y-4">
            <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900">
                    Escolha a forma de pagamento
                </h3>
                <p className="text-sm text-gray-600">
                    Valor total: <span className="font-semibold">{formatCurrency(amount)}</span>
                </p>
            </div>

            <Separator />

            <div className="grid gap-4 md:grid-cols-2">
                {availableMethods.map((methodId) => {
                    const method = paymentMethods[methodId];
                    if (!method) return null;

                    const isSelected = selectedMethod === method.id;
                    const isHovered = hoveredMethod === method.id;

                    return (
                        <Card
                            key={method.id}
                            className={`cursor-pointer transition-all duration-200 ${
                                isSelected
                                    ? 'ring-2 ring-blue-500 border-blue-500 bg-blue-50'
                                    : isHovered
                                    ? 'border-gray-300 shadow-md'
                                    : 'border-gray-200 hover:border-gray-300'
                            } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onMouseEnter={() => !disabled && setHoveredMethod(method.id)}
                            onMouseLeave={() => setHoveredMethod(null)}
                            onClick={() => !disabled && !loading && onMethodSelect(method.id)}
                        >
                            <CardHeader className="pb-3">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className={`p-2 rounded-lg ${
                                            isSelected ? 'bg-blue-100' : 'bg-gray-100'
                                        }`}>
                                            {getMethodIcon(method.icon)}
                                        </div>
                                        <div>
                                            <CardTitle className="text-base">{method.name}</CardTitle>
                                            <CardDescription className="text-sm">
                                                {method.description}
                                            </CardDescription>
                                        </div>
                                    </div>
                                    {isSelected && (
                                        <CheckCircle className="h-5 w-5 text-blue-500" />
                                    )}
                                </div>
                            </CardHeader>

                            <CardContent className="pt-0">
                                <div className="space-y-2">
                                    {/* Badges */}
                                    <div className="flex flex-wrap gap-1">
                                        {getMethodBadges(method)}
                                    </div>

                                    {/* Informações adicionais */}
                                    <div className="text-xs text-gray-600 space-y-1">
                                        {method.fees && (
                                            <p className="flex items-center">
                                                <span className="font-medium">💰</span>
                                                <span className="ml-1">{method.fees}</span>
                                            </p>
                                        )}
                                        {method.processingTime && (
                                            <p className="flex items-center">
                                                <Clock className="h-3 w-3 mr-1" />
                                                {method.processingTime}
                                            </p>
                                        )}
                                    </div>

                                    {/* Opções de parcelamento */}
                                    {getInstallmentOptions(method)}
                                </div>
                            </CardContent>
                        </Card>
                    );
                })}
            </div>

            {/* Botão de confirmação */}
            {selectedMethod && (
                <div className="mt-6">
                    <Button
                        onClick={() => onMethodSelect(selectedMethod)}
                        disabled={loading || disabled}
                        className="w-full"
                        size="lg"
                    >
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Processando...
                            </>
                        ) : (
                            <>
                                Pagar {formatCurrency(amount)} com {paymentMethods[selectedMethod]?.name}
                            </>
                        )}
                    </Button>
                </div>
            )}

            {/* Informações de segurança */}
            <div className="mt-6 text-center">
                <div className="flex items-center justify-center space-x-2 text-xs text-gray-500">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Pagamento seguro processado pelo Mercado Pago</span>
                </div>
                <p className="mt-1 text-xs text-gray-400">
                    Seus dados são protegidos com criptografia SSL
                </p>
            </div>
        </div>
    );
}
