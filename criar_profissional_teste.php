<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Fisioterapeuta;
use App\Models\Estabelecimento;
use App\Models\HorarioBase;
use App\Models\Disponibilidade;
use App\Models\MedicoFeriadosConfig;
use App\Models\Feriado;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

try {
    // Conectar ao banco de dados usando a configuração do Laravel
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    echo "=== Criando Profissional de Teste ===\n\n";

    // Verificar se já existe um profissional de teste
    $emailTeste = '<EMAIL>';
    $userExistente = User::where('email', $emailTeste)->first();

    if ($userExistente) {
        echo "Profissional de teste já existe. Removendo dados existentes...\n";
        
        // Remover dados existentes em ordem correta
        DB::table('disponibilidades')->where('fisioterapeuta_id', $userExistente->id)->delete();
        DB::table('horarios_base')->where('fisioterapeuta_id', $userExistente->id)->delete();
        DB::table('estabelecimentos')->where('user_id', $userExistente->id)->delete();
        DB::table('fisioterapeutas')->where('user_id', $userExistente->id)->delete();
        $userExistente->delete();
        
        echo "Dados existentes removidos.\n\n";
    }

    echo "1. Criando usuário base...\n";
    
    // Criar usuário base
    $user = User::create([
        'name' => 'Dr. João Silva',
        'email' => $emailTeste,
        'password' => Hash::make('password123'),
        'role' => 'fisioterapeuta',
        'phone' => '(11) 99999-8888',
        'birth_date' => '1980-05-15',
        'gender' => 'masculino',
        'address' => 'Rua das Clínicas, 123',
        'city' => 'São Paulo',
        'state' => 'SP',
        'zip_code' => '01234-567',
        'active' => true,
        'email_verified_at' => now(),
    ]);

    echo "✓ Usuário criado: ID {$user->id}\n\n";

    echo "2. Criando perfil de fisioterapeuta...\n";
    
    // Criar perfil de fisioterapeuta
    $fisioterapeuta = Fisioterapeuta::create([
        'user_id' => $user->id,
        'crefito' => '12345-SP',
        'specializations' => [
            'Fisioterapia Ortopédica',
            'Fisioterapia Desportiva',
            'Fisioterapia Traumato-Ortopédica'
        ],
        'bio' => 'Fisioterapeuta com mais de 15 anos de experiência, especializado em tratamento de lesões esportivas e recuperação pós-cirúrgica. Atendo pacientes de todas as idades com foco em reabilitação funcional e prevenção de lesões.',
        'hourly_rate' => 150.00,
        'session_rate' => 120.00,
        'travel_fee' => 50.00,
        'service_rates' => [
            'avaliacao_inicial' => 150.00,
            'sessao_individual' => 120.00,
            'pacote_5_sessoes' => 550.00,
            'pacote_10_sessoes' => 1000.00,
            'atendimento_domiciliar' => 170.00
        ],
        'pricing_mode' => 'por_sessao',
        'available_areas' => [
            'São Paulo - Zona Sul',
            'São Paulo - Zona Oeste',
            'Santo André',
            'São Bernardo do Campo'
        ],
        'working_hours' => [
            'segunda' => ['08:00', '18:00'],
            'terca' => ['08:00', '18:00'],
            'quarta' => ['08:00', '18:00'],
            'quinta' => ['08:00', '18:00'],
            'sexta' => ['08:00', '17:00'],
            'sabado' => ['08:00', '12:00']
        ],
        'available' => true,
        'rating' => 4.8,
        'total_reviews' => 127,
        'status' => 'approved',
    ]);

    echo "✓ Perfil de fisioterapeuta criado\n\n";

    echo "3. Criando estabelecimento...\n";
    
    // Criar estabelecimento
    $estabelecimento = Estabelecimento::create([
        'user_id' => $user->id,
        'nome' => 'Clínica de Fisioterapia Dr. João Silva',
        'categoria' => 'fisioterapia',
        'descricao' => 'Clínica especializada em fisioterapia ortopédica e desportiva, equipada com moderna tecnologia e equipe qualificada.',
        'telefone' => '(11) 3333-4444',
        'whatsapp' => '(11) 99999-8888',
        'email' => $emailTeste,
        'endereco' => 'Rua das Clínicas, 123',
        'cidade' => 'São Paulo',
        'estado' => 'SP',
        'cep' => '01234567',
        'latitude' => -23.5505,
        'longitude' => -46.6333,
        'horario_funcionamento' => json_encode([
            'segunda' => ['08:00', '18:00'],
            'terca' => ['08:00', '18:00'],
            'quarta' => ['08:00', '18:00'],
            'quinta' => ['08:00', '18:00'],
            'sexta' => ['08:00', '17:00'],
            'sabado' => ['08:00', '12:00'],
            'domingo' => null
        ]),
        'servicos_oferecidos' => 'Fisioterapia Ortopédica, Fisioterapia Desportiva, Reabilitação Pós-Cirúrgica, Pilates Clínico, Acupuntura, RPG - Reeducação Postural Global',
        'site' => 'https://clinicajoao.exemplo.com',
        'instagram' => '@clinicajoao',
        'facebook' => 'ClinicaJoaoSilva',
        'ativo' => true,
        'plano_ativo' => true,
        'plano_vencimento' => now()->addMonths(12),
        'avaliacao_media' => 4.8,
        'total_avaliacoes' => 127,
    ]);

    echo "✓ Estabelecimento criado\n\n";

    echo "4. Criando horários base...\n";
    
    // Criar horários base para cada dia da semana
    $horariosBase = [
        ['dia_semana' => 1, 'hora_inicio' => '08:00', 'hora_fim' => '12:00', 'periodo_nome' => 'Manhã'],
        ['dia_semana' => 1, 'hora_inicio' => '14:00', 'hora_fim' => '18:00', 'periodo_nome' => 'Tarde'],
        ['dia_semana' => 2, 'hora_inicio' => '08:00', 'hora_fim' => '12:00', 'periodo_nome' => 'Manhã'],
        ['dia_semana' => 2, 'hora_inicio' => '14:00', 'hora_fim' => '18:00', 'periodo_nome' => 'Tarde'],
        ['dia_semana' => 3, 'hora_inicio' => '08:00', 'hora_fim' => '12:00', 'periodo_nome' => 'Manhã'],
        ['dia_semana' => 3, 'hora_inicio' => '14:00', 'hora_fim' => '18:00', 'periodo_nome' => 'Tarde'],
        ['dia_semana' => 4, 'hora_inicio' => '08:00', 'hora_fim' => '12:00', 'periodo_nome' => 'Manhã'],
        ['dia_semana' => 4, 'hora_inicio' => '14:00', 'hora_fim' => '18:00', 'periodo_nome' => 'Tarde'],
        ['dia_semana' => 5, 'hora_inicio' => '08:00', 'hora_fim' => '12:00', 'periodo_nome' => 'Manhã'],
        ['dia_semana' => 5, 'hora_inicio' => '14:00', 'hora_fim' => '17:00', 'periodo_nome' => 'Tarde'],
        ['dia_semana' => 6, 'hora_inicio' => '08:00', 'hora_fim' => '12:00', 'periodo_nome' => 'Sábado'],
    ];

    foreach ($horariosBase as $horario) {
        HorarioBase::create([
            'fisioterapeuta_id' => $user->id,
            'dia_semana' => $horario['dia_semana'],
            'hora_inicio' => $horario['hora_inicio'],
            'hora_fim' => $horario['hora_fim'],
            'periodo_nome' => $horario['periodo_nome'],
            'ativo' => true,
        ]);
    }

    echo "✓ " . count($horariosBase) . " horários base criados\n\n";

    echo "5. Criando disponibilidades específicas...\n";
    
    // Criar disponibilidades para os próximos 30 dias
    $dataInicio = now();
    $dataFim = now()->addDays(30);

    // Disponibilidade geral para todos os dias úteis
    Disponibilidade::create([
        'fisioterapeuta_id' => $user->id,
        'tipo' => 'disponivel',
        'data_inicio' => $dataInicio,
        'data_fim' => $dataFim,
        'hora_inicio' => '08:00',
        'hora_fim' => '18:00',
        'dias_semana' => [1, 2, 3, 4, 5], // Segunda a Sexta
        'recorrente' => true,
        'motivo' => 'Horário comercial normal',
        'ativo' => true,
    ]);

    // Disponibilidade para sábados
    Disponibilidade::create([
        'fisioterapeuta_id' => $user->id,
        'tipo' => 'disponivel',
        'data_inicio' => $dataInicio,
        'data_fim' => $dataFim,
        'hora_inicio' => '08:00',
        'hora_fim' => '12:00',
        'dias_semana' => [6], // Sábado
        'recorrente' => true,
        'motivo' => 'Horário de sábado',
        'ativo' => true,
    ]);

    // Bloquear alguns horários específicos (ex: almoço)
    Disponibilidade::create([
        'fisioterapeuta_id' => $user->id,
        'tipo' => 'bloqueio',
        'data_inicio' => $dataInicio,
        'data_fim' => $dataFim,
        'hora_inicio' => '12:00',
        'hora_fim' => '14:00',
        'dias_semana' => [1, 2, 3, 4, 5], // Segunda a Sexta
        'recorrente' => true,
        'motivo' => 'Horário de almoço',
        'ativo' => true,
    ]);

    echo "✓ Disponibilidades criadas\n\n";

    echo "6. Criando configuração de feriados...\n";
    
    // Criar configuração de feriados para o fisioterapeuta
    MedicoFeriadosConfig::create([
        'fisioterapeuta_id' => $user->id,
        'trabalha_feriados_nacionais' => false,
        'trabalha_feriados_estaduais' => false,
        'trabalha_feriados_municipais' => false,
        'feriados_excecoes' => [],
    ]);

    echo "✓ Configuração de feriados criada\n\n";

    echo "7. Criando feriados básicos...\n";
    
    // Verificar se já existem feriados
    $feriadosExistentes = Feriado::count();
    
    if ($feriadosExistentes === 0) {
        // Criar alguns feriados básicos
        $feriados = [
            [
                'nome' => 'Natal',
                'data' => '2025-12-25',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Natal',
                'ativo' => true,
            ],
            [
                'nome' => 'Ano Novo',
                'data' => '2025-01-01',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Ano Novo',
                'ativo' => true,
            ],
            [
                'nome' => 'Tiradentes',
                'data' => '2025-04-21',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Dia de Tiradentes',
                'ativo' => true,
            ],
            [
                'nome' => 'Dia do Trabalho',
                'data' => '2025-05-01',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Dia do Trabalho',
                'ativo' => true,
            ],
            [
                'nome' => 'Independência do Brasil',
                'data' => '2025-09-07',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Dia da Independência do Brasil',
                'ativo' => true,
            ],
            [
                'nome' => 'Nossa Senhora Aparecida',
                'data' => '2025-10-12',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Dia de Nossa Senhora Aparecida',
                'ativo' => true,
            ],
            [
                'nome' => 'Finados',
                'data' => '2025-11-02',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Dia de Finados',
                'ativo' => true,
            ],
            [
                'nome' => 'Proclamação da República',
                'data' => '2025-11-15',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Dia da Proclamação da República',
                'ativo' => true,
            ],
        ];

        foreach ($feriados as $feriado) {
            Feriado::create($feriado);
        }

        echo "✓ " . count($feriados) . " feriados criados\n\n";
    } else {
        echo "✓ Feriados já existentes no banco de dados\n\n";
    }

    echo "=== Resumo do Profissional de Teste Criado ===\n";
    echo "Nome: Dr. João Silva\n";
    echo "Email: {$emailTeste}\n";
    echo "Senha: password123\n";
    echo "CREFITO: 12345-SP\n";
    echo "User ID: {$user->id}\n";
    echo "Fisioterapeuta ID: {$fisioterapeuta->id}\n";
    echo "Estabelecimento ID: {$estabelecimento->id}\n";
    echo "Status: Aprovado e Ativo\n";
    echo "Avaliação: 4.8/5 (127 avaliações)\n";
    echo "\n";
    echo "=== Dados de Acesso ===\n";
    echo "Email: {$emailTeste}\n";
    echo "Senha: password123\n";
    echo "Role: fisioterapeuta\n";
    echo "\n";
    echo "Profissional de teste criado com sucesso!\n";

} catch (Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
