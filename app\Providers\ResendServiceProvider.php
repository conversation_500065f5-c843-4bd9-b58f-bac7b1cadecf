<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Mail;
use App\Mail\Transport\CustomResendTransport;

class ResendServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Configurar o Resend transport customizado com SSL desabilitado
        Mail::extend('resend_custom', function (array $config) {
            return new CustomResendTransport(config('services.resend.key'));
        });
    }
}