<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        DB::table('planos')
            ->where('name', 'Plano Pessoal')
            ->update(['sessions_per_month' => 2]);
    }

    public function down(): void
    {
        // Reverter para 1 (valor anterior conhecido)
        DB::table('planos')
            ->where('name', 'Plano Pessoal')
            ->update(['sessions_per_month' => 1]);
    }
};
