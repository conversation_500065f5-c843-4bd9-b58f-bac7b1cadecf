import SearchTemplate, { SearchConfig, useSearchState } from '@/components/search-template';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { User, Calendar, Phone } from 'lucide-react';

// Exemplo 1: Busca simples de usuários
export function SimpleUserSearch() {
    const { state, updateFilter, setLoading, setError } = useSearchState();

    const config: SearchConfig = {
        title: 'Buscar Usuários',
        description: 'Encontre usuários cadastrados no sistema',
        placeholder: 'Digite o nome ou email do usuário...',
        showResults: true,
    };

    const handleSearch = async (query: string, filters: Record<string, any>) => {
        setLoading(true);
        setError('');
        
        try {
            // Simular chamada à API
            await new Promise(resolve => setTimeout(resolve, 1000));
            console.log('Buscando:', { query, filters });
        } catch (error) {
            setError('Erro ao buscar usuários');
        } finally {
            setLoading(false);
        }
    };

    return (
        <SearchTemplate
            config={config}
            state={state}
            onSearch={handleSearch}
            onFilterChange={updateFilter}
            resultCount={0}
        />
    );
}

// Exemplo 2: Busca avançada de pacientes
export function AdvancedPatientSearch() {
    const { state, updateFilter, updatePage, setLoading, setError, clearFilters } = useSearchState({
        status: '',
        has_subscription: '',
        age_range: '',
    });

    const config: SearchConfig = {
        title: 'Buscar Pacientes',
        description: 'Encontre pacientes com filtros avançados',
        placeholder: 'Digite o nome, email ou telefone...',
        showAdvancedFilters: true,
        showResults: true,
        showPagination: true,
        filters: [
            {
                key: 'status',
                label: 'Status',
                type: 'select',
                options: [
                    { value: 'ativo', label: 'Ativo' },
                    { value: 'inativo', label: 'Inativo' },
                    { value: 'suspenso', label: 'Suspenso' },
                ],
            },
            {
                key: 'has_subscription',
                label: 'Possui Assinatura',
                type: 'select',
                options: [
                    { value: 'sim', label: 'Sim' },
                    { value: 'nao', label: 'Não' },
                ],
            },
            {
                key: 'age_range',
                label: 'Faixa Etária',
                type: 'select',
                options: [
                    { value: '18-30', label: '18-30 anos' },
                    { value: '31-50', label: '31-50 anos' },
                    { value: '51-70', label: '51-70 anos' },
                    { value: '70+', label: '70+ anos' },
                ],
            },
            {
                key: 'city',
                label: 'Cidade',
                type: 'input',
                placeholder: 'Digite a cidade...',
            },
            {
                key: 'active_only',
                label: 'Apenas ativos',
                type: 'checkbox',
            },
        ],
    };

    const mockPagination = {
        current_page: 1,
        total_pages: 5,
        total: 50,
        per_page: 10,
        has_next_page: true,
        has_prev_page: false,
        from: 1,
        to: 10,
    };

    const handleSearch = async (query: string, filters: Record<string, any>, page = 1) => {
        setLoading(true);
        setError('');
        
        try {
            // Simular chamada à API
            await new Promise(resolve => setTimeout(resolve, 1500));
            console.log('Buscando pacientes:', { query, filters, page });
        } catch (error) {
            setError('Erro ao buscar pacientes');
        } finally {
            setLoading(false);
        }
    };

    const mockPatients = [
        { id: 1, name: 'João Silva', email: '<EMAIL>', phone: '(11) 99999-9999', status: 'ativo' },
        { id: 2, name: 'Maria Santos', email: '<EMAIL>', phone: '(11) 88888-8888', status: 'ativo' },
    ];

    return (
        <SearchTemplate
            config={config}
            state={state}
            onSearch={handleSearch}
            onFilterChange={updateFilter}
            onPageChange={updatePage}
            onClearFilters={clearFilters}
            pagination={mockPagination}
            resultCount={mockPatients.length}
        >
            {/* Resultados customizados */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {mockPatients.map((patient) => (
                    <Card key={patient.id} className="hover:shadow-md transition-shadow">
                        <CardHeader className="pb-3">
                            <CardTitle className="flex items-center gap-2 text-base">
                                <User className="h-4 w-4" />
                                {patient.name}
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <p className="text-sm text-muted-foreground">{patient.email}</p>
                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                <Phone className="h-3 w-3" />
                                {patient.phone}
                            </p>
                            <Badge variant={patient.status === 'ativo' ? 'default' : 'secondary'}>
                                {patient.status}
                            </Badge>
                        </CardContent>
                    </Card>
                ))}
            </div>
        </SearchTemplate>
    );
}

// Exemplo 3: Busca com filtro de range
export function ProductSearchWithRange() {
    const { state, updateFilter, setLoading, setError } = useSearchState({
        category: '',
        price_range: 0,
        in_stock: false,
    });

    const config: SearchConfig = {
        title: 'Buscar Produtos',
        description: 'Encontre produtos com filtros de preço e categoria',
        placeholder: 'Digite o nome do produto...',
        showAdvancedFilters: true,
        showResults: true,
        filters: [
            {
                key: 'category',
                label: 'Categoria',
                type: 'select',
                options: [
                    { value: 'eletronicos', label: 'Eletrônicos' },
                    { value: 'roupas', label: 'Roupas' },
                    { value: 'casa', label: 'Casa e Jardim' },
                ],
            },
            {
                key: 'price_range',
                label: 'Preço Máximo',
                type: 'range',
                min: 0,
                max: 1000,
                step: 50,
            },
            {
                key: 'in_stock',
                label: 'Apenas em estoque',
                type: 'checkbox',
            },
        ],
    };

    const handleSearch = async (query: string, filters: Record<string, any>) => {
        setLoading(true);
        setError('');
        
        try {
            await new Promise(resolve => setTimeout(resolve, 800));
            console.log('Buscando produtos:', { query, filters });
        } catch (error) {
            setError('Erro ao buscar produtos');
        } finally {
            setLoading(false);
        }
    };

    return (
        <SearchTemplate
            config={config}
            state={state}
            onSearch={handleSearch}
            onFilterChange={updateFilter}
            resultCount={0}
            emptyState={
                <Card className="shadow-sm">
                    <CardContent className="py-12 text-center">
                        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                            <Calendar className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <p className="mb-2 text-base font-medium text-foreground">Nenhum produto encontrado</p>
                        <p className="text-sm text-muted-foreground">
                            Tente ajustar os filtros de categoria ou preço.
                        </p>
                        <Button className="mt-4" variant="outline" onClick={() => window.location.reload()}>
                            Limpar busca
                        </Button>
                    </CardContent>
                </Card>
            }
        />
    );
}
