<?php

require_once 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel');
\Illuminate\Support\Facades\Facade::setFacadeApplication($app);

echo "=== VERIFICANDO HORÁRIOS BASE DO FISIOTERAPEUTA 4 ===\n";
$horariosBase = \App\Models\HorarioBase::where('fisioterapeuta_id', 4)->where('ativo', true)->get();
echo "Total de horários base: {$horariosBase->count()}\n";
foreach($horariosBase as $horario) {
    echo "- Dia semana {$horario->dia_semana}: {$horario->hora_inicio} - {$horario->hora_fim}\n";
}

echo "\n=== VERIFICANDO EXCEÇÕES DO FISIOTERAPEUTA 4 ===\n";
$excecoes = \App\Models\HorarioExcecao::where('fisioterapeuta_id', 4)->where('ativo', true)->get();
echo "Total de exceções: {$excecoes->count()}\n";
foreach($excecoes as $excecao) {
    echo "- Tipo: {$excecao->acao}: {$excecao->hora_inicio} - {$excecao->hora_fim} (Motivo: {$excecao->motivo})\n";
}

echo "\n=== VERIFICANDO DISPONIBILIDADES BLOCADAS DO FISIOTERAPEUTA 4 ===\n";
$bloqueios = \App\Models\Disponibilidade::where('fisioterapeuta_id', 4)
    ->whereIn('tipo', ['indisponivel', 'bloqueio'])
    ->where('ativo', true)
    ->get();
echo "Total de bloqueios: {$bloqueios->count()}\n";
foreach($bloqueios as $bloqueio) {
    echo "- Tipo: {$bloqueio->tipo}: {$bloqueio->data_inicio} - {$bloqueio->data_fim} {$bloqueio->hora_inicio} - {$bloqueio->hora_fim}\n";
}

?>
