import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { useEffect, useRef, useState } from 'react';
import { Calendar, Clock, MapPin, Search, Star } from 'lucide-react';
import SearchTemplate, { useSearchState } from '@/components/search-template';

interface Fisioterapeuta {
    id: number;
    user: {
        name: string;
        email: string;
        avatar?: string;
    };
    crefito: string;
    specializations: string[];
    bio: string;
    hourly_rate: number;
    session_rate?: number | null;
    travel_fee?: number | null;
    pricing_mode?: 'por_hora' | 'por_sessao';
    available_areas: string[];
    rating: number;
    total_reviews: number;
    available: boolean;
    next_available_slot?: string;
}

interface Props {
    fisioterapeutas: {
        data: Fisioterapeuta[];
        links: any[];
        meta: any;
    };
    filters: {
        search?: string;
        specialization?: string;
        area?: string;
        rating?: string;
        available_only?: boolean;
        min_price?: number;
        max_price?: number;
    };
    specializations: string[];
    areas: string[];
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Início', href: '/paciente/dashboard' },
    { title: 'Buscar Fisioterapeutas', href: '/paciente/fisioterapeutas' },
];

export default function PacienteFisioterapeutas({ fisioterapeutas, filters, specializations, areas }: Props) {
    const { state, updateFilter, clearFilters, setLoading, setError } = useSearchState({
        specialization: filters.specialization || '',
        area: filters.area || '',
        rating: filters.rating || '',
        available_only: Boolean(filters.available_only) || false,
        min_price: filters.min_price ?? '',
        max_price: filters.max_price ?? '',
    });

    // Infinite scroll state
    const [items, setItems] = useState<Fisioterapeuta[]>(fisioterapeutas.data || []);
    const [currentPage, setCurrentPage] = useState<number>(Number(fisioterapeutas?.meta?.current_page || 1));
    const [lastPage, setLastPage] = useState<number>(Number(fisioterapeutas?.meta?.last_page || 1));
    const [loadingMore, setLoadingMore] = useState(false);
    const sentinelRef = useRef<HTMLDivElement | null>(null);

    // When new props arrive, merge or reset list depending on page
    useEffect(() => {
        const newPage = Number(fisioterapeutas?.meta?.current_page || 1);
        const newLast = Number(fisioterapeutas?.meta?.last_page || 1);
        setCurrentPage(newPage);
        setLastPage(newLast);

        const incoming = fisioterapeutas.data || [];
        if (newPage === 1) {
            setItems(incoming);
        } else {
            setItems((prev) => {
                const seen = new Set(prev.map((i) => i.id));
                const merged = [...prev];
                for (const it of incoming) if (!seen.has(it.id)) merged.push(it);
                return merged;
            });
        }
        setLoadingMore(false);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fisioterapeutas?.meta?.current_page, fisioterapeutas?.data]);

    const hasMore = currentPage < lastPage;

    const loadNextPage = () => {
        if (!hasMore || loadingMore) return;
        setLoadingMore(true);
        const nextPage = currentPage + 1;
        // Build params preserving filters
        const params: any = {
            search: state.query || filters.search || undefined,
            specialization: state.filters.specialization && state.filters.specialization !== 'all' ? state.filters.specialization : undefined,
            area: state.filters.area && state.filters.area !== 'all' ? state.filters.area : undefined,
            rating: state.filters.rating && state.filters.rating !== 'all' ? state.filters.rating : undefined,
            available_only: state.filters.available_only || undefined,
            min_price: state.filters.min_price || undefined,
            max_price: state.filters.max_price || undefined,
            page: nextPage,
        };
        router.get(route('paciente.fisioterapeutas.index', params), {}, {
            preserveState: true,
            preserveScroll: true,
            replace: true,
            onFinish: () => setLoadingMore(false),
        });
    };

    // IntersectionObserver to trigger loading when near bottom
    useEffect(() => {
        if (!sentinelRef.current) return;
        const el = sentinelRef.current;
        const observer = new IntersectionObserver(
            (entries) => {
                const [entry] = entries;
                if (entry.isIntersecting) loadNextPage();
            },
            { root: null, rootMargin: '200px', threshold: 0 }
        );
        observer.observe(el);
        return () => observer.unobserve(el);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [sentinelRef.current, hasMore, loadingMore, currentPage]);

    const onSearch = (query: string, f: Record<string, any>, page = 1) => {
        setLoading(true);
        setError('');
        const params: any = {
            search: query || undefined,
            specialization: f.specialization && f.specialization !== 'all' ? f.specialization : undefined,
            area: f.area && f.area !== 'all' ? f.area : undefined,
            rating: f.rating && f.rating !== 'all' ? f.rating : undefined,
            available_only: f.available_only || undefined,
            min_price: f.min_price || undefined,
            max_price: f.max_price || undefined,
            page,
        };
        router.get(route('paciente.fisioterapeutas.index', params), {}, {
            preserveState: true,
            replace: true,
            onFinish: () => setLoading(false),
        });
    };

    const onClear = () => {
        clearFilters();
        router.get(route('paciente.fisioterapeutas.index'), {}, {
            preserveState: true,
            replace: true,
        });
    };

    const renderStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, i) => (
            <Star key={i} className={`h-4 w-4 ${i < Math.floor(rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} />
        ));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Buscar Fisioterapeutas" />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                <div className="space-y-2">
                    <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">Buscar Fisioterapeutas</h1>
                    <p className="text-gray-600">Encontre o profissional ideal para seu tratamento</p>
                </div>
                
                <SearchTemplate
                    config={{
                        placeholder: 'Nome do fisioterapeuta... ',
                        showAdvancedFilters: true,
                        showResults: true,
                        showPagination: false,
                        filters: [
                            { key: 'specialization', label: 'Especialização', type: 'select', options: [{ value: 'all', label: 'Todas' }, ...specializations.map((s) => ({ value: s, label: s }))] },
                            { key: 'area', label: 'Área de atendimento', type: 'select', options: [{ value: 'all', label: 'Todas' }, ...areas.map((a) => ({ value: a, label: a }))] },
                            { key: 'rating', label: 'Avaliação mínima', type: 'select', options: [
                                { value: 'all', label: 'Qualquer avaliação' },
                                { value: '4', label: '4+ estrelas' },
                                { value: '3', label: '3+ estrelas' },
                                { value: '2', label: '2+ estrelas' },
                            ] },
                            { key: 'available_only', label: 'Somente disponíveis', type: 'checkbox' },
                            { key: 'min_price', label: 'Preço mínimo (R$)', type: 'input', placeholder: 'Ex: 80.00' },
                            { key: 'max_price', label: 'Preço máximo (R$)', type: 'input', placeholder: 'Ex: 150.00' },
                        ],
                    }}
                    state={{ ...state, query: filters.search || state.query }}
                    onSearch={onSearch}
                    onFilterChange={updateFilter}
                    onClearFilters={onClear}
                    resultCount={typeof fisioterapeutas?.meta?.total === 'number' ? fisioterapeutas.meta.total : items.length}
                >
                    {/* Lista de Fisioterapeutas (com infinite scroll) */}
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        {items.map((fisio) => (
                            <Card key={fisio.id} className="transition-shadow hover:shadow-lg">
                                <CardContent className="p-6">
                                    <div className="flex items-start gap-4">
                                        <Avatar className="h-16 w-16">
                                            <AvatarImage src={fisio.user.avatar} />
                                            <AvatarFallback>
                                                {fisio.user.name
                                                    .split(' ')
                                                    .map((n) => n[0])
                                                    .join('')
                                                    .toUpperCase()}
                                            </AvatarFallback>
                                        </Avatar>

                                        <div className="min-w-0 flex-1">
                                            <h3 className="truncate text-lg font-semibold">{fisio.user.name}</h3>
                                            <p className="text-sm text-muted-foreground">CREFITO: {fisio.crefito}</p>

                                            <div className="mt-1 flex items-center gap-1">
                                                {renderStars(fisio.rating)}
                                                <span className="ml-1 text-sm text-muted-foreground">({fisio.total_reviews} avaliações)</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="mt-4 space-y-3">
                                        {/* Especializações */}
                                        <div>
                                            <p className="mb-2 text-sm font-medium">Especializações:</p>
                                            <div className="flex flex-wrap gap-1">
                                                {fisio.specializations.slice(0, 3).map((spec) => (
                                                    <Badge key={spec} variant="secondary" className="text-xs">
                                                        {spec}
                                                    </Badge>
                                                ))}
                                                {fisio.specializations.length > 3 && (
                                                    <Badge variant="outline" className="text-xs">
                                                        +{fisio.specializations.length - 3}
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>

                                        {/* Áreas de atendimento */}
                                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                            <MapPin className="h-4 w-4" />
                                            <span>{fisio.available_areas.slice(0, 2).join(', ')}</span>
                                            {fisio.available_areas.length > 2 && <span>+{fisio.available_areas.length - 2}</span>}
                                        </div>

                                        {/* Valor e disponibilidade */}
                                        <div className="flex items-center justify-between">
                                            <div className="flex flex-col text-sm">
                                                <div className="flex items-center gap-2">
                                                    <Clock className="h-4 w-4 text-muted-foreground" />
                                                    <span className="font-medium">
                                                        {(() => {
                                                            const isHourly = fisio.pricing_mode === 'por_hora';
                                                            const price = isHourly
                                                                ? Number(fisio.hourly_rate || 0)
                                                                : (fisio.session_rate != null ? Number(fisio.session_rate) : Number(fisio.hourly_rate || 0));
                                                            const label = isHourly ? 'hora' : 'sessão';
                                                            return `R$ ${price.toFixed(2)}/${label}`;
                                                        })()}
                                                    </span>
                                                </div>
                                            </div>

                                            {fisio.available && (
                                                <Badge variant="default" className="bg-green-100 text-green-800">
                                                    Disponível
                                                </Badge>
                                            )}
                                        </div>

                                        {fisio.next_available_slot && (
                                            <div className="text-xs text-muted-foreground">Próximo horário: {fisio.next_available_slot}</div>
                                        )}
                                    </div>

                                    <div className="mt-4 flex gap-2">
                                        <Link href={route('paciente.fisioterapeutas.show', fisio.id)} className="flex-1">
                                            <Button variant="outline" className="w-full">
                                                Ver Perfil
                                            </Button>
                                        </Link>
                                        <Link href={route('paciente.agendamentos.create', { fisioterapeuta: fisio.id })} className="flex-1">
                                            <Button className="w-full">
                                                <Calendar className="mr-2 h-4 w-4" />
                                                Agendar
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>

                    {/* Empty state when no items */}
                    {items.length === 0 && (
                        <div className="py-12 text-center">
                            <Search className="mx-auto h-12 w-12 text-gray-400" />
                            <p className="mt-2 text-muted-foreground">Nenhum fisioterapeuta encontrado</p>
                            <p className="text-sm text-muted-foreground">Tente ajustar os filtros de busca</p>
                        </div>
                    )}

                    {/* Sentinel / Load more indicator placed under the list */}
                    <div ref={sentinelRef} />
                    {loadingMore && items.length > 0 && (
                        <div className="py-4 text-center text-sm text-muted-foreground">Carregando mais...</div>
                    )}
                    {!loadingMore && !hasMore && items.length > 0 && (
                        <div className="py-4 text-center text-xs text-muted-foreground">Você chegou ao fim da lista</div>
                    )}
                </SearchTemplate>
            </div>
        </AppLayout>
    );
}
