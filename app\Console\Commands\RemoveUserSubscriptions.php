<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Assinatura;
use App\Models\Pagamento;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RemoveUserSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:remove-subscriptions {email : Email do usuário}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove todas as assinaturas e informações de pagamento do usuário';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!config('features.plans_enabled')) {
            $this->info('Planos desabilitados. Comando ignorado.');
            return Command::SUCCESS;
        }

        $email = $this->argument('email');
        
        // Encontrar o usuário pelo email
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("Usuário com o email {$email} não encontrado.");
            return 1;
        }
        
        $this->info("Removendo assinaturas e pagamentos do usuário: {$user->name} ({$user->email})");
        
        // Iniciar transação para garantir consistência
        DB::beginTransaction();
        
        try {
            // Remover assinaturas
            $assinaturasCount = Assinatura::where('user_id', $user->id)->count();
            Assinatura::where('user_id', $user->id)->delete();
            $this->info("Removidas {$assinaturasCount} assinaturas.");
            
            // Remover pagamentos (se existir a tabela de pagamentos)
            if (\Schema::hasTable('pagamentos')) {
                $pagamentosCount = Pagamento::where('user_id', $user->id)->count();
                Pagamento::where('user_id', $user->id)->delete();
                $this->info("Removidos {$pagamentosCount} pagamentos.");
            }
            
            // Atualizar status do usuário
            $user->update([
                'has_subscription' => false,
                'plan_selected' => false,
                'checkout_completed' => false,
                'plan_selected_at' => null,
                'checkout_completed_at' => null
            ]);
            
            $this->info("Status do usuário atualizado para sem assinatura.");
            
            // Commit das alterações
            DB::commit();
            
            $this->info("\nTodas as assinaturas e informações de pagamento foram removidas com sucesso!");
            
        } catch (\Exception $e) {
            // Em caso de erro, faz rollback
            DB::rollBack();
            $this->error("Erro ao remover assinaturas: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
