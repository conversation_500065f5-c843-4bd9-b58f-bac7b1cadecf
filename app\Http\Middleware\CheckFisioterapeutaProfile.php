<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckFisioterapeutaProfile
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Só aplica para fisioterapeutas
        if ($user->role !== 'fisioterapeuta') {
            return $next($request);
        }
        
        // Permitir acesso às rotas de setup, email verification, logout, configurações, status e perfil
        $allowedRoutes = [
            'fisioterapeuta.setup',
            'fisioterapeuta.setup.store',
            'fisioterapeuta.verificar-email',
            'fisioterapeuta.verificar-email.reenviar',
            'fisioterapeuta.analise',
            'fisioterapeuta.conta-rejeitada',
            'fisioterapeuta.perfil',
            'fisioterapeuta.perfil.update',
            'fisioterapeuta.perfil.avatar.upload',
            'fisioterapeuta.perfil.avatar.remove',
            'logout',
            'profile.edit',
            'profile.update',
            'profile.destroy',
            'password.edit',
            'password.update',
            'appearance'
        ];

        // Se a rota atual é permitida, prosseguir sem verificações adicionais
        if (in_array($request->route()->getName(), $allowedRoutes)) {
            return $next($request);
        }

        // Verificar se o fisioterapeuta tem perfil completo
        if (!$this->hasCompleteProfile($user)) {
            return redirect()->route('fisioterapeuta.setup')
                ->with('warning', 'Você precisa completar seu perfil profissional para continuar.');
        }

        // Se o perfil está completo, verificar status da conta
        if ($user->fisioterapeuta) {
            $status = $user->fisioterapeuta->status;
            
            // Se a conta estiver rejeitada, redireciona para a página de conta rejeitada
            if ($status === 'rejected') {
                return redirect()->route('fisioterapeuta.conta-rejeitada');
            }
            
            // Se a conta estiver em análise, redireciona para a página de análise
            if ($status === 'pending') {
                return redirect()->route('fisioterapeuta.analise');
            }
        }

        return $next($request);
    }

    /**
     * Check if fisioterapeuta has complete profile
     */
    private function hasCompleteProfile($user): bool
    {
        $fisioterapeuta = $user->fisioterapeuta;

        // Se não tem perfil de fisioterapeuta, não está completo
        if (!$fisioterapeuta) {
            return false;
        }

        // Verificar campos obrigatórios
        $requiredFields = [
            'crefito',
            'specializations',
            'bio',
            'hourly_rate',
            'available_areas'
        ];

        foreach ($requiredFields as $field) {
            $value = $fisioterapeuta->$field;
            
            // Verificar se o campo está vazio
            if (empty($value)) {
                return false;
            }

            // Para arrays, verificar se tem pelo menos um item
            if (is_array($value) && count($value) === 0) {
                return false;
            }
        }

        // Verificar se o valor da hora é maior que 0
        if ($fisioterapeuta->hourly_rate <= 0) {
            return false;
        }

        // Verificar se a bio tem pelo menos 20 caracteres (match com validação do form)
        if (strlen($fisioterapeuta->bio) < 20) {
            return false;
        }

        return true;
    }
}
