import InputError from '@/components/input-error';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Head, useForm } from '@inertiajs/react';
import { FileText, Heart, Home, MapPin, Upload, User, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

interface User {
    id: number;
    name: string;
    email: string;
    phone?: string;
    birth_date?: string;
    gender?: string;
    address?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    medical_history?: string;
    emergency_contact?: string;
}

interface Props {
    user: User;
    estados: Record<string, string>;
}

export default function PacienteOnboarding({ user, estados }: Props) {
    const [currentStep, setCurrentStep] = useState(1);
    const totalSteps = 5;

    // Função para converter data do formato ISO para YYYY-MM-DD
    const formatDateForInput = (dateString: string | undefined): string => {
        if (!dateString) return '';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '';
            return date.toISOString().split('T')[0];
        } catch {
            return '';
        }
    };

    const { data, setData, post, processing, errors } = useForm({
        // Dados pessoais
        name: user.name || '',
        phone: user.phone || '',
        birth_date: formatDateForInput(user.birth_date),
        gender: user.gender || '',

        // Endereço
        address: user.address || '',
        city: user.city || '',
        state: user.state || '',
        zip_code: user.zip_code || '',

        // Informações médicas
        medical_history: user.medical_history || '',
        emergency_contact: user.emergency_contact || '',

        // Objetivos do tratamento
        main_objective: '',
        pain_level: '5',
        specific_areas: [],
        treatment_goals: '',

        // Preferências
        preferred_time: '',
        preferred_days: [],
        communication_preference: 'whatsapp',
        reminder_frequency: 'daily',
    });

    // Estado para upload de documentos
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [uploadedDocuments, setUploadedDocuments] = useState<
        Array<{
            id: string;
            name: string;
            size: string;
            type: string;
        }>
    >([]);

    // Estados para seleções múltiplas
    const [selectedAreas, setSelectedAreas] = useState<string[]>([]);
    const [selectedDays, setSelectedDays] = useState<string[]>([]);

    // Estado para mensagem de erro de idade
    const [ageError, setAgeError] = useState<string>('');

    // Sincronizar dados dos arrays com o formulário
    useEffect(() => {
        setData('specific_areas', selectedAreas as never[]);
    }, [selectedAreas]);

    useEffect(() => {
        setData('preferred_days', selectedDays as never[]);
    }, [selectedDays]);

    // Função para mapear erros para etapas
    const getStepFromError = (errorKey: string): number => {
        const stepMapping: Record<string, number> = {
            // Etapa 1: Dados Pessoais
            name: 1,
            phone: 1,
            birth_date: 1,
            gender: 1,
            // Etapa 2: Endereço
            address: 2,
            city: 2,
            state: 2,
            zip_code: 2,
            // Etapa 3: Informações Médicas
            medical_history: 3,
            emergency_contact: 3,
            medical_info: 3,
            // Etapa 4: Objetivos
            main_objective: 4,
            pain_level: 4,
            specific_areas: 4,
            treatment_goals: 4,
            // Etapa 5: Preferências
            preferred_time: 5,
            preferred_days: 5,
            communication_preference: 5,
            reminder_frequency: 5,
        };
        return stepMapping[errorKey] || currentStep;
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        console.log('Submitting onboarding form...', data);

        // Verificar se todos os campos obrigatórios estão preenchidos
        if (!canProceedToNext) {
            console.error('Form validation failed');
            toast.error('Por favor, preencha todos os campos obrigatórios.');
            return;
        }

        // Preparar dados para envio
        const submitData = {
            ...data,
            specific_areas: selectedAreas,
            preferred_days: selectedDays,
        };

        console.log('Final submit data:', submitData);

        post(route('paciente.onboarding.store'), {
            onSuccess: () => {
                console.log('Onboarding completed successfully');
                toast.success('Configuração finalizada com sucesso!');
            },
            onError: (errors) => {
                console.error('Onboarding submission failed:', errors);

                // Encontrar a primeira etapa com erro
                const errorKeys = Object.keys(errors);
                if (errorKeys.length > 0) {
                    const firstErrorStep = Math.min(...errorKeys.map((key) => getStepFromError(key)));
                    setCurrentStep(firstErrorStep);

                    // Mostrar mensagem específica
                    const firstError = errors[errorKeys[0]];
                    toast.error(`Erro na etapa ${firstErrorStep}: ${firstError}`);
                } else {
                    toast.error('Erro ao finalizar configuração. Verifique os dados e tente novamente.');
                }
            },
        });
    };

    // Funções para upload de documentos
    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (!files) return;

        Array.from(files).forEach((file) => {
            // Validar tipo de arquivo
            const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
            if (!allowedTypes.includes(file.type)) {
                toast.error('Apenas arquivos PDF, JPG e PNG são permitidos.');
                return;
            }

            // Validar tamanho (5MB)
            if (file.size > 5 * 1024 * 1024) {
                toast.error('O arquivo deve ter no máximo 5MB.');
                return;
            }

            // Adicionar à lista de documentos
            const newDoc = {
                id: Math.random().toString(36).substr(2, 9),
                name: file.name,
                size: formatFileSize(file.size),
                type: file.type,
            };

            setUploadedDocuments((prev) => [...prev, newDoc]);
            toast.success(`Documento "${file.name}" adicionado com sucesso!`);
        });

        // Limpar input
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const removeDocument = (id: string) => {
        setUploadedDocuments((prev) => prev.filter((doc) => doc.id !== id));
        toast.success('Documento removido.');
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Funções para seleções múltiplas
    const toggleArea = (area: string) => {
        const newAreas = selectedAreas.includes(area) ? selectedAreas.filter((a) => a !== area) : [...selectedAreas, area];
        setSelectedAreas(newAreas);
        setData('specific_areas', newAreas as never[]);
    };

    const toggleDay = (day: string) => {
        const newDays = selectedDays.includes(day) ? selectedDays.filter((d) => d !== day) : [...selectedDays, day];
        setSelectedDays(newDays);
        setData('preferred_days', newDays as never[]);
    };

    const formatCEP = (value: string) => {
        const numbers = value.replace(/\D/g, '');
        if (numbers.length <= 5) {
            return numbers;
        }
        return `${numbers.slice(0, 5)}-${numbers.slice(5, 8)}`;
    };

    const formatPhone = (value: string) => {
        const numbers = value.replace(/\D/g, '');
        if (numbers.length <= 2) {
            return `(${numbers}`;
        } else if (numbers.length <= 7) {
            return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
        } else if (numbers.length <= 11) {
            return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7)}`;
        }
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
    };

    // Validação mais robusta para cada etapa
    const validateStep = (step: number): { isValid: boolean; errors: string[] } => {
        const errors: string[] = [];

        switch (step) {
            case 1:
                if (!data.name.trim()) errors.push('Nome é obrigatório');
                if (!data.phone.trim()) errors.push('Telefone é obrigatório');
                if (!data.birth_date) errors.push('Data de nascimento é obrigatória');
                if (!data.gender) errors.push('Gênero é obrigatório');

                // Validar idade mínima
                if (data.birth_date) {
                    const birthDate = new Date(data.birth_date);
                    const today = new Date();
                    let age = today.getFullYear() - birthDate.getFullYear();
                    const monthDiff = today.getMonth() - birthDate.getMonth();
                    const dayDiff = today.getDate() - birthDate.getDate();

                    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
                        age--;
                    }

                    if (age < 18) {
                        errors.push('Apenas usuários maiores de 18 anos podem se cadastrar');
                    }
                    if (age > 120) {
                        errors.push('Data de nascimento inválida');
                    }
                }
                break;

            case 2:
                if (!data.address.trim()) errors.push('Endereço é obrigatório');
                if (!data.city.trim()) errors.push('Cidade é obrigatória');
                if (!data.state) errors.push('Estado é obrigatório');
                if (!data.zip_code.trim()) errors.push('CEP é obrigatório');
                break;

            case 3:
                if (data.medical_history.length < 20 && data.emergency_contact.length < 9) {
                    errors.push('Preencha pelo menos o histórico médico (mín. 20 caracteres) ou contato de emergência (mín. 9 caracteres)');
                }
                break;

            case 4:
                if (!data.main_objective) errors.push('Objetivo principal é obrigatório');
                break;

            case 5:
                if (!data.preferred_time) errors.push('Horário preferido é obrigatório');
                if (!data.communication_preference) errors.push('Preferência de comunicação é obrigatória');
                break;
        }

        return { isValid: errors.length === 0, errors };
    };

    const getStepValidation = (step: number): boolean => {
        return validateStep(step).isValid;
    };

    const canProceedToNext = getStepValidation(currentStep);
    const progress = (currentStep / totalSteps) * 100;

    const nextStep = () => {
        const validation = validateStep(currentStep);

        if (!validation.isValid) {
            // Mostrar erros específicos
            validation.errors.forEach((error) => {
                toast.error(error);
            });
            return;
        }

        if (currentStep < totalSteps) {
            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };

    return (
        <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-green-50 to-primary/10 p-4">
            <Head title="Configuração dos Dados Médicos" />

            <div className="w-full max-w-2xl">
                {/* Header */}
                <div className="mb-8 text-center">
                    <div className="mb-4 flex justify-center">
                        <Badge size="icon-lg" variant="default" className="h-16 w-16 shadow-sm">
                            <Heart className="!size-8" />
                        </Badge>
                    </div>
                    <h1 className="text-3xl font-bold text-gray-900">Bem-vindo à F4 Fisio!</h1>
                    <p className="mt-2 text-gray-600">Vamos configurar seus dados médicos para que você possa receber o melhor atendimento</p>
                </div>

                {/* Progress */}
                <div className="mb-8">
                    <div className="mb-2 flex justify-between text-sm text-gray-600">
                        <span>
                            Passo {currentStep} de {totalSteps}
                        </span>
                        <span>{Math.round(progress)}% concluído</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                </div>

                {/* Form */}
                <Card>
                    <form onSubmit={handleSubmit}>
                        {/* Step 1: Dados Pessoais */}
                        {currentStep === 1 && (
                            <>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Badge size="icon-lg" variant="default" className="shadow-sm">
                                            <User />
                                        </Badge>
                                        Dados Pessoais
                                    </CardTitle>
                                    <CardDescription>Confirme suas informações básicas de identificação</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Nome Completo *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Seu nome completo"
                                        />
                                        <InputError message={errors.name} />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="phone">Telefone *</Label>
                                        <Input
                                            id="phone"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', formatPhone(e.target.value))}
                                            placeholder="(11) 99999-9999"
                                            maxLength={15}
                                        />
                                        <InputError message={errors.phone} />
                                    </div>

                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="birth_date">Data de Nascimento *</Label>
                                            <DatePicker
                                                value={data.birth_date ? new Date(data.birth_date) : undefined}
                                                onValueChange={(date) => {
                                                    if (date) {
                                                        // Formatar data para YYYY-MM-DD
                                                        const formattedDate = date.toISOString().split('T')[0];
                                                        setData('birth_date', formattedDate);

                                                        // Validar idade
                                                        const today = new Date();
                                                        let age = today.getFullYear() - date.getFullYear();
                                                        const monthDiff = today.getMonth() - date.getMonth();
                                                        const dayDiff = today.getDate() - date.getDate();

                                                        if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
                                                            age--;
                                                        }

                                                        if (age < 18) {
                                                            setAgeError('Apenas usuários maiores de 18 anos podem se cadastrar');
                                                        } else {
                                                            setAgeError('');
                                                        }
                                                    } else {
                                                        setData('birth_date', '');
                                                        setAgeError('');
                                                    }
                                                }}
                                                placeholder="Selecione sua data de nascimento"
                                                captionLayout="dropdown"
                                                fromYear={new Date().getFullYear() - 120}
                                                toYear={new Date().getFullYear()}
                                                disabledDates={(date) => {
                                                    const today = new Date();
                                                    const minDate = new Date();
                                                    minDate.setFullYear(today.getFullYear() - 120);

                                                    // Desabilitar datas futuras e muito antigas
                                                    return date > today || date < minDate;
                                                }}
                                            />
                                            <InputError message={errors.birth_date} />
                                            {ageError && (
                                                <div className="rounded-md border border-red-200 bg-red-50 p-2 text-sm text-red-600">{ageError}</div>
                                            )}
                                            <div className="rounded-md border border-primary/20 bg-primary/5 p-2 text-sm text-gray-600">
                                                <strong>Atenção:</strong> Apenas usuários maiores de 18 anos podem se cadastrar na plataforma.
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="gender">Gênero *</Label>
                                            <Select value={data.gender} onValueChange={(value) => setData('gender', value)}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione seu gênero" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="masculino">Masculino</SelectItem>
                                                    <SelectItem value="feminino">Feminino</SelectItem>
                                                    <SelectItem value="outro">Outro</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <InputError message={errors.gender} />
                                        </div>
                                    </div>
                                </CardContent>
                            </>
                        )}

                        {/* Step 2: Endereço */}
                        {currentStep === 2 && (
                            <>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Badge size="icon-lg" variant="default" className="shadow-sm">
                                            <Home />
                                        </Badge>
                                        Endereço
                                    </CardTitle>
                                    <CardDescription>Informações de localização para atendimento domiciliar</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="address">Endereço Completo *</Label>
                                        <Input
                                            id="address"
                                            value={data.address}
                                            onChange={(e) => setData('address', e.target.value)}
                                            placeholder="Rua, número, complemento"
                                        />
                                        <InputError message={errors.address} />
                                    </div>

                                    <div className="grid gap-4 sm:grid-cols-3">
                                        <div className="space-y-2">
                                            <Label htmlFor="city">Cidade *</Label>
                                            <Input
                                                id="city"
                                                value={data.city}
                                                onChange={(e) => setData('city', e.target.value)}
                                                placeholder="São Paulo"
                                            />
                                            <InputError message={errors.city} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="state">Estado *</Label>
                                            <Select value={data.state} onValueChange={(value) => setData('state', value)}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="UF" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(estados).map(([uf, nome]) => (
                                                        <SelectItem key={uf} value={uf}>
                                                            {uf} - {nome}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <InputError message={errors.state} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="zip_code">CEP *</Label>
                                            <Input
                                                id="zip_code"
                                                value={data.zip_code}
                                                onChange={(e) => setData('zip_code', formatCEP(e.target.value))}
                                                placeholder="00000-000"
                                                maxLength={9}
                                            />
                                            <InputError message={errors.zip_code} />
                                        </div>
                                    </div>
                                </CardContent>
                            </>
                        )}

                        {/* Step 3: Informações Médicas */}
                        {currentStep === 3 && (
                            <>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Badge size="icon-lg" variant="default" className="shadow-sm">
                                            <Heart />
                                        </Badge>
                                        Informações Médicas
                                    </CardTitle>
                                    <CardDescription>Dados essenciais para seu atendimento (preencha pelo menos um campo)</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="space-y-2">
                                        <Label htmlFor="medical_history">Histórico Médico</Label>
                                        <Textarea
                                            id="medical_history"
                                            value={data.medical_history}
                                            onChange={(e) => setData('medical_history', e.target.value)}
                                            placeholder="Descreva seu histórico médico: condições existentes, cirurgias anteriores, medicamentos em uso, alergias, etc."
                                            rows={4}
                                        />
                                        <InputError message={errors.medical_history} />
                                        <p className={`text-sm ${data.medical_history.length >= 20 ? 'text-green-600' : 'text-muted-foreground'}`}>
                                            {data.medical_history.length >= 20 ? '✓' : ''} {data.medical_history.length}/2000 caracteres (mínimo 20)
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="emergency_contact">Contato de Emergência</Label>
                                        <Textarea
                                            id="emergency_contact"
                                            value={data.emergency_contact}
                                            onChange={(e) => setData('emergency_contact', e.target.value)}
                                            placeholder="Nome, parentesco, telefone e endereço de uma pessoa para contato em caso de emergência"
                                            rows={3}
                                        />
                                        <InputError message={errors.emergency_contact} />
                                        <p className={`text-sm ${data.emergency_contact.length >= 9 ? 'text-green-600' : 'text-muted-foreground'}`}>
                                            {data.emergency_contact.length >= 9 ? '✓' : ''} {data.emergency_contact.length}/500 caracteres (mínimo 9)
                                        </p>
                                    </div>

                                    {(errors as Record<string, string>).medical_info && (
                                        <div className="text-sm text-destructive">{(errors as Record<string, string>).medical_info}</div>
                                    )}

                                    {/* Upload de Documentos Médicos */}
                                    <div className="space-y-4">
                                        <div className="space-y-2">
                                            <Label>Documentos Médicos (Opcional)</Label>
                                            <p className="text-sm text-muted-foreground">
                                                Anexe exames, receitas médicas ou outros documentos relevantes (PDF, JPG, PNG - máx. 5MB cada)
                                            </p>
                                        </div>

                                        {/* Área de Upload */}
                                        <div className="rounded-lg border-2 border-dashed border-gray-300 p-6 text-center transition-colors hover:border-gray-400">
                                            <input
                                                ref={fileInputRef}
                                                type="file"
                                                multiple
                                                accept=".pdf,.jpg,.jpeg,.png"
                                                onChange={handleFileSelect}
                                                className="hidden"
                                            />
                                            <div className="space-y-2">
                                                <Upload className="mx-auto h-8 w-8 text-gray-400" />
                                                <div>
                                                    <Button type="button" variant="outline" onClick={() => fileInputRef.current?.click()}>
                                                        <Upload className="mr-2 h-4 w-4" />
                                                        Selecionar Documentos
                                                    </Button>
                                                </div>
                                                <p className="text-xs text-muted-foreground">Ou arraste e solte os arquivos aqui</p>
                                            </div>
                                        </div>

                                        {/* Lista de Documentos Carregados */}
                                        {uploadedDocuments.length > 0 && (
                                            <div className="space-y-2">
                                                <Label>Documentos Adicionados ({uploadedDocuments.length})</Label>
                                                <div className="space-y-2">
                                                    {uploadedDocuments.map((doc) => (
                                                        <div key={doc.id} className="flex items-center justify-between rounded-lg bg-gray-50 p-3">
                                                            <div className="flex items-center gap-3">
                                                                <FileText className="h-5 w-5 text-green-600" />
                                                                <div>
                                                                    <p className="text-sm font-medium">{doc.name}</p>
                                                                    <p className="text-xs text-muted-foreground">{doc.size}</p>
                                                                </div>
                                                            </div>
                                                            <Button
                                                                type="button"
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => removeDocument(doc.id)}
                                                                className="text-red-600 hover:text-red-700"
                                                            >
                                                                <X className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                                        <div className="flex items-start gap-3">
                                            <MapPin className="mt-0.5 h-5 w-5 text-green-600" />
                                            <div>
                                                <h4 className="font-medium text-green-900">Informação Importante</h4>
                                                <p className="mt-1 text-sm text-green-700">
                                                    Essas informações são essenciais para que nossos fisioterapeutas possam oferecer o melhor
                                                    atendimento personalizado para você.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </>
                        )}

                        {/* Step 4: Objetivos do Tratamento */}
                        {currentStep === 4 && (
                            <>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Badge size="icon-lg" variant="default" className="shadow-sm">
                                            <Heart />
                                        </Badge>
                                        Objetivos do Tratamento
                                    </CardTitle>
                                    <CardDescription>Defina seus objetivos para personalizar seu tratamento</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="space-y-2">
                                        <Label htmlFor="main_objective">Objetivo Principal</Label>
                                        <Select value={data.main_objective} onValueChange={(value) => setData('main_objective', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione seu objetivo principal" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="alivio_dor">Alívio da Dor</SelectItem>
                                                <SelectItem value="reabilitacao">Reabilitação Pós-Lesão</SelectItem>
                                                <SelectItem value="fortalecimento">Fortalecimento Muscular</SelectItem>
                                                <SelectItem value="flexibilidade">Melhora da Flexibilidade</SelectItem>
                                                <SelectItem value="postura">Correção Postural</SelectItem>
                                                <SelectItem value="prevencao">Prevenção de Lesões</SelectItem>
                                                <SelectItem value="condicionamento">Condicionamento Físico</SelectItem>
                                                <SelectItem value="outro">Outro</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <InputError message={errors.main_objective} />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="pain_level">Nível de Dor Atual (0-10)</Label>
                                        <div className="space-y-2">
                                            <input
                                                type="range"
                                                id="pain_level"
                                                min="0"
                                                max="10"
                                                value={data.pain_level}
                                                onChange={(e) => setData('pain_level', e.target.value)}
                                                className="w-full"
                                            />
                                            <div className="flex justify-between text-sm text-muted-foreground">
                                                <span>0 - Sem dor</span>
                                                <span className="font-medium">Atual: {data.pain_level}</span>
                                                <span>10 - Dor máxima</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label>Áreas Específicas de Interesse</Label>
                                        <p className="text-sm text-muted-foreground">Selecione as áreas que precisam de atenção</p>
                                        <div className="grid grid-cols-2 gap-2">
                                            {[
                                                'Pescoço',
                                                'Ombros',
                                                'Braços',
                                                'Punhos',
                                                'Coluna Cervical',
                                                'Coluna Torácica',
                                                'Coluna Lombar',
                                                'Quadril',
                                                'Coxas',
                                                'Joelhos',
                                                'Panturrilhas',
                                                'Pés',
                                            ].map((area) => (
                                                <button
                                                    key={area}
                                                    type="button"
                                                    onClick={() => toggleArea(area)}
                                                    className={`rounded-md border p-2 text-sm transition-colors ${
                                                        selectedAreas.includes(area)
                                                            ? 'border-green-300 bg-green-100 text-green-700'
                                                            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                                    }`}
                                                >
                                                    {area}
                                                </button>
                                            ))}
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="treatment_goals">Metas Específicas</Label>
                                        <Textarea
                                            id="treatment_goals"
                                            value={data.treatment_goals}
                                            onChange={(e) => setData('treatment_goals', e.target.value)}
                                            placeholder="Descreva suas metas específicas: voltar a praticar esportes, melhorar postura no trabalho, reduzir dores, etc."
                                            rows={3}
                                        />
                                        <p className="text-sm text-muted-foreground">{data.treatment_goals.length}/500 caracteres</p>
                                    </div>
                                </CardContent>
                            </>
                        )}

                        {/* Step 5: Preferências */}
                        {currentStep === 5 && (
                            <>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Badge size="icon-lg" variant="default" className="shadow-sm">
                                            <User />
                                        </Badge>
                                        Configurar Preferências
                                    </CardTitle>
                                    <CardDescription>Personalize sua experiência na plataforma</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="space-y-2">
                                        <Label htmlFor="preferred_time">Horário Preferido</Label>
                                        <Select value={data.preferred_time} onValueChange={(value) => setData('preferred_time', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione seu horário preferido" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="manha">Manhã (6h - 12h)</SelectItem>
                                                <SelectItem value="tarde">Tarde (12h - 18h)</SelectItem>
                                                <SelectItem value="noite">Noite (18h - 22h)</SelectItem>
                                                <SelectItem value="flexivel">Flexível</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label>Dias da Semana Preferidos</Label>
                                        <p className="text-sm text-muted-foreground">Selecione os dias que você prefere para as sessões</p>
                                        <div className="grid grid-cols-4 gap-2">
                                            {[
                                                { key: 'segunda', label: 'Seg' },
                                                { key: 'terca', label: 'Ter' },
                                                { key: 'quarta', label: 'Qua' },
                                                { key: 'quinta', label: 'Qui' },
                                                { key: 'sexta', label: 'Sex' },
                                                { key: 'sabado', label: 'Sáb' },
                                                { key: 'domingo', label: 'Dom' },
                                            ].map((day) => (
                                                <button
                                                    key={day.key}
                                                    type="button"
                                                    onClick={() => toggleDay(day.key)}
                                                    className={`rounded-md border p-2 text-sm transition-colors ${
                                                        selectedDays.includes(day.key)
                                                            ? 'border-green-300 bg-green-100 text-green-700'
                                                            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                                    }`}
                                                >
                                                    {day.label}
                                                </button>
                                            ))}
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="communication_preference">Preferência de Comunicação</Label>
                                        <Select
                                            value={data.communication_preference}
                                            onValueChange={(value) => setData('communication_preference', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Como prefere ser contatado?" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="whatsapp">WhatsApp</SelectItem>
                                                <SelectItem value="email">Email</SelectItem>
                                                <SelectItem value="sms">SMS</SelectItem>
                                                <SelectItem value="telefone">Telefone</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="reminder_frequency">Frequência de Lembretes</Label>
                                        <Select value={data.reminder_frequency} onValueChange={(value) => setData('reminder_frequency', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Com que frequência deseja receber lembretes?" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="none">Sem lembretes</SelectItem>
                                                <SelectItem value="daily">Diário</SelectItem>
                                                <SelectItem value="weekly">Semanal</SelectItem>
                                                <SelectItem value="before_session">Apenas antes das sessões</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                                        <div className="flex items-start gap-3">
                                            <User className="mt-0.5 h-5 w-5 text-green-600" />
                                            <div>
                                                <h4 className="font-medium text-green-900">Personalização Completa</h4>
                                                <p className="mt-1 text-sm text-green-700">
                                                    Suas preferências nos ajudam a oferecer uma experiência mais personalizada e eficiente. Você pode
                                                    alterar essas configurações a qualquer momento no seu perfil.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </>
                        )}

                        {/* Navigation */}
                        <div className="flex justify-between border-t p-6">
                            <Button type="button" variant="outline" onClick={prevStep} disabled={currentStep === 1}>
                                Anterior
                            </Button>

                            {currentStep < totalSteps ? (
                                <Button type="button" onClick={nextStep} disabled={!canProceedToNext}>
                                    Próximo
                                </Button>
                            ) : (
                                <Button
                                    type="submit"
                                    disabled={processing || !canProceedToNext}
                                    className="gap-2"
                                    onClick={() => {
                                        console.log('Submit button clicked', { processing, canProceedToNext, currentStep });
                                        // O handleSubmit será chamado automaticamente pelo form
                                    }}
                                >
                                    {processing ? 'Finalizando...' : 'Finalizar Configuração'}
                                </Button>
                            )}
                        </div>
                    </form>
                </Card>
            </div>
        </div>
    );
}
