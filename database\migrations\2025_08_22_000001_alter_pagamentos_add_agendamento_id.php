<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pagamentos', function (Blueprint $table) {
            // tornar assinatura_id opcional para suportar pagamentos avulsos
            $table->foreignId('assinatura_id')->nullable()->change();

            // adicionar vínculo opcional com agendamentos
            if (!Schema::hasColumn('pagamentos', 'agendamento_id')) {
                $table->foreignId('agendamento_id')->nullable()->after('assinatura_id')
                    ->constrained('agendamentos')->onDelete('set null');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pagamentos', function (Blueprint $table) {
            // remover chave estrangeira e coluna de agendamento
            if (Schema::hasColumn('pagamentos', 'agendamento_id')) {
                $table->dropConstrainedForeignId('agendamento_id');
            }

            // voltar a exigir assinatura_id
            $table->foreignId('assinatura_id')->nullable(false)->change();
        });
    }
};
