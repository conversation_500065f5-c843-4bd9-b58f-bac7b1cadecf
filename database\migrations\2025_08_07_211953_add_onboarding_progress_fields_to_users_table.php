<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Campos para controlar o progresso do fluxo de onboarding
            $table->boolean('onboarding_completed')->default(false)->after('has_subscription')->comment('Se o onboarding médico foi completado');
            $table->boolean('plan_selected')->default(false)->after('onboarding_completed')->comment('Se um plano foi selecionado');
            $table->boolean('checkout_completed')->default(false)->after('plan_selected')->comment('Se o checkout foi completado');
            $table->timestamp('onboarding_completed_at')->nullable()->after('checkout_completed')->comment('Data de conclusão do onboarding');
            $table->timestamp('plan_selected_at')->nullable()->after('onboarding_completed_at')->comment('Data de seleção do plano');
            $table->timestamp('checkout_completed_at')->nullable()->after('plan_selected_at')->comment('Data de conclusão do checkout');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'onboarding_completed',
                'plan_selected',
                'checkout_completed',
                'onboarding_completed_at',
                'plan_selected_at',
                'checkout_completed_at'
            ]);
        });
    }
};
