<?php

namespace App\Http\Controllers\Fisioterapeuta;

use App\Http\Controllers\Controller;
use App\Models\Fisioterapeuta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class SetupController extends Controller
{
    /**
     * Display the fisioterapeuta setup page.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $fisioterapeuta = $user->fisioterapeuta;
        
        // Se não tem perfil de fisioterapeuta, criar um básico
        if (!$fisioterapeuta) {
            $fisioterapeuta = Fisioterapeuta::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'crefito' => null, // NULL permite múltiplos registros sem CREFITO
                    'specializations' => [],
                    'bio' => '',
                    'hourly_rate' => 0,
                    'available_areas' => [],
                    'working_hours' => [],
                    'available' => false,
                    'rating' => 0,
                    'total_reviews' => 0,
                ]
            );
        }
        
        // Listas para os selects
        $especialidades = [
            'Ortopédica',
            'Neurológica',
            'Respiratória',
            'Cardíaca',
            'Geriátrica',
            'Pediátrica',
            'Esportiva',
            'Dermatofuncional',
            'Uroginecológica',
            'Aquática',
            'Intensiva',
            'Oncológica',
            'Reumatológica',
            'Traumato-Ortopédica',
            'Outras'
        ];
        
        $areas = [
            'São Paulo - Centro',
            'São Paulo - Zona Norte',
            'São Paulo - Zona Sul',
            'São Paulo - Zona Leste',
            'São Paulo - Zona Oeste',
            'ABC Paulista',
            'Guarulhos',
            'Osasco',
            'Barueri',
            'Alphaville',
            'Carapicuíba',
            'Itapevi',
            'Jandira',
            'Cotia',
            'Taboão da Serra',
            'Embu das Artes',
            'São Bernardo do Campo',
            'Santo André',
            'São Caetano do Sul',
            'Diadema',
            'Mauá',
            'Ribeirão Pires',
            'Rio Grande da Serra'
        ];
        
        // Default working hours (Monday to Friday, 9am to 6pm)
        $defaultWorkingHours = [
            'monday' => [ 'start' => '09:00', 'end' => '18:00', 'available' => true ],
            'tuesday' => [ 'start' => '09:00', 'end' => '18:00', 'available' => true ],
            'wednesday' => [ 'start' => '09:00', 'end' => '18:00', 'available' => true ],
            'thursday' => [ 'start' => '09:00', 'end' => '18:00', 'available' => true ],
            'friday' => [ 'start' => '09:00', 'end' => '18:00', 'available' => true ],
            'saturday' => [ 'start' => '09:00', 'end' => '13:00', 'available' => false ],
            'sunday' => [ 'start' => '09:00', 'end' => '13:00', 'available' => false ],
        ];

        // Se vier vazio (\nulo, [], {}), enviar padrão para o front sem persistir ainda
        $wh = $fisioterapeuta->working_hours;
        $isEmptyArray = is_array($wh) && count($wh) === 0;
        $isEmptyObject = is_object($wh) && count((array) $wh) === 0;
        if (!$wh || $isEmptyArray || $isEmptyObject) {
            $fisioterapeuta->working_hours = $defaultWorkingHours;
        }

        return Inertia::render('fisioterapeuta/setup', [
            'fisioterapeuta' => $fisioterapeuta,
            'user' => $user,
            'especialidades' => $especialidades,
            'areas' => $areas,
            // Taxa de serviço global (percentual)
            'serviceFeePercent' => (float) config('fees.service_fee_percent', 20),
        ]);
    }
    
    /**
     * Store the fisioterapeuta setup.
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        $fisioterapeuta = $user->fisioterapeuta;
        
        if (!$fisioterapeuta) {
            return redirect()->route('fisioterapeuta.setup')
                ->with('error', 'Perfil de fisioterapeuta não encontrado.');
        }
        
        // Debug: Log dos dados recebidos
        Log::info('Setup form data received:', $request->all());
        
        try {
            $validated = $request->validate([
                'crefito' => 'required|string|max:50|unique:fisioterapeutas,crefito,' . $fisioterapeuta->id . ',id',
                'specializations' => 'required|array|min:1',
                'specializations.*' => 'string|max:100',
                'bio' => 'required|string|min:20|max:1000',
                'pricing_mode' => 'nullable|in:por_hora,por_sessao',
                'hourly_rate' => 'required|numeric|min:10|max:999999',
                'session_rate' => 'nullable|numeric|min:10|max:999.99',
                'travel_fee' => 'nullable|numeric|min:0|max:999.99',
                'service_rates' => 'nullable|array',
                'service_rates.avaliacao' => 'nullable|numeric|min:10|max:999.99',
                'service_rates.sessao' => 'nullable|numeric|min:10|max:999.99',
                'service_rates.teleatendimento' => 'nullable|numeric|min:10|max:999.99',
                'available_areas' => 'required|array|min:1',
                'available_areas.*' => 'string|max:100',
                // Horários de trabalho
                'working_hours' => 'required|array',
                'working_hours.*.start' => 'required|string|date_format:H:i',
                'working_hours.*.end' => 'required|string|date_format:H:i',
                'working_hours.*.available' => 'required|boolean',
            ], [
                'crefito.required' => 'O número do CREFITO é obrigatório.',
                'crefito.unique' => 'Este CREFITO já está cadastrado no sistema.',
                'specializations.required' => 'Selecione pelo menos uma especialização.',
                'specializations.min' => 'Selecione pelo menos uma especialização.',
                'bio.required' => 'A biografia profissional é obrigatória.',
                'bio.min' => 'A biografia deve ter pelo menos 20 caracteres.',
                'hourly_rate.required' => 'O valor da hora é obrigatório.',
                'hourly_rate.min' => 'O valor da hora deve ser pelo menos R$ 10,00.',
                'hourly_rate.max' => 'O valor da hora não pode exceder R$ 999.999,00.',
                'session_rate.min' => 'O valor por sessão deve ser pelo menos R$ 10,00.',
                'travel_fee.min' => 'A taxa de deslocamento não pode ser negativa.',
                'service_rates.*.min' => 'Cada serviço deve ter no mínimo R$ 10,00 quando informado.',
                'service_rates.*.max' => 'Cada serviço não pode exceder R$ 999,99.',
                'available_areas.required' => 'Selecione pelo menos uma área de atendimento.',
                'available_areas.min' => 'Selecione pelo menos uma área de atendimento.',
                'working_hours.required' => 'Defina seus horários de trabalho.',
                'working_hours.*.start.required' => 'Informe o horário de início.',
                'working_hours.*.end.required' => 'Informe o horário de término.',
                'working_hours.*.available.required' => 'Informe a disponibilidade do dia.',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Validation failed:', [
                'errors' => $e->errors(),
                'data' => $request->all()
            ]);
            // Return back with validation errors instead of throwing exception
            return back()->withErrors($e->errors())->withInput();
        }

        // Regras adicionais: pelo menos um dia disponível e start < end para dias disponíveis
        $wh = $request->input('working_hours', []);
        $hasAvailableDay = false;
        $timesValid = true;
        if (is_array($wh)) {
            foreach ($wh as $day => $info) {
                $available = (bool) ($info['available'] ?? false);
                if ($available) {
                    $hasAvailableDay = true;
                    $start = $info['start'] ?? null;
                    $end = $info['end'] ?? null;
                    if (!$start || !$end || $start >= $end) {
                        $timesValid = false;
                        break;
                    }
                }
            }
        }
        if (!$hasAvailableDay) {
            return back()->withErrors(['working_hours' => 'Selecione pelo menos um dia disponível.'])->withInput();
        }
        if (!$timesValid) {
            return back()->withErrors(['working_hours' => 'Verifique os horários: o início deve ser antes do término para os dias disponíveis.'])->withInput();
        }
        
        // Validar dados do usuário
        $userValidated = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
        ], [
            'name.required' => 'O nome completo é obrigatório.',
            'phone.required' => 'O telefone é obrigatório.',
        ]);
        
        DB::transaction(function () use ($validated, $userValidated, $fisioterapeuta, $user) {
            // Atualizar dados do usuário
            $user->update($userValidated);
            
            // Atualizar dados do fisioterapeuta
            $validated['available'] = true; // Marcar como disponível após setup
            $validated['pricing_mode'] = $validated['pricing_mode'] ?? 'por_hora'; // Default para por hora
            $fisioterapeuta->update($validated);
        });
        
        return redirect()->route('fisioterapeuta.analise')
            ->with('success', 'Perfil configurado com sucesso! Sua conta está sendo analisada pela nossa equipe.');
    }
}
