<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assinaturas', function (Blueprint $table) {
            $table->string('mercadopago_subscription_id')->nullable()->after('monthly_price');
            $table->index('mercadopago_subscription_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assinaturas', function (Blueprint $table) {
            $table->dropIndex(['mercadopago_subscription_id']);
            $table->dropColumn('mercadopago_subscription_id');
        });
    }
};
