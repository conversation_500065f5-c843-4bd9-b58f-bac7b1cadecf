import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import axios from 'axios';
import { AlertCircle, Bell, Calendar, Check, CheckCheck, CheckCircle, Clock, ExternalLink, Filter, Trash2, Search } from 'lucide-react';
import { useState } from 'react';

interface Notificacao {
    id: number;
    tipo: string;
    titulo: string;
    mensagem: string;
    lida: boolean;
    data_envio: string;
    data_leitura?: string;
    agendamento?: {
        id: number;
        data_hora: string;
        status: string;
    };
}

interface NotificacoesIndexProps {
    notificacoes: {
        data: Notificacao[];
        links: any[];
        meta: any;
    };
    stats: {
        total: number;
        nao_lidas: number;
        lidas: number;
    };
    filtros: {
        status?: string;
        tipo?: string;
        search?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/paciente/dashboard' },
    { title: 'Notificações', href: '/notificacoes' },
];

export default function NotificacoesIndex({ notificacoes, stats, filtros }: NotificacoesIndexProps) {
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState<string>(filtros?.search ?? '');
    const [status, setStatus] = useState<string>(filtros?.status ?? 'todas');
    const [tipo, setTipo] = useState<string>(filtros?.tipo ?? 'todos');

    const marcarComoLida = async (notificacaoId: number) => {
        try {
            setLoading(true);
            await axios.post(`/notificacoes/${notificacaoId}/marcar-lida`);
            router.reload({ only: ['notificacoes', 'stats'] });
        } catch (error) {
            console.error('Erro ao marcar notificação como lida:', error);
        } finally {
            setLoading(false);
        }
    };

    const marcarTodasComoLidas = async () => {
        try {
            setLoading(true);
            await axios.post('/notificacoes/marcar-todas-lidas');
            router.reload({ only: ['notificacoes', 'stats'] });
        } catch (error) {
            console.error('Erro ao marcar todas como lidas:', error);
        } finally {
            setLoading(false);
        }
    };

    const deletarNotificacao = async (notificacaoId: number) => {
        if (!confirm('Tem certeza que deseja deletar esta notificação?')) {
            return;
        }

        try {
            setLoading(true);
            await axios.delete(`/notificacoes/${notificacaoId}`);
            router.reload({ only: ['notificacoes', 'stats'] });
        } catch (error) {
            console.error('Erro ao deletar notificação:', error);
        } finally {
            setLoading(false);
        }
    };

    const filtrarPorStatus = (value: string) => {
        setStatus(value);
    };

    const filtrarPorTipo = (value: string) => {
        setTipo(value);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/notificacoes', { status, tipo, search }, { preserveState: true, replace: true });
    };

    // Visualizar notificação específica
    const visualizarNotificacao = (notificacao: Notificacao) => {
        router.visit(`/notificacoes/${notificacao.id}`);
    };

    // Obter ícone de ação baseado no tipo
    const getActionIcon = (tipo: string) => {
        switch (tipo) {
            case 'novo_agendamento':
                return <Calendar className="h-4 w-4" />;
            case 'agendamento_confirmado':
                return <CheckCircle className="h-4 w-4" />;
            case 'agendamento_cancelado':
                return <AlertCircle className="h-4 w-4" />;
            case 'lembrete_sessao':
                return <Clock className="h-4 w-4" />;
            case 'sessao_iniciada':
            case 'sessao_finalizada':
                return <CheckCircle className="h-4 w-4" />;
            default:
                return <ExternalLink className="h-4 w-4" />;
        }
    };

    // Obter texto de ação baseado no tipo
    const getActionText = (tipo: string) => {
        switch (tipo) {
            case 'novo_agendamento':
                return 'Ver agendamento';
            case 'agendamento_confirmado':
                return 'Ver detalhes';
            case 'agendamento_cancelado':
                return 'Ver motivo';
            case 'lembrete_sessao':
                return 'Ver sessão';
            case 'sessao_iniciada':
                return 'Acompanhar';
            case 'sessao_finalizada':
                return 'Ver relatório';
            default:
                return 'Ver mais';
        }
    };

    const getTipoIcon = (tipo: string) => {
        switch (tipo) {
            case 'novo_agendamento':
                return '📅';
            case 'agendamento_confirmado':
                return '✅';
            case 'agendamento_cancelado':
                return '❌';
            case 'sessao_iniciada':
                return '▶️';
            case 'sessao_finalizada':
                return '🏁';
            case 'lembrete_sessao':
                return '⏰';
            default:
                return '📢';
        }
    };

    const getTipoColor = (tipo: string) => {
        switch (tipo) {
            case 'novo_agendamento':
                return 'bg-blue-100 text-blue-800';
            case 'agendamento_confirmado':
                return 'bg-green-100 text-green-800';
            case 'agendamento_cancelado':
                return 'bg-red-100 text-red-800';
            case 'sessao_iniciada':
                return 'bg-purple-100 text-purple-800';
            case 'sessao_finalizada':
                return 'bg-gray-100 text-gray-800';
            case 'lembrete_sessao':
                return 'bg-yellow-100 text-yellow-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getTipoLabel = (tipo: string) => {
        switch (tipo) {
            case 'novo_agendamento':
                return 'Novo Agendamento';
            case 'agendamento_confirmado':
                return 'Agendamento Confirmado';
            case 'agendamento_cancelado':
                return 'Agendamento Cancelado';
            case 'sessao_iniciada':
                return 'Sessão Iniciada';
            case 'sessao_finalizada':
                return 'Sessão Finalizada';
            case 'lembrete_sessao':
                return 'Lembrete de Sessão';
            default:
                return 'Notificação';
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Notificações" />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Notificações</h1>
                        <p className="text-muted-foreground">Gerencie suas notificações e mantenha-se atualizado</p>
                    </div>
                    {stats.nao_lidas > 0 && (
                        <Button onClick={marcarTodasComoLidas} disabled={loading}>
                            <CheckCheck className="mr-2 h-4 w-4" />
                            Marcar todas como lidas
                        </Button>
                    )}
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total</CardTitle>
                            <Bell className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Não Lidas</CardTitle>
                            <Badge variant="destructive" className="flex h-6 w-6 items-center justify-center rounded-full p-0 text-xs">
                                {stats.nao_lidas}
                            </Badge>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.nao_lidas}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Lidas</CardTitle>
                            <Check className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.lidas}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Busca (estilo Agendamentos) */}
                <div className="w-full">
                    <Card>
                        <CardContent className="p-6">
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
                                    <div className="flex-1">
                                        <label htmlFor="search" className="mb-2 block text-sm font-medium text-gray-700">
                                            Buscar Notificações
                                        </label>
                                        <Input
                                            id="search"
                                            type="text"
                                            placeholder="Ex.: título, mensagem..."
                                            value={search}
                                            onChange={(e) => setSearch(e.target.value)}
                                        />
                                    </div>

                                    <div className="w-full sm:w-48">
                                        <label htmlFor="status" className="mb-2 block text-sm font-medium text-gray-700">
                                            Status
                                        </label>
                                        <Select value={status} onValueChange={filtrarPorStatus}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Todos os status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="todas">Todos os status</SelectItem>
                                                <SelectItem value="nao_lidas">Não Lidas</SelectItem>
                                                <SelectItem value="lidas">Lidas</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="w-full sm:w-56">
                                        <label htmlFor="tipo" className="mb-2 block text-sm font-medium text-gray-700">
                                            Tipo
                                        </label>
                                        <Select value={tipo} onValueChange={filtrarPorTipo}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Todos os tipos" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="todos">Todos os Tipos</SelectItem>
                                                <SelectItem value="novo_agendamento">Novo Agendamento</SelectItem>
                                                <SelectItem value="agendamento_confirmado">Agendamento Confirmado</SelectItem>
                                                <SelectItem value="agendamento_cancelado">Agendamento Cancelado</SelectItem>
                                                <SelectItem value="sessao_iniciada">Sessão Iniciada</SelectItem>
                                                <SelectItem value="sessao_finalizada">Sessão Finalizada</SelectItem>
                                                <SelectItem value="lembrete_sessao">Lembrete de Sessão</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <Button type="submit" className="w-full sm:w-auto bg-green-500 hover:bg-green-600 text-black">
                                        <Search className="mr-2 h-4 w-4" />
                                        Pesquisar
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>

                {/* Lista de Notificações */}
                <div className="space-y-4">
                    {notificacoes.data.length === 0 ? (
                        <Card>
                            <CardContent className="flex flex-col items-center justify-center py-12">
                                <Bell className="mb-4 h-12 w-12 text-muted-foreground" />
                                <h3 className="mb-2 text-lg font-semibold">Nenhuma notificação encontrada</h3>
                                <p className="text-center text-muted-foreground">Não há notificações para exibir com os filtros selecionados.</p>
                            </CardContent>
                        </Card>
                    ) : (
                        notificacoes.data.map((notificacao) => (
                            <div key={notificacao.id} className="relative">
                                <Card className={`rounded-2xl border border-transparent bg-background p-6 shadow-sm ring-1 ring-foreground/10 transition-all hover:shadow-md ${!notificacao.lida ? 'border-l-4 border-l-blue-500' : ''}`}>
                                    <div className="flex items-start gap-4">
                                        <div className={`mt-1 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full ${getTipoColor(notificacao.tipo)}`}>
                                            <span className="text-lg">{getTipoIcon(notificacao.tipo)}</span>
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center justify-between gap-2 mb-1">
                                                <h3 className="text-base font-medium text-foreground truncate">{notificacao.titulo}</h3>
                                                {!notificacao.lida && (
                                                    <Badge variant="destructive" className="ml-2 flex-shrink-0">
                                                        Nova
                                                    </Badge>
                                                )}
                                            </div>
                                            <p className="text-sm text-muted-foreground mb-3">{notificacao.mensagem}</p>
                                            <div className="flex flex-wrap items-center justify-between gap-2">
                                                <div className="flex items-center gap-2 flex-wrap">
                                                    <Badge variant="outline" className={getTipoColor(notificacao.tipo)}>
                                                        {getTipoLabel(notificacao.tipo)}
                                                    </Badge>
                                                    <span className="text-xs text-muted-foreground">{notificacao.data_envio}</span>
                                                    {notificacao.lida && notificacao.data_leitura && (
                                                        <span className="text-xs text-muted-foreground">
                                                            • Lida em {notificacao.data_leitura}
                                                        </span>
                                                    )}
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <TooltipProvider>
                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="h-8 w-8 p-0 sm:w-auto sm:px-3 sm:py-1"
                                                                    onClick={() => visualizarNotificacao(notificacao)}
                                                                    disabled={loading}
                                                                >
                                                                    {getActionIcon(notificacao.tipo)}
                                                                    <span className="ml-1 hidden sm:inline">
                                                                        {getActionText(notificacao.tipo)}
                                                                    </span>
                                                                </Button>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>{getActionText(notificacao.tipo)}</p>
                                                            </TooltipContent>
                                                        </Tooltip>
                                                    </TooltipProvider>
                                                    {!notificacao.lida && (
                                                        <TooltipProvider>
                                                            <Tooltip>
                                                                <TooltipTrigger asChild>
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="icon"
                                                                        className="h-8 w-8 text-muted-foreground hover:text-foreground"
                                                                        onClick={() => marcarComoLida(notificacao.id)}
                                                                        disabled={loading}
                                                                    >
                                                                        <Check className="h-4 w-4" />
                                                                    </Button>
                                                                </TooltipTrigger>
                                                                <TooltipContent>
                                                                    <p>Marcar como lida</p>
                                                                </TooltipContent>
                                                            </Tooltip>
                                                        </TooltipProvider>
                                                    )}
                                                    <TooltipProvider>
                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <Button
                                                                    variant="destructive"
                                                                    size="icon"
                                                                    className="h-8 w-8"
                                                                    onClick={() => deletarNotificacao(notificacao.id)}
                                                                    disabled={loading}
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>Deletar notificação</p>
                                                            </TooltipContent>
                                                        </Tooltip>
                                                    </TooltipProvider>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Card>
                            </div>
                        ))
                    )}
                </div>

                {/* Paginação */}
                {notificacoes.links && notificacoes.links.length > 3 && (
                    <div className="flex justify-center">
                        <div className="flex space-x-1">
                            {notificacoes.links.map((link, index) => (
                                <Button
                                    key={index}
                                    variant={link.active ? 'default' : 'outline'}
                                    size="sm"
                                    disabled={!link.url}
                                    onClick={() => link.url && router.get(link.url)}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
