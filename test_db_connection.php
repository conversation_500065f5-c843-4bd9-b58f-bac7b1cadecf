<?php
// Database connection details
$db_connection = 'mysql';
$db_host = '***************';
$db_port = '3306';
$db_database = 'luisf488_db';
$db_username = 'luisf488_user';
$db_password = 'OZ8xgA}~b~kO';

echo "Testing database connection...\n\n";

try {
    // Create connection
    if ($db_connection === 'mysql') {
        $dsn = "mysql:host=$db_host;port=$db_port;dbname=$db_database;charset=utf8mb4";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        
        $pdo = new PDO($dsn, $db_username, $db_password, $options);
        
        echo "✅ Database connection successful!\n";
        echo "Host: $db_host:$db_port\n";
        echo "Database: $db_database\n";
        echo "Username: $db_username\n\n";
        
        // Test a simple query
        $stmt = $pdo->query("SELECT VERSION() as version");
        $version = $stmt->fetch();
        echo "MySQL Version: " . $version['version'] . "\n";
        
        // Test database exists and is accessible
        $stmt = $pdo->query("SELECT DATABASE() as current_db");
        $current_db = $stmt->fetch();
        echo "Current Database: " . $current_db['current_db'] . "\n";
        
        // Count tables
        $stmt = $pdo->query("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = '$db_database'");
        $table_count = $stmt->fetch();
        echo "Number of tables: " . $table_count['table_count'] . "\n";
        
    } else {
        echo "❌ Unsupported database type: $db_connection\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database connection failed!\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "Error Code: " . $e->getCode() . "\n";
    
    // Provide more specific error information
    if ($e->getCode() == 2002) {
        echo "💡 This suggests the host '$db_host' is not reachable or MySQL is not running.\n";
    } elseif ($e->getCode() == 1045) {
        echo "💡 This suggests the username or password is incorrect.\n";
    } elseif ($e->getCode() == 1049) {
        echo "💡 This suggests the database '$db_database' does not exist.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Unexpected error: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
?>
