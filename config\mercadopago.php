<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Mercado Pago Configuration
    |--------------------------------------------------------------------------
    |
    | Configurações para integração com o Mercado Pago
    |
    */

    'access_token' => env('MERCADOPAGO_ACCESS_TOKEN'),
    'public_key' => env('MERCADOPAGO_PUBLIC_KEY'),
    'base_url' => 'https://api.mercadopago.com',
    'webhook_secret' => env('MERCADOPAGO_WEBHOOK_SECRET'),

    'webhook' => [
        'url' => '/webhook/mercadopago',
        'events' => [
            'payment',
            'subscription_preapproval',
            'subscription_preapproval_plan',
        ],
    ],

    'subscription' => [
        'default_frequency' => 1,
        'default_frequency_type' => 'months',
        'default_currency' => 'BRL',
    ],

    'payment_methods' => [
        'credit_card' => ['visa', 'master', 'amex', 'elo', 'hipercard'],
        'debit_card' => ['debvisa', 'debmaster'],
        'pix' => ['pix'],
        'boleto' => ['bolbradesco'],
    ],

    'status_mapping' => [
        'payment' => [
            'approved' => 'pago',
            'pending' => 'pendente',
            'in_process' => 'pendente',
            'rejected' => 'falhou',
            'cancelled' => 'falhou',
        ],
        'subscription' => [
            'pending' => 'suspensa',
            'authorized' => 'ativa',
            'paused' => 'suspensa',
            'cancelled' => 'cancelada',
        ],
    ],
];
