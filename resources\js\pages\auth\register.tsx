import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useEffect } from 'react';

import { FormFeedback, useFormFeedback } from '@/components/form/form-feedback';
import { LoadingButton } from '@/components/form/loading-button';
import { ValidatedInput } from '@/components/form/validated-input';
import { ValidatedSelect } from '@/components/form/validated-select';
import TextLink from '@/components/text-link';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useFormValidation } from '@/hooks/use-form-validation';
import AuthLayout from '@/layouts/auth-layout';

type RegisterForm = {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
    role: string;
    terms_accepted: boolean;
};

export default function Register() {
    const { data, setData, post, processing, errors, reset } = useForm<Required<RegisterForm>>({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: 'paciente',
        terms_accepted: false,
    });

    const { feedback, showError, clearFeedback } = useFormFeedback();

    const {
        errors: validationErrors,
        validateForm,
        validateField,
        clearError,
    } = useFormValidation({
        name: { required: true, minLength: 2, maxLength: 255 },
        email: { required: true, email: true, maxLength: 255 },
        password: { required: true, password: true },
        password_confirmation: { required: true, passwordConfirmation: 'password' },
        role: { required: true },
        terms_accepted: { required: true },
    });

    // Sincronizar apenas erros gerais do servidor (não específicos de campos)
    useEffect(() => {
        if (Object.keys(errors).length > 0) {
            // Verifica se há erros que não são de campos específicos
            const generalErrors = Object.values(errors).find(error => 
                error && !['name', 'email', 'password', 'password_confirmation', 'role', 'terms_accepted'].includes(
                    Object.keys(errors).find(key => errors[key as keyof typeof errors] === error) || ''
                )
            );
            
            if (generalErrors) {
                showError(generalErrors);
            }
        }
    }, [errors, showError]);

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        clearFeedback();

        // Validação frontend primeiro
        if (!validateForm(data)) {
            showError('Por favor, corrija os erros no formulário.');
            return;
        }

        post(route('register'), {
            onFinish: () => reset('password', 'password_confirmation'),
            onError: () => {
                showError('Erro ao criar conta. Verifique os dados e tente novamente.');
            },
        });
    };

    const roleOptions = [
        { value: 'paciente', label: 'Paciente' },
        { value: 'fisioterapeuta', label: 'Fisioterapeuta' },
    ];

    return (
        <AuthLayout title="Criar uma conta" description="Digite seus dados abaixo para criar sua conta">
            <Head title="Cadastro" />

            {feedback && (
                <FormFeedback type={feedback.type} message={feedback.message} title={feedback.title} onClose={clearFeedback} className="mb-6" />
            )}

            <form className="flex flex-col gap-6" onSubmit={submit}>
                <div className="grid gap-6">
                    <ValidatedInput
                        id="name"
                        label="Nome"
                        type="text"
                        required
                        autoFocus
                        tabIndex={1}
                        autoComplete="name"
                        value={data.name}
                        onChange={(value) => {
                            setData('name', value);
                            clearError('name');
                        }}
                        onBlur={(value) => validateField('name', value)}
                        disabled={processing}
                        placeholder="Nome completo"
                        error={validationErrors.name || errors.name}
                        success={!validationErrors.name && !errors.name && data.name.length > 0}
                        tooltip="Digite seu nome completo"
                    />

                    <ValidatedInput
                        id="email"
                        label="Endereço de email"
                        type="email"
                        required
                        tabIndex={2}
                        autoComplete="email"
                        value={data.email}
                        onChange={(value) => {
                            setData('email', value.toLowerCase()); // Normaliza email para minúsculas
                            clearError('email');
                        }}
                        onBlur={(value) => validateField('email', value)}
                        disabled={processing}
                        placeholder="<EMAIL>"
                        error={validationErrors.email || errors.email}
                        success={!validationErrors.email && !errors.email && data.email.length > 0}
                        tooltip="Digite um email válido"
                    />

                    <ValidatedSelect
                        id="role"
                        label="Tipo de usuário"
                        placeholder="Selecione o tipo de usuário"
                        value={data.role}
                        options={roleOptions}
                        onChange={(value) => {
                            setData('role', value);
                            clearError('role');
                        }}
                        disabled={processing}
                        error={validationErrors.role || errors.role}
                        success={!validationErrors.role && !errors.role && data.role.length > 0}
                        required
                        tooltip="Selecione o tipo de conta que deseja criar"
                    />

                    <ValidatedInput
                        id="password"
                        label="Senha"
                        type="password"
                        required
                        tabIndex={4}
                        autoComplete="new-password"
                        value={data.password}
                        onChange={(value) => {
                            setData('password', value);
                            clearError('password');
                        }}
                        onBlur={(value) => validateField('password', value)}
                        disabled={processing}
                        placeholder="Senha"
                        error={validationErrors.password || errors.password}
                        success={!validationErrors.password && !errors.password && data.password.length > 0}
                        tooltip="Mínimo 8 caracteres, incluindo maiúscula, minúscula e número"
                    />

                    <ValidatedInput
                        id="password_confirmation"
                        label="Confirmar senha"
                        type="password"
                        required
                        tabIndex={5}
                        autoComplete="new-password"
                        value={data.password_confirmation}
                        onChange={(value) => {
                            setData('password_confirmation', value);
                            clearError('password_confirmation');
                        }}
                        onBlur={(value) => validateField('password_confirmation', value)}
                        disabled={processing}
                        placeholder="Confirmar senha"
                        error={validationErrors.password_confirmation || errors.password_confirmation}
                        success={!validationErrors.password_confirmation && !errors.password_confirmation && data.password_confirmation.length > 0}
                        tooltip="Digite a mesma senha novamente"
                    />

                    <div className="flex items-start space-x-3">
                        <Checkbox
                            id="terms_accepted"
                            name="terms_accepted"
                            checked={data.terms_accepted}
                            onCheckedChange={(checked) => {
                                setData('terms_accepted', !!checked);
                                clearError('terms_accepted');
                            }}
                            disabled={processing}
                            tabIndex={6}
                            className={validationErrors.terms_accepted || errors.terms_accepted ? 'border-red-500' : ''}
                        />
                        <div className="grid gap-1.5 leading-none">
                            <Label
                                htmlFor="terms_accepted"
                                className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                                Aceito os{' '}
                                <TextLink href={route('termos-uso')} target="_blank" className="underline">
                                    Termos de Uso
                                </TextLink>{' '}
                                e a{' '}
                                <TextLink href={route('politica-privacidade')} target="_blank" className="underline">
                                    Política de Privacidade
                                </TextLink>
                            </Label>
                            {(validationErrors.terms_accepted || errors.terms_accepted) && (
                                <p className="flex items-center gap-1 text-sm text-red-600">
                                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            fillRule="evenodd"
                                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                            clipRule="evenodd"
                                        />
                                    </svg>
                                    {validationErrors.terms_accepted || errors.terms_accepted}
                                </p>
                            )}
                        </div>
                    </div>

                    <LoadingButton type="submit" className="mt-2 w-full" tabIndex={7} loading={processing} loadingText="Criando conta...">
                        Criar conta
                    </LoadingButton>
                </div>

                <div className="text-center text-sm text-muted-foreground">
                    Já tem uma conta?{' '}
                    <TextLink href={route('login')} tabIndex={8}>
                        Entrar
                    </TextLink>
                </div>
            </form>
        </AuthLayout>
    );
}
