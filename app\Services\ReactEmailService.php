<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;

class ReactEmailService
{
    /**
     * Renderizar um template React Email
     */
    public function renderTemplate(string $templateName, array $props = []): string
    {
        try {
            $scriptPath = base_path('scripts/render-email.cjs');
            $propsJson = json_encode($props);
            
            // Criar processo para executar o script Node.js
            $process = new Process([
                'node',
                $scriptPath,
                $templateName,
                $propsJson
            ]);
            
            $process->setTimeout(30); // 30 segundos timeout
            $process->run();
            
            if (!$process->isSuccessful()) {
                throw new ProcessFailedException($process);
            }
            
            $html = $process->getOutput();
            
            if (empty($html)) {
                throw new \Exception("Template renderizado está vazio");
            }
            
            Log::info("Template React Email renderizado com sucesso", [
                'template' => $templateName,
                'props_count' => count($props)
            ]);
            
            return $html;
            
        } catch (\Exception $e) {
            Log::error("Erro ao renderizar template React Email", [
                'template' => $templateName,
                'error' => $e->getMessage(),
                'props' => $props
            ]);
            
            // Fallback para template Blade se React Email falhar
            return $this->getFallbackTemplate($templateName, $props);
        }
    }
    
    /**
     * Renderizar template de boas-vindas
     */
    public function renderBoasVindas(string $nome, string $email, string $tipoUsuario, ?string $loginUrl = null): string
    {
        return $this->renderTemplate('boas-vindas', [
            'nome' => $nome,
            'email' => $email,
            'tipoUsuario' => $tipoUsuario,
            'loginUrl' => $loginUrl ?? route('login'),
        ]);
    }
    
    /**
     * Renderizar template de agendamento confirmado
     */
    public function renderAgendamentoConfirmado(array $dados): string
    {
        return $this->renderTemplate('agendamento-confirmado', [
            'pacienteNome' => $dados['paciente_nome'],
            'fisioterapeutaNome' => $dados['fisioterapeuta_nome'],
            'dataHora' => $dados['data_hora'],
            'endereco' => $dados['endereco'],
            'valor' => $dados['valor'] ?? null,
            'observacoes' => $dados['observacoes'] ?? null,
            'agendamentoUrl' => $dados['agendamento_url'] ?? null,
            'whatsappFisioterapeuta' => $dados['whatsapp_fisioterapeuta'] ?? null,
        ]);
    }
    
    /**
     * Renderizar template de lembrete de agendamento
     */
    public function renderLembreteAgendamento(array $dados): string
    {
        return $this->renderTemplate('lembrete-agendamento', [
            'pacienteNome' => $dados['paciente_nome'],
            'fisioterapeutaNome' => $dados['fisioterapeuta_nome'],
            'dataHora' => $dados['data_hora'],
            'endereco' => $dados['endereco'],
            'horasAntecedencia' => $dados['horas_antecedencia'] ?? 24,
            'valor' => $dados['valor'] ?? null,
            'agendamentoUrl' => $dados['agendamento_url'] ?? null,
            'whatsappFisioterapeuta' => $dados['whatsapp_fisioterapeuta'] ?? null,
        ]);
    }
    
    /**
     * Renderizar template de agendamento cancelado
     */
    public function renderAgendamentoCancelado(array $dados): string
    {
        return $this->renderTemplate('agendamento-cancelado', [
            'pacienteNome' => $dados['paciente_nome'],
            'fisioterapeutaNome' => $dados['fisioterapeuta_nome'],
            'dataHora' => $dados['data_hora'],
            'motivo' => $dados['motivo'] ?? null,
            'reagendarUrl' => $dados['reagendar_url'] ?? route('paciente.fisioterapeutas.index'),
            'buscarUrl' => $dados['buscar_url'] ?? route('buscar'),
        ]);
    }
    
    /**
     * Fallback para templates Blade existentes
     */
    private function getFallbackTemplate(string $templateName, array $props): string
    {
        try {
            // Mapear templates React para templates Blade
            $bladeTemplates = [
                'boas-vindas' => 'emails.welcome',
                'agendamento-confirmado' => 'emails.agendamento-confirmado',
                'lembrete-agendamento' => 'emails.lembrete-agendamento',
                'agendamento-cancelado' => 'emails.agendamento-cancelado',
            ];
            
            $bladeTemplate = $bladeTemplates[$templateName] ?? null;
            
            if ($bladeTemplate && view()->exists($bladeTemplate)) {
                return view($bladeTemplate, $props)->render();
            }
            
            // Template básico de fallback
            return $this->getBasicFallbackTemplate($templateName, $props);
            
        } catch (\Exception $e) {
            Log::error("Erro no fallback template", [
                'template' => $templateName,
                'error' => $e->getMessage()
            ]);
            
            return $this->getBasicFallbackTemplate($templateName, $props);
        }
    }
    
    /**
     * Template HTML básico de emergência
     */
    private function getBasicFallbackTemplate(string $templateName, array $props): string
    {
        $content = '';
        
        switch ($templateName) {
            case 'boas-vindas':
                $content = "
                    <h1>Bem-vindo(a) à F4 Fisio!</h1>
                    <p>Olá {$props['nome']},</p>
                    <p>Seja bem-vindo(a) à nossa plataforma!</p>
                ";
                break;
                
            case 'agendamento-confirmado':
                $content = "
                    <h1>Agendamento Confirmado</h1>
                    <p>Olá {$props['pacienteNome']},</p>
                    <p>Seu agendamento foi confirmado para {$props['dataHora']}.</p>
                ";
                break;
                
            default:
                $content = "
                    <h1>F4 Fisio</h1>
                    <p>Obrigado por usar nossos serviços!</p>
                ";
        }
        
        return "
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='utf-8'>
                <title>F4 Fisio</title>
            </head>
            <body style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
                {$content}
                <hr>
                <p style='color: #666; font-size: 12px;'>
                    © 2025 F4 Fisio. Todos os direitos reservados.
                </p>
            </body>
            </html>
        ";
    }
    
    /**
     * Verificar se Node.js está disponível
     */
    public function isNodeAvailable(): bool
    {
        try {
            $process = new Process(['node', '--version']);
            $process->run();
            return $process->isSuccessful();
        } catch (\Exception $e) {
            return false;
        }
    }
}
