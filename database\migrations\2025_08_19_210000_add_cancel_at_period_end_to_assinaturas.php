<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Early guard: if the column already exists (possibly created via a hotfix), skip
        if (Schema::hasColumn('assinaturas', 'cancel_at_period_end')) {
            return;
        }
        Schema::table('assinaturas', function (Blueprint $table) {
            $table->boolean('cancel_at_period_end')->default(false)->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assinaturas', function (Blueprint $table) {
            if (Schema::hasColumn('assinaturas', 'cancel_at_period_end')) {
                $table->dropColumn('cancel_at_period_end');
            }
        });
    }
};
