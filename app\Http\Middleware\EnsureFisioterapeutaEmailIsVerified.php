<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\URL;
use Symfony\Component\HttpFoundation\Response;

class EnsureFisioterapeutaEmailIsVerified
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        // Se não é fisioterapeuta, prossegue normalmente
        if (!$user || $user->role !== 'fisioterapeuta') {
            return $next($request);
        }

        // Se o email não está verificado
        if (!$user->hasVerifiedEmail()) {
            return $request->expectsJson()
                ? abort(409, 'Seu email precisa ser verificado.')
                : Redirect::route('fisioterapeuta.verificar-email');
        }

        return $next($request);
    }
}
