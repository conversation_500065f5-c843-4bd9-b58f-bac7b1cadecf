<?php

namespace App\Listeners;

use App\Services\NotificacaoService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Log;

class SendWelcomeEmail
{

    public function __construct(
        private NotificacaoService $notificacaoService
    ) {}

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        try {
            // Enviar email de boas-vindas
            $this->notificacaoService->enviarEmailBoasVindas($event->user);
            
            Log::info('Listener SendWelcomeEmail executado com sucesso', [
                'user_id' => $event->user->id,
                'email' => $event->user->email
            ]);
            
        } catch (\Exception $e) {
            Log::error('Erro no listener SendWelcomeEmail', [
                'user_id' => $event->user->id ?? null,
                'email' => $event->user->email ?? null,
                'error' => $e->getMessage()
            ]);
        }
    }
}
