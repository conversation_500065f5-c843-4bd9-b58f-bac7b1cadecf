import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/toast-notification';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, useForm, usePage } from '@inertiajs/react';
import { AlertTriangle, ArrowLeft, Calendar, CheckCircle, Clock, FileText, Home, Mail, MapPin, Phone, User, XCircle } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Início', href: '/paciente/dashboard' },
    { title: 'Agendamentos', href: '/paciente/agendamentos' },
    { title: 'Detalhes', href: '' },
];

export default function PacienteAgendamentoShow() {
    const pageProps = usePage().props as any;
    const { agendamento, podeEditar, podeCancelar, pagamentoPendente } = pageProps;
    const [showCancelDialog, setShowCancelDialog] = useState(false);

    const { processing } = useForm();
    const { showSuccess, showError } = useToast();
    const [isCancelling, setIsCancelling] = useState(false);

    const handleCancel = () => {
        if (isCancelling) return;
        setIsCancelling(true);
        router.post(
            route('paciente.agendamentos.cancel', agendamento.id),
            {},
            {
                preserveScroll: true,
                onSuccess: () => {
                    showSuccess('Agendamento cancelado com sucesso.');
                    setShowCancelDialog(false);
                    router.visit(route('paciente.agendamentos.index'));
                },
                onError: (errors: Record<string, string>) => {
                    const msg = errors?.cancel || 'Não foi possível cancelar o agendamento.';
                    showError(msg);
                    setShowCancelDialog(false);
                },
                onFinish: () => {
                    setIsCancelling(false);
                },
            },
        );
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            pendente: 'outline',
            agendado: 'default',
            confirmado: 'secondary',
            'a caminho': 'default',
            concluido: 'default',
            cancelado: 'destructive',
            em_andamento: 'secondary',
        } as const;

        const colors = {
            pendente: 'text-yellow-600',
            agendado: 'text-blue-600',
            confirmado: 'text-green-600',
            'a caminho': 'text-orange-600',
            concluido: 'text-green-700 bg-green-50',
            cancelado: 'text-red-600',
            em_andamento: 'text-purple-600 bg-purple-50',
        };

        const labels = {
            pendente: 'Pendente',
            agendado: 'Agendado',
            confirmado: 'Confirmado',
            'a caminho': 'A Caminho',
            concluido: 'Concluído',
            cancelado: 'Cancelado',
            em_andamento: 'Em Andamento',
        };

        const icons = {
            pendente: <Clock className="h-4 w-4" />,
            agendado: <Clock className="h-4 w-4" />,
            confirmado: <CheckCircle className="h-4 w-4" />,
            'a caminho': <MapPin className="h-4 w-4" />,
            concluido: <CheckCircle className="h-4 w-4" />,
            cancelado: <XCircle className="h-4 w-4" />,
            em_andamento: <Clock className="h-4 w-4 animate-pulse" />,
        };

        return (
            <Badge
                variant={variants[status as keyof typeof variants] || 'default'}
                className={`flex items-center gap-1 ${colors[status as keyof typeof colors] || ''}`}
            >
                {icons[status as keyof typeof icons]}
                {labels[status as keyof typeof labels] || status}
            </Badge>
        );
    };

    const formatDate = (dateString: string | null | undefined) => {
        if (!dateString) return 'Data não informada';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'Data inválida';
            return date.toLocaleDateString('pt-BR', {
                day: '2-digit',
                month: 'long',
                year: 'numeric',
                weekday: 'long',
            });
        } catch {
            return 'Data inválida';
        }
    };

    const formatTime = (timeString: string | null | undefined) => {
        if (!timeString) return '--:--';
        return timeString.substring(0, 5);
    };

    const formatDateTime = (dateTimeString: string) => {
        return new Date(dateTimeString).toLocaleString('pt-BR');
    };

    const formatAddress = (endereco: any) => {
        if (!endereco) return 'Endereço não informado';

        const { logradouro, numero, complemento, bairro, cidade, cep, estado } = endereco;

        // Construir endereço formatado
        let enderecoFormatado = logradouro || 'Endereço não informado';

        if (numero) {
            enderecoFormatado += `, ${numero}`;
        }

        if (complemento) {
            enderecoFormatado += ` - ${complemento}`;
        }

        if (bairro) {
            enderecoFormatado += `, ${bairro}`;
        }

        if (cidade) {
            enderecoFormatado += `, ${cidade}`;
        }

        if (estado) {
            enderecoFormatado += ` - ${estado}`;
        }

        if (cep) {
            enderecoFormatado += ` - CEP: ${cep}`;
        }

        return enderecoFormatado;
    };

    // elegibilidade de cancelamento (espelho da regra do backend)
    const getScheduledDateTime = () => {
        // combina data_agendamento (YYYY-MM-DD ou DD/MM/YYYY) + horario (HH:mm)
        try {
            const d = agendamento.data_agendamento;
            const t = agendamento.horario?.slice(0, 5) || '00:00';
            // tenta ISO direto; se vier em DD/MM/YYYY, reordena
            let isoDate = d;
            if (/^\d{2}\/\d{2}\/\d{4}$/.test(d)) {
                const [dd, mm, yyyy] = d.split('/');
                isoDate = `${yyyy}-${mm}-${dd}`;
            }
            const dt = new Date(`${isoDate}T${t}:00`);
            return isNaN(dt.getTime()) ? null : dt;
        } catch {
            return null;
        }
    };

    const now = new Date();
    const createdAt = new Date(agendamento.created_at);
    const scheduledAt = getScheduledDateTime();
    const withinCreationGrace = !isNaN(createdAt.getTime()) && now.getTime() <= createdAt.getTime() + 24 * 60 * 60 * 1000;
    const has24hBefore = scheduledAt ? scheduledAt.getTime() - now.getTime() >= 24 * 60 * 60 * 1000 : false;
    const canCancelByPolicy = withinCreationGrace || has24hBefore;

    const humanizeDuration = (ms: number) => {
        if (ms <= 0) return '0h';
        const hours = Math.floor(ms / (60 * 60 * 1000));
        const minutes = Math.floor((ms % (60 * 60 * 1000)) / (60 * 1000));
        if (hours >= 24) {
            const days = Math.floor(hours / 24);
            const remH = hours % 24;
            return `${days}d ${remH}h`;
        }
        return `${hours}h ${minutes}m`;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Agendamento - ${formatDate(agendamento.data_agendamento)}`} />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                <div className="mb-8">
                    <div className="mb-4">
                        <Link href={route('paciente.agendamentos.index')}>
                            <Button variant="ghost" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar aos Agendamentos
                            </Button>
                        </Link>
                    </div>
                    <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">
                        {agendamento.tipo === 'sessao' ? 'Sessão de Fisioterapia' : 'Avaliação'}
                    </h1>
                    <p className="text-muted-foreground">
                        {formatDate(agendamento.data_agendamento)} às {formatTime(agendamento.horario)}
                    </p>
                </div>

                {/* Alertas baseados no status */}
                {agendamento.status === 'cancelado' && (
                    <Alert>
                        <XCircle className="h-4 w-4" />
                        <AlertDescription>Este agendamento foi cancelado.</AlertDescription>
                    </Alert>
                )}

                {/* Alerta de pagamento pendente */}
                {pagamentoPendente && (
                    <Alert className="border-red-200 bg-red-50">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <AlertDescription className="flex flex-col gap-2">
                            <span className="text-red-800">
                                O pagamento para este agendamento está pendente. Por favor, realize o pagamento para confirmar sua sessão.
                            </span>
                            {agendamento.pagamento?.payment_link ? (
                                <Button
                                    variant="destructive"
                                    size="sm"
                                    className="w-fit bg-red-600 hover:bg-red-700"
                                    onClick={() => window.open(agendamento.pagamento.payment_link, '_blank')}
                                >
                                    Realizar Pagamento
                                </Button>
                            ) : (
                                <Button
                                    variant="destructive"
                                    size="sm"
                                    className="w-fit bg-red-600 hover:bg-red-700"
                                    onClick={() => {
                                        // Fallback: recarregar a página para tentar gerar o link novamente
                                        window.location.reload();
                                    }}
                                >
                                    Carregar Opção de Pagamento
                                </Button>
                            )}
                        </AlertDescription>
                    </Alert>
                )}

                {agendamento.status === 'confirmado' && (
                    <Alert>
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>
                            Profissional confirmado! Você será notificado quando ele estiver a caminho do atendimento.
                        </AlertDescription>
                    </Alert>
                )}

                {agendamento.status === 'a caminho' && (
                    <Alert className="border-blue-200 bg-blue-50">
                        <Clock className="h-4 w-4 text-blue-600" />
                        <AlertDescription className="flex flex-col gap-2">
                            <span className="font-medium text-blue-800">Seu fisioterapeuta está a caminho!</span>
                            <span className="text-sm text-blue-700">
                                • Prepare o espaço do atendimento (cerca de 2m² de espaço livre)
                                <br />
                                • Certifique-se de que o acesso está livre
                                <br />
                                • Posicione equipamentos necessários (se aplicável)
                                <br />• O profissional chegará em breve
                            </span>
                        </AlertDescription>
                    </Alert>
                )}

                {agendamento.status === 'em_andamento' && (
                    <Alert className="border-green-200 bg-green-50">
                        <Clock className="h-4 w-4 animate-pulse text-green-600" />
                        <AlertDescription className="flex flex-col gap-2">
                            <span className="font-medium text-green-800">Sessão em andamento</span>
                            <span className="text-sm text-green-700">
                                A sessão de fisioterapia está sendo realizada. Você receberá notificações sobre o progresso.
                            </span>
                        </AlertDescription>
                    </Alert>
                )}

                {agendamento.status === 'concluido' && (
                    <Alert className="border-green-200 bg-green-50">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <AlertDescription className="flex flex-col gap-2">
                            <span className="font-medium text-green-800">Atendimento concluído com sucesso!</span>
                            <span className="text-sm text-green-700">
                                Não esqueça de avaliar o atendimento. Você pode verificar o relatório completo para acompanhar o progresso.
                            </span>
                            <div className="mt-2">
                                <Link href={route('paciente.historico.show', agendamento.id)}>
                                    <Button variant="outline" size="sm" className="border-green-300 bg-white text-green-700 hover:bg-green-50">
                                        <FileText className="mr-2 h-4 w-4" />
                                        Ver Relatório Completo
                                    </Button>
                                </Link>
                            </div>
                        </AlertDescription>
                    </Alert>
                )}

                {agendamento.status === 'agendado' && (
                    <Alert>
                        <Clock className="h-4 w-4" />
                        <AlertDescription>
                            Agendamento realizado com sucesso! Aguarde a confirmação do fisioterapeuta sobre sua disponibilidade.
                        </AlertDescription>
                    </Alert>
                )}

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Informações do Agendamento */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                                <span className="flex items-center gap-2">
                                    <Calendar className="h-5 w-5" />
                                    Detalhes do Agendamento
                                </span>
                                {getStatusBadge(agendamento.status)}
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm text-muted-foreground">Data</p>
                                    <p className="font-medium">{formatDate(agendamento.data_agendamento)}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-muted-foreground">Horário</p>
                                    <p className="font-medium">{formatTime(agendamento.horario)}</p>
                                </div>
                            </div>

                            <div>
                                <p className="text-sm text-muted-foreground">Tipo</p>
                                <p className="font-medium">{agendamento.tipo === 'sessao' ? 'Sessão de Fisioterapia' : 'Avaliação'}</p>
                            </div>

                            {agendamento.observacoes && (
                                <div>
                                    <p className="text-sm text-muted-foreground">Observações</p>
                                    <p className="rounded-md bg-muted p-3 text-sm">{agendamento.observacoes}</p>
                                </div>
                            )}

                            <Separator />

                            <div className="space-y-1 text-xs text-muted-foreground">
                                <p>Agendado em: {formatDateTime(agendamento.created_at)}</p>
                                {agendamento.updated_at !== agendamento.created_at && (
                                    <p>Última atualização: {formatDateTime(agendamento.updated_at)}</p>
                                )}
                                {agendamento.status === 'agendado' && (
                                    <div className="mt-2 space-y-1 rounded-md border p-2">
                                        {canCancelByPolicy ? (
                                            <>
                                                {withinCreationGrace ? (
                                                    <p>
                                                        Você pode cancelar livremente dentro de 24h após criar este agendamento. Janela encerra em:{' '}
                                                        <b>{humanizeDuration(createdAt.getTime() + 24 * 60 * 60 * 1000 - now.getTime())}</b>
                                                    </p>
                                                ) : (
                                                    <p>
                                                        Você pode cancelar até 24h antes do horário marcado. Tempo restante até a janela fechar:{' '}
                                                        <b>
                                                            {scheduledAt
                                                                ? humanizeDuration(scheduledAt.getTime() - 24 * 60 * 60 * 1000 - now.getTime())
                                                                : '--'}
                                                        </b>
                                                    </p>
                                                )}
                                            </>
                                        ) : (
                                            <p className="text-destructive">
                                                Cancelamento indisponível: já passaram 24h da criação e faltam menos de 24h para o horário marcado.
                                            </p>
                                        )}
                                    </div>
                                )}
                            </div>

                            {/* Ações */}
                            {agendamento.status === 'agendado' && (
                                <div className="flex gap-2 pt-4">
                                    {podeCancelar && (
                                        <Button variant="destructive" size="sm" onClick={() => setShowCancelDialog(true)} disabled={processing}>
                                            Cancelar Agendamento
                                        </Button>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Informações do Fisioterapeuta */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Fisioterapeuta
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <p className="text-lg font-medium">{agendamento.fisioterapeuta.user.name}</p>
                                <p className="text-sm text-muted-foreground">CREFITO: {agendamento.fisioterapeuta.crefito}</p>
                            </div>

                            {agendamento.fisioterapeuta.especialidades.length > 0 && (
                                <div>
                                    <p className="mb-2 text-sm text-muted-foreground">Especialidades</p>
                                    <div className="flex flex-wrap gap-1">
                                        {agendamento.fisioterapeuta.especialidades.map((especialidade: string, index: number) => (
                                            <Badge key={index} variant="secondary" className="text-xs">
                                                {especialidade}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>
                            )}

                            <Separator />

                            <div className="space-y-3">
                                <div className="flex items-center gap-2">
                                    <Mail className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm">{agendamento.fisioterapeuta.user.email}</span>
                                </div>

                                {agendamento.fisioterapeuta.user.phone && (
                                    <div className="flex items-center gap-2">
                                        <Phone className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm">{agendamento.fisioterapeuta.user.phone}</span>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Endereço do Atendimento */}
                    <Card className="md:col-span-2">
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <MapPin className="h-5 w-5" />
                                    Local do Atendimento
                                </div>
                                {['pendente', 'agendado', 'confirmado'].includes(agendamento.status) && (
                                    <Link href={route('paciente.agendamentos.edit-address', agendamento.id)}>
                                        <Button variant="outline" size="sm">
                                            Editar Endereço
                                        </Button>
                                    </Link>
                                )}
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div className="flex items-start gap-2">
                                <Home className="mt-0.5 h-4 w-4 text-muted-foreground" />
                                <div className="flex-1">
                                    <p className="text-sm font-medium">
                                        {agendamento.appointment_type === 'domicilio'
                                            ? 'Atendimento a Domicílio'
                                            : agendamento.appointment_type === 'clinica'
                                              ? 'Atendimento na Clínica'
                                              : 'Atendimento ' + agendamento.appointment_type}
                                    </p>
                                    <p className="text-sm text-muted-foreground">
                                        {agendamento.appointment_type === 'domicilio'
                                            ? 'Serviço realizado no domicílio do paciente'
                                            : agendamento.appointment_type === 'clinica'
                                              ? 'Serviço realizado na clínica do profissional'
                                              : 'Tipo de atendimento personalizado'}
                                    </p>
                                    {agendamento.appointment_type_notes && (
                                        <p className="mt-1 text-sm text-gray-600 italic">{agendamento.appointment_type_notes}</p>
                                    )}
                                </div>
                            </div>
                            <div className="rounded-md bg-muted p-3">
                                <p className="text-sm">{formatAddress(agendamento.endereco_atendimento)}</p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Timeline de Status - disponível apenas para agendamentos ativos */}
                    {agendamento.status !== 'cancelado' && (
                        <Card className="md:col-span-2">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Clock className="h-5 w-5" />
                                    Timeline do Agendamento
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {/* Seção "O que acontece agora?" */}
                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                                    <h4 className="mb-2 font-medium text-blue-900">O que acontece agora?</h4>
                                    <div className="space-y-1 text-sm text-blue-800">
                                        <p>• Aguarde a confirmação do profissional sobre a disponibilidade</p>
                                        <p>• Quando confirmado, acompanhe o deslocamento em tempo real (a caminho/chegou)</p>
                                        <p>• Receberá notificações do início e progresso da sessão</p>
                                        <p>• Após a conclusão, será possível avaliar o atendimento</p>
                                        <p>• Todo o progresso ficará registrado no seu histórico</p>
                                    </div>
                                </div>

                                <div className="space-y-3">
                                    {/* Timeline de Status Melhorada */}
                                    {(() => {
                                        const currentStatus = agendamento.status;
                                        // Remover redundância: quando pagamento é confirmado, agendamento é automaticamente realizado
                                        const statusOrder = ['pendente', 'confirmado', 'a caminho', 'em_andamento', 'concluido'];
                                        const currentIndex = statusOrder.indexOf(currentStatus);

                                        // Verificar se o pagamento foi realizado
                                        const isPagamentoPago =
                                            agendamento.pagamento &&
                                            (agendamento.pagamento.status === 'approved' || agendamento.pagamento.status === 'pago');

                                        // Se status é pendente e não pagou, mostrar apenas pendente
                                        // Se status é pendente e pagou, mostrar pendente como completo e agendado como atual
                                        // Para outros status, mostrar normalmente

                                        return statusOrder.map((statusKey, index) => {
                                            let isCompleted = false;
                                            let isCurrent = false;
                                            let isNext = false;
                                            let shouldShow = true;

                                            if (currentStatus === 'pendente') {
                                                if (isPagamentoPago) {
                                                    // Pagamento foi feito, agendamento está confirmado
                                                    isCompleted = statusKey === 'pendente';
                                                    isCurrent = statusKey === 'confirmado';
                                                    shouldShow = index <= 1; // Mostrar pendente e confirmado
                                                } else {
                                                    // Pagamento não foi feito
                                                    isCurrent = statusKey === 'pendente';
                                                    shouldShow = index === 0; // Mostrar apenas pendente
                                                }
                                            } else if (currentStatus === 'agendado') {
                                                // Status agendado = confirmado na nova timeline
                                                isCompleted = statusKey === 'pendente';
                                                isCurrent = statusKey === 'confirmado';
                                                isNext = statusKey === 'a caminho';
                                                shouldShow = index <= 2; // Mostrar até "a caminho"
                                            } else {
                                                // Para outros status, lógica normal
                                                isCompleted = index < currentIndex;
                                                isCurrent = index === currentIndex;
                                                isNext = index === currentIndex + 1;
                                                shouldShow = index <= currentIndex + 1; // Mostrar até próximo passo
                                            }

                                            if (!shouldShow) return null;

                                            const statusInfo = {
                                                pendente: {
                                                    current: {
                                                        title: 'Pagamento Pendente',
                                                        description: 'Aguardando confirmação de pagamento',
                                                        icon: 'clock',
                                                    },
                                                    completed: {
                                                        title: 'Pagamento Confirmado',
                                                        description: 'Pagamento processado com sucesso',
                                                        icon: 'check',
                                                    },
                                                },
                                                confirmado: {
                                                    current: {
                                                        title: 'Aguardando Profissional',
                                                        description: 'Profissional foi notificado e deve confirmar',
                                                        icon: 'user-check',
                                                    },
                                                    completed: {
                                                        title: 'Profissional Confirmado',
                                                        description: 'Profissional confirmou a assistência',
                                                        icon: 'user-check',
                                                    },
                                                },
                                                'a caminho': {
                                                    current: {
                                                        title: 'Profissional a Caminho',
                                                        description: 'O fisioterapeuta está se deslocando para o atendimento',
                                                        icon: 'navigation',
                                                    },
                                                    completed: {
                                                        title: 'Profissional Em Deslocamento',
                                                        description: 'Profissional está a caminho do local',
                                                        icon: 'navigation',
                                                    },
                                                },
                                                em_andamento: {
                                                    current: {
                                                        title: 'Sessão Em Andamento',
                                                        description: 'A sessão de fisioterapia está sendo realizada',
                                                        icon: 'play',
                                                    },
                                                    completed: {
                                                        title: 'Sessão Iniciada',
                                                        description: 'Atendimento foi iniciado',
                                                        icon: 'play',
                                                    },
                                                },
                                                concluido: {
                                                    current: {
                                                        title: 'Atendimento Concluído',
                                                        description: 'Sessão finalizada com sucesso',
                                                        icon: 'success',
                                                    },
                                                    completed: {
                                                        title: 'Atendimento Finalizado',
                                                        description: 'Procure o relatório para detalhes',
                                                        icon: 'success',
                                                    },
                                                },
                                            };

                                            const statusConfig = statusInfo[statusKey as keyof typeof statusInfo];

                                            if (!statusConfig) return null;

                                            // Choose the appropriate message based on status
                                            const info = isCompleted ? statusConfig.completed : statusConfig.current;

                                            return (
                                                <div key={statusKey} className="flex items-start gap-3">
                                                    {/* Status indicator */}
                                                    <div className="mt-1 h-3 w-3 shrink-0 rounded-full">
                                                        {isCompleted ? (
                                                            <div className="h-3 w-3 rounded-full bg-green-500"></div>
                                                        ) : isCurrent ? (
                                                            <div className="h-3 w-3 animate-pulse rounded-full bg-blue-500"></div>
                                                        ) : isNext ? (
                                                            <div className="h-3 w-3 rounded-full bg-blue-300"></div>
                                                        ) : (
                                                            <div className="h-3 w-3 rounded-full bg-gray-300"></div>
                                                        )}
                                                    </div>

                                                    <div className="flex-1">
                                                        <p
                                                            className={`text-sm font-medium ${
                                                                isCompleted
                                                                    ? 'text-green-700'
                                                                    : isCurrent
                                                                      ? 'text-blue-700'
                                                                      : isNext
                                                                        ? 'text-blue-600'
                                                                        : 'text-gray-500'
                                                            }`}
                                                        >
                                                            {info.title}
                                                        </p>
                                                        <p
                                                            className={`text-xs ${
                                                                isCompleted
                                                                    ? 'text-green-600'
                                                                    : isCurrent
                                                                      ? 'text-blue-600'
                                                                      : isNext
                                                                        ? 'text-blue-500'
                                                                        : 'text-gray-400'
                                                            }`}
                                                        >
                                                            {info.description}
                                                        </p>
                                                    </div>

                                                    {/* Status icon */}
                                                    <div
                                                        className={`flex h-4 w-4 shrink-0 items-center justify-center rounded-full ${
                                                            isCompleted
                                                                ? 'bg-green-100'
                                                                : isCurrent
                                                                  ? 'bg-blue-100'
                                                                  : isNext
                                                                    ? 'bg-blue-100'
                                                                    : 'bg-gray-100'
                                                        }`}
                                                    >
                                                        {/* Renderiza ícone baseado no tipo */}
                                                        {isCompleted ? (
                                                            <svg className="h-2.5 w-2.5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                                <path
                                                                    fillRule="evenodd"
                                                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                                    clipRule="evenodd"
                                                                />
                                                            </svg>
                                                        ) : isCurrent ? (
                                                            <svg
                                                                className="h-2.5 w-2.5 animate-spin text-blue-600"
                                                                fill="currentColor"
                                                                viewBox="0 0 20 20"
                                                            >
                                                                <path
                                                                    fillRule="evenodd"
                                                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                                                    clipRule="evenodd"
                                                                />
                                                            </svg>
                                                        ) : isNext ? (
                                                            <svg className="h-2.5 w-2.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                                <path
                                                                    fillRule="evenodd"
                                                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                                                    clipRule="evenodd"
                                                                />
                                                            </svg>
                                                        ) : (
                                                            <svg className="h-2.5 w-2.5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                                <path
                                                                    fillRule="evenodd"
                                                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                                                    clipRule="evenodd"
                                                                />
                                                            </svg>
                                                        )}
                                                    </div>
                                                </div>
                                            );
                                        });
                                    })()}

                                    {/* Próximos passos */}
                                    <div className="mt-6 rounded-lg border border-blue-200 bg-blue-50 p-4">
                                        <h4 className="mb-2 font-medium text-blue-900">Próximos Passos</h4>
                                        <div className="space-y-1 text-sm text-blue-800">
                                            {agendamento.status === 'agendado' && (
                                                <p>• Aguarde a confirmação do profissional sobre a disponibilidade</p>
                                            )}
                                            {agendamento.status === 'confirmado' && (
                                                <>
                                                    <p>• O profissional está se preparando para o atendimento</p>
                                                    <p>• Você receberá uma notificação quando ele estiver a caminho</p>
                                                </>
                                            )}
                                            {agendamento.status === 'a caminho' && (
                                                <>
                                                    <p>• O profissional está a caminho do local</p>
                                                    <p>• Você pode acompanhar a localização em tempo real</p>
                                                    <p>• Prepare o espaço para o atendimento</p>
                                                </>
                                            )}
                                            {agendamento.status === 'em_andamento' && (
                                                <>
                                                    <p>• Sessão iniciada, deve durar aproximadamente 60 minutos</p>
                                                    <p>• Você receberá notificações sobre o progresso</p>
                                                </>
                                            )}
                                            {agendamento.status === 'concluido' && (
                                                <>
                                                    <p>• Atendimento finalizado com sucesso</p>
                                                    <p>• Não esqueça de avaliar o atendimento</p>
                                                    <p>• Verifique o relatório para detalhes completos</p>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>

                {/* Dialog de Confirmação de Cancelamento */}
                {showCancelDialog && (
                    <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
                        <Card className="w-full max-w-md">
                            <CardHeader>
                                <CardTitle>Confirmar Cancelamento</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <p className="text-sm text-muted-foreground">
                                    Tem certeza que deseja cancelar este agendamento? Esta ação não pode ser desfeita.
                                </p>
                                <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={() => setShowCancelDialog(false)} disabled={processing}>
                                        Não, manter agendamento
                                    </Button>
                                    <Button variant="destructive" onClick={handleCancel} disabled={processing || isCancelling}>
                                        {processing || isCancelling ? 'Cancelando...' : 'Sim, cancelar'}
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
