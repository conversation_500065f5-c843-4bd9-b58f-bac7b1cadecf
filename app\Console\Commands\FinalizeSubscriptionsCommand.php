<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Assinatura;
use App\Services\MercadoPagoService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class FinalizeSubscriptionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assinaturas:finalizar-periodo';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Finaliza assinaturas com cancelamento agendado cujo período terminou e garante estado consistente no gateway e local.';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if (!config('features.plans_enabled')) {
            $this->info('Planos desabilitados. Comando ignorado.');
            return Command::SUCCESS;
        }

        $agora = Carbon::now();
        $this->info('Iniciando finalização de assinaturas em ' . $agora->toDateTimeString());

        Assinatura::where('status', 'ativa')
            ->where('cancel_at_period_end', true)
            ->whereDate('end_date', '<=', $agora)
            ->orderBy('id')
            ->chunkById(500, function ($assinaturas) {
                foreach ($assinaturas as $assinatura) {
                    DB::beginTransaction();
                    try {
                        // Segurança extra: garantir cancelamento no gateway
                        if (!empty($assinatura->mercadopago_subscription_id)) {
                            $mp = new MercadoPagoService();
                            $ok = $mp->cancelSubscription($assinatura->mercadopago_subscription_id);
                            if (!$ok) {
                                Log::warning('Falha ao cancelar no MP durante finalização periódica', [
                                    'assinatura_id' => $assinatura->id,
                                    'mercadopago_subscription_id' => $assinatura->mercadopago_subscription_id,
                                ]);
                            }
                        }

                        // Finalizar localmente: no próximo ciclo fica inativa
                        $assinatura->status = 'inativa';
                        $assinatura->cancel_at_period_end = false;
                        $assinatura->save();

                        if ($assinatura->user) {
                            $assinatura->user->has_subscription = false;
                            $assinatura->user->save();
                        }

                        DB::commit();
                        $this->line("Assinatura {$assinatura->id} finalizada.");
                    } catch (\Throwable $e) {
                        DB::rollBack();
                        Log::error('Erro ao finalizar assinatura ao fim do ciclo', [
                            'assinatura_id' => $assinatura->id,
                            'error' => $e->getMessage(),
                        ]);
                        $this->error("Erro ao finalizar assinatura {$assinatura->id}: {$e->getMessage()}");
                    }
                }
            });

        $this->info('Finalização de assinaturas concluída.');
        return Command::SUCCESS;
    }
}
