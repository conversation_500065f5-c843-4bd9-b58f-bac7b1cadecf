import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { BarChart3, Calendar, DollarSign, Download, Eye, FileText, Filter, TrendingUp, Users } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/fisioterapeuta/dashboard',
    },
    {
        title: 'Relatórios',
        href: '/fisioterapeuta/relatorios',
    },
];

interface RelatorioSessao {
    id: number;
    agendamento_id: number;
    observations: string;
    exercises: string;
    progress_notes: string;
    next_steps: string;
    mobility_assessment: string;
    pain_level_before: number;
    pain_level_after: number;
    created_at: string;
    agendamento: {
        id: number;
        scheduled_at: string;
        paciente: {
            id: number;
            name: string;
        };
    };
}

interface Stats {
    total_relatorios: number;
    relatorios_mes: number;
    pacientes_atendidos: number;
    sessoes_concluidas: number;
    receita_mes: number;
    media_dor_antes: number;
    media_dor_depois: number;
    melhoria_dor: number;
}

interface Props {
    relatorios: {
        data: RelatorioSessao[];
        links: any[];
        meta: any;
    };
    stats: Stats;
    filters: {
        search?: string;
        periodo?: string;
        paciente_id?: string;
    };
    pacientes: Array<{
        id: number;
        name: string;
    }>;
}

export default function FisioterapeutaRelatorios({ relatorios, stats, filters, pacientes }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters?.search || '');
    const [selectedPeriodo, setSelectedPeriodo] = useState(filters?.periodo || '');
    const [selectedPaciente, setSelectedPaciente] = useState(filters?.paciente_id || '');

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    const formatDateTime = (dateString: string) => {
        return new Date(dateString).toLocaleString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getMobilityLabel = (assessment: string) => {
        const labels = {
            limitada: 'Limitada',
            parcial: 'Parcial',
            normal: 'Normal',
            melhorada: 'Melhorada',
        };
        return labels[assessment as keyof typeof labels] || assessment;
    };

    const getMobilityColor = (assessment: string) => {
        const colors = {
            limitada: 'bg-red-100 text-red-800',
            parcial: 'bg-yellow-100 text-yellow-800',
            normal: 'bg-blue-100 text-blue-800',
            melhorada: 'bg-green-100 text-green-800',
        };
        return colors[assessment as keyof typeof colors] || 'bg-gray-100 text-gray-800';
    };

    const handleSearch = () => {
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (selectedPeriodo && selectedPeriodo !== 'todos') params.append('periodo', selectedPeriodo);
        if (selectedPaciente && selectedPaciente !== 'todos') params.append('paciente_id', selectedPaciente);

        window.location.href = `/fisioterapeuta/relatorios?${params.toString()}`;
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedPeriodo('todos');
        setSelectedPaciente('todos');
        window.location.href = '/fisioterapeuta/relatorios';
    };

    const exportRelatorios = () => {
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (selectedPeriodo && selectedPeriodo !== 'todos') params.append('periodo', selectedPeriodo);
        if (selectedPaciente && selectedPaciente !== 'todos') params.append('paciente_id', selectedPaciente);

        window.open(`/fisioterapeuta/relatorios/export?${params.toString()}`, '_blank');
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Relatórios" />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold">Relatórios</h1>
                        <p className="text-muted-foreground">Acompanhe o progresso dos seus pacientes e suas estatísticas</p>
                    </div>
                    <div className="flex gap-2">
                        <Button onClick={exportRelatorios} variant="outline">
                            <Download className="mr-2 h-4 w-4" />
                            Exportar
                        </Button>
                        <Button asChild>
                            <Link href="/fisioterapeuta/relatorios/create">
                                <FileText className="mr-2 h-4 w-4" />
                                Novo Relatório
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Estatísticas */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total de Relatórios</CardTitle>
                            <FileText className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.total_relatorios || 0}</div>
                            <p className="text-xs text-muted-foreground">relatórios criados</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Este Mês</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.relatorios_mes || 0}</div>
                            <p className="text-xs text-muted-foreground">relatórios este mês</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pacientes Atendidos</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.pacientes_atendidos || 0}</div>
                            <p className="text-xs text-muted-foreground">pacientes únicos</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Receita do Mês</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats?.receita_mes || 0)}</div>
                            <p className="text-xs text-muted-foreground">sessões concluídas</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Métricas de Dor */}
                <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Dor Média (Antes)</CardTitle>
                            <BarChart3 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.media_dor_antes?.toFixed(1) || '0.0'}</div>
                            <p className="text-xs text-muted-foreground">escala 0-10</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Dor Média (Depois)</CardTitle>
                            <BarChart3 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.media_dor_depois?.toFixed(1) || '0.0'}</div>
                            <p className="text-xs text-muted-foreground">escala 0-10</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Melhoria da Dor</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">
                                {stats?.melhoria_dor > 0 ? '+' : ''}
                                {stats?.melhoria_dor?.toFixed(1) || '0.0'}%
                            </div>
                            <p className="text-xs text-muted-foreground">redução média</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Filtros */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filtros
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-4">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Buscar</label>
                                <Input placeholder="Observações, exercícios..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Período</label>
                                <Select value={selectedPeriodo} onValueChange={setSelectedPeriodo}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Todos os períodos" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="todos">Todos os períodos</SelectItem>
                                        <SelectItem value="hoje">Hoje</SelectItem>
                                        <SelectItem value="semana">Esta semana</SelectItem>
                                        <SelectItem value="mes">Este mês</SelectItem>
                                        <SelectItem value="trimestre">Este trimestre</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Paciente</label>
                                <Select value={selectedPaciente} onValueChange={setSelectedPaciente}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Todos os pacientes" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="todos">Todos os pacientes</SelectItem>
                                        {pacientes?.map((paciente) => (
                                            <SelectItem key={paciente.id} value={paciente.id.toString()}>
                                                {paciente.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex items-end gap-2">
                                <Button onClick={handleSearch} className="flex-1">
                                    Filtrar
                                </Button>
                                <Button variant="outline" onClick={clearFilters}>
                                    Limpar
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Lista de Relatórios */}
                {relatorios?.data?.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <FileText className="h-12 w-12 text-muted-foreground" />
                            <h3 className="mt-4 text-lg font-semibold">Nenhum relatório encontrado</h3>
                            <p className="text-muted-foreground">
                                {filters?.search || filters?.periodo || filters?.paciente_id
                                    ? 'Tente ajustar os filtros para encontrar relatórios.'
                                    : 'Você ainda não criou relatórios. Eles aparecerão aqui após as sessões.'}
                            </p>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="space-y-4">
                        {relatorios?.data?.map((relatorio) => (
                            <Card key={relatorio.id} className="transition-shadow hover:shadow-md">
                                <CardContent className="p-6">
                                    <div className="flex flex-col gap-4 lg:flex-row lg:items-start lg:justify-between">
                                        <div className="flex-1 space-y-3">
                                            <div className="flex items-start justify-between">
                                                <div>
                                                    <h3 className="font-semibold">{relatorio.agendamento.paciente.name}</h3>
                                                    <p className="text-sm text-muted-foreground">
                                                        Sessão: {formatDateTime(relatorio.agendamento.scheduled_at)}
                                                    </p>
                                                    <p className="text-sm text-muted-foreground">Relatório: {formatDateTime(relatorio.created_at)}</p>
                                                </div>
                                                <Badge className={getMobilityColor(relatorio.mobility_assessment)}>
                                                    {getMobilityLabel(relatorio.mobility_assessment)}
                                                </Badge>
                                            </div>

                                            <div className="grid gap-2 sm:grid-cols-2">
                                                <div className="flex items-center gap-2 text-sm">
                                                    <span className="font-medium">Dor antes:</span>
                                                    <Badge variant="outline">{relatorio.pain_level_before}/10</Badge>
                                                </div>
                                                <div className="flex items-center gap-2 text-sm">
                                                    <span className="font-medium">Dor depois:</span>
                                                    <Badge variant="outline">{relatorio.pain_level_after}/10</Badge>
                                                </div>
                                            </div>

                                            {relatorio.observations && (
                                                <div className="rounded-md bg-muted p-3">
                                                    <p className="mb-1 text-sm font-medium">Observações:</p>
                                                    <p className="text-sm">{relatorio.observations}</p>
                                                </div>
                                            )}

                                            {relatorio.exercises && (
                                                <div className="rounded-md bg-muted p-3">
                                                    <p className="mb-1 text-sm font-medium">Exercícios:</p>
                                                    <p className="text-sm">{relatorio.exercises}</p>
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex flex-col gap-2 lg:items-end">
                                            <div className="flex gap-2">
                                                <Button asChild size="sm" variant="outline">
                                                    <Link href={`/fisioterapeuta/relatorios/${relatorio.id}`}>
                                                        <Eye className="mr-2 h-4 w-4" />
                                                        Ver Completo
                                                    </Link>
                                                </Button>
                                                <Button asChild size="sm" variant="outline">
                                                    <Link href={`/fisioterapeuta/relatorios/${relatorio.id}/edit`}>Editar</Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}

                {/* Paginação */}
                {relatorios?.links && relatorios.links.length > 3 && (
                    <div className="flex justify-center">
                        <div className="flex gap-2">
                            {relatorios.links.map((link: any, index: number) => (
                                <Button key={index} variant={link.active ? 'default' : 'outline'} size="sm" asChild={!!link.url} disabled={!link.url}>
                                    {link.url ? (
                                        <Link href={link.url} dangerouslySetInnerHTML={{ __html: link.label }} />
                                    ) : (
                                        <span dangerouslySetInnerHTML={{ __html: link.label }} />
                                    )}
                                </Button>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
