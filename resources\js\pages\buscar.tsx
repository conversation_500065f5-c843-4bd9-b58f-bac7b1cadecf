import BuscaEstabelecimentos from '@/components/busca-estabelecimentos';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import PublicLayout from '@/layouts/public-layout';
import { MapPin, MessageCircle, MessageSquare, Search } from 'lucide-react';

export default function Buscar() {
    return (
        <PublicLayout
            title="Buscar Fisioterapeutas - F4 Fisio"
            description="Encontre fisioterapeutas próximos a você. Busca por localização com contato direto via WhatsApp."
        >
            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-20 md:py-36">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-5xl font-medium text-balance md:text-6xl">
                                Encontre
                                <span className="block text-primary">Fisioterapeutas Próximos</span>
                            </h1>
                            <p className="mx-auto my-8 max-w-3xl text-xl text-balance text-muted-foreground">
                                Localize fisioterapeutas na sua região. Contato direto via WhatsApp e informações completas.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Busca Section */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <BuscaEstabelecimentos />
                </div>
            </section>

            {/* Como Funciona Section */}
            <section className="bg-muted/30 py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Como Funciona a Busca</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Processo simples e rápido para encontrar os melhores fisioterapeutas
                        </p>
                    </div>

                    <div className="mt-16 grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="mb-4 h-12 w-12">
                                <MapPin className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Informe sua Localização</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Digite seu CEP, cidade ou use sua localização atual para encontrar fisioterapeutas próximos.
                            </p>
                        </div>

                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="mb-4 h-12 w-12">
                                <Search className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Explore os Profissionais</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Veja avaliações, distância e especialidades para escolher o fisioterapeuta ideal.
                            </p>
                        </div>

                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="mb-4 h-12 w-12">
                                <MessageSquare className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Entre em Contato</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Clique no botão WhatsApp para conversar diretamente com o fisioterapeuta e agendar.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Removido: seção para empresas */}
        </PublicLayout>
    );
}
