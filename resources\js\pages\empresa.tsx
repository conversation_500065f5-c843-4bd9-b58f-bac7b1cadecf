import { Button } from '@/components/ui/button';
import PublicLayout from '@/layouts/public-layout';
import { Users, Building2, Bar<PERSON>hart3, CheckCircle } from 'lucide-react';

export default function Empresa() {
    return (
        <PublicLayout
            title="Empresa - F4 Fisio"
            description="Soluções corporativas de fisioterapia para sua empresa. Prevenção que gera resultados com saúde e bem-estar para sua equipe."
        >
            {/* Hero Section */}
            <section className="relative bg-gradient-to-br from-green-600 via-green-500 to-white">
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="relative mx-auto max-w-7xl px-4 py-32 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h1 className="mx-auto max-w-4xl text-5xl font-bold text-white md:text-6xl">
                            Prevenção que gera resultados para sua empresa.
                        </h1>
                        <p className="mx-auto mt-8 max-w-3xl text-2xl text-white/90">
                            Por apenas R$ 1.000/mês, sua equipe tem saúde, bem-estar e mais produtividade.
                        </p>
                        <div className="mt-12">
                            <Button size="lg" className="bg-white text-green-600 hover:bg-gray-100 text-lg px-8 py-4">
                                Quero Implantar na Minha Empresa
                            </Button>
                        </div>
                    </div>
                </div>
                {/* Background image with overlay */}
                <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')] bg-cover bg-center bg-no-repeat mix-blend-overlay"></div>
            </section>

            {/* Sobre a F4 Fisio Section */}
            <section className="bg-background py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-4xl font-bold text-foreground">Sobre a F4 Fisio</h2>
                        <p className="mx-auto mt-4 max-w-2xl text-xl text-muted-foreground">
                            Conheça nossos diferenciais e como podemos transformar a saúde da sua equipe
                        </p>
                    </div>

                    <div className="grid gap-8 md:grid-cols-3">
                        {/* Cuidado com pessoas */}
                        <div className="text-center">
                            <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
                                <Users className="h-10 w-10 text-green-600" />
                            </div>
                            <h3 className="mt-6 text-xl font-semibold text-foreground">Cuidado com pessoas</h3>
                            <p className="mt-4 text-base leading-relaxed text-muted-foreground">
                                Atendimento humanizado e personalizado para cada colaborador, focando no bem-estar integral e qualidade de vida.
                            </p>
                        </div>

                        {/* Resultados corporativos */}
                        <div className="text-center">
                            <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
                                <Building2 className="h-10 w-10 text-green-600" />
                            </div>
                            <h3 className="mt-6 text-xl font-semibold text-foreground">Resultados corporativos</h3>
                            <p className="mt-4 text-base leading-relaxed text-muted-foreground">
                                Redução de absenteísmo, aumento de produtividade e melhoria no clima organizacional com programas eficazes.
                            </p>
                        </div>

                        {/* Indicadores confiáveis */}
                        <div className="text-center">
                            <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
                                <BarChart3 className="h-10 w-10 text-green-600" />
                            </div>
                            <h3 className="mt-6 text-xl font-semibold text-foreground">Indicadores confiáveis</h3>
                            <p className="mt-4 text-base leading-relaxed text-muted-foreground">
                                Métricas claras de ROI, relatórios detalhados e acompanhamento contínuo dos resultados do programa.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* O Plano Corporativo Section */}
            <section className="bg-muted/30 py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-4xl font-bold text-foreground">O Plano Corporativo</h2>
                        <p className="mx-auto mt-4 max-w-2xl text-xl text-muted-foreground">
                            Tudo o que sua empresa precisa em um único plano acessível
                        </p>
                    </div>

                    <div className="mx-auto max-w-4xl">
                        <div className="rounded-2xl border-2 border-green-500 bg-white p-8 shadow-xl">
                            <div className="text-center mb-8">
                                <div className="mx-auto inline-flex items-center justify-center rounded-full bg-green-100 px-4 py-2">
                                    <span className="text-lg font-semibold text-green-600">PLANO EMPRESARIAL</span>
                                </div>
                                <div className="mt-4">
                                    <span className="text-5xl font-bold text-foreground">R$ 1.000</span>
                                    <span className="text-xl text-muted-foreground">/mês</span>
                                </div>
                                <p className="mt-2 text-muted-foreground">Para toda a sua equipe</p>
                            </div>

                            <div className="space-y-4">
                                <div className="flex items-center gap-3">
                                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                                    <span className="text-foreground">✅ Avaliação ergonômica completa do ambiente de trabalho</span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                                    <span className="text-foreground">✅ Sessões de fisioterapia preventiva e corretiva</span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                                    <span className="text-foreground">✅ Palestras educativas sobre saúde e postura</span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                                    <span className="text-foreground">✅ Atendimento a lesões relacionadas ao trabalho</span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                                    <span className="text-foreground">✅ Programa de exercícios personalizados</span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                                    <span className="text-foreground">✅ Relatórios mensais de resultados e indicadores</span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                                    <span className="text-foreground">✅ Suporte 24/7 para emergências</span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                                    <span className="text-foreground">✅ Equipe de fisioterapeutas especializados</span>
                                </div>
                            </div>

                            <div className="mt-8 text-center">
                                <Button size="lg" className="bg-green-600 hover:bg-green-700 text-white w-full py-4 text-lg">
                                    Contratar Plano Corporativo
                                </Button>
                                <p className="mt-4 text-sm text-muted-foreground">
                                    Sem taxa de adesão • Cancele quando quiser • Suporte dedicado
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="bg-background py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mx-auto max-w-4xl rounded-2xl bg-gradient-to-r from-green-600 to-green-500 p-8 text-center text-white shadow-xl">
                        <h2 className="text-3xl font-bold mb-4">Transforme a saúde da sua equipe hoje mesmo</h2>
                        <p className="text-xl mb-8 text-white/90">
                            Entre em contato com nosso time comercial e descubra como podemos personalizar o melhor plano para as necessidades da sua empresa.
                        </p>
                        <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
                            <Button size="lg" variant="secondary" className="text-green-600 hover:bg-gray-100">
                                Falar com Especialista
                            </Button>
                            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600">
                                Agendar Demonstração
                            </Button>
                        </div>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
