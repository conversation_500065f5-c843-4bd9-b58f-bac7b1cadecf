import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { AlertCircle, Calendar, Clock, Plus, Save, Trash2 } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/fisioterapeuta/dashboard',
    },
    {
        title: 'Disponibilidade',
        href: '/fisioterapeuta/disponibilidade',
    },
];

interface HorarioBase {
    id?: number;
    dia_semana: number;
    hora_inicio: string;
    hora_fim: string;
    periodo_nome: string;
    ativo: boolean;
}

interface Disponibilidade {
    id?: number;
    tipo: 'disponivel' | 'indisponivel';
    data_inicio: string;
    data_fim?: string;
    hora_inicio?: string;
    hora_fim?: string;
    dias_semana?: number[];
    recorrente: boolean;
    motivo?: string;
    ativo: boolean;
}

interface Props {
    horariosBase: HorarioBase[];
    disponibilidades: Disponibilidade[];
    fisioterapeuta: {
        available: boolean;
    };
}

const diasSemana = [
    { value: 0, label: 'Domingo' },
    { value: 1, label: 'Segunda-feira' },
    { value: 2, label: 'Terça-feira' },
    { value: 3, label: 'Quarta-feira' },
    { value: 4, label: 'Quinta-feira' },
    { value: 5, label: 'Sexta-feira' },
    { value: 6, label: 'Sábado' },
];

export default function FisioterapeutaDisponibilidade({ horariosBase, disponibilidades, fisioterapeuta }: Props) {
    const [activeTab, setActiveTab] = useState<'horarios' | 'excecoes'>('horarios');
    const [editingHorario, setEditingHorario] = useState<HorarioBase | null>(null);
    const [editingDisponibilidade, setEditingDisponibilidade] = useState<Disponibilidade | null>(null);

    // Form para disponibilidade geral
    const {
        data: availabilityData,
        setData: setAvailabilityData,
        post: postAvailability,
        processing: processingAvailability,
    } = useForm({
        available: fisioterapeuta.available,
    });

    // Form para horários base
    const {
        data: horarioData,
        setData: setHorarioData,
        post: postHorario,
        put: putHorario,
        delete: deleteHorario,
        processing: processingHorario,
        reset: resetHorario,
    } = useForm({
        dia_semana: 1,
        hora_inicio: '08:00',
        hora_fim: '18:00',
        periodo_nome: 'Manhã',
        ativo: true,
    });

    // Form para exceções
    const {
        data: excecaoData,
        setData: setExcecaoData,
        post: postExcecao,
        put: putExcecao,
        delete: deleteExcecao,
        processing: processingExcecao,
        reset: resetExcecao,
    } = useForm({
        tipo: 'indisponivel',
        data_inicio: '',
        data_fim: '',
        hora_inicio: '',
        hora_fim: '',
        dias_semana: [],
        recorrente: false,
        motivo: '',
        ativo: true,
    });

    const handleAvailabilityToggle = () => {
        postAvailability(route('fisioterapeuta.disponibilidade.toggle'));
    };

    const handleSaveHorario = () => {
        if (editingHorario?.id) {
            putHorario(route('fisioterapeuta.horarios.base.update', editingHorario.id), {
                onSuccess: () => {
                    setEditingHorario(null);
                    resetHorario();
                },
            });
        } else {
            postHorario(route('fisioterapeuta.horarios.base.store'), {
                onSuccess: () => {
                    resetHorario();
                },
            });
        }
    };

    const handleEditHorario = (horario: HorarioBase) => {
        setEditingHorario(horario);
        setHorarioData(horario as any);
    };

    const handleDeleteHorario = (id: number) => {
        if (confirm('Tem certeza que deseja excluir este horário?')) {
            deleteHorario(route('fisioterapeuta.horarios.base.destroy', id));
        }
    };

    const handleSaveExcecao = () => {
        if (editingDisponibilidade?.id) {
            putExcecao(route('fisioterapeuta.horarios.excecoes.update', editingDisponibilidade.id), {
                onSuccess: () => {
                    setEditingDisponibilidade(null);
                    resetExcecao();
                },
            });
        } else {
            postExcecao(route('fisioterapeuta.horarios.excecoes.store'), {
                onSuccess: () => {
                    resetExcecao();
                },
            });
        }
    };

    const handleEditDisponibilidade = (disponibilidade: Disponibilidade) => {
        setEditingDisponibilidade(disponibilidade);
        setExcecaoData(disponibilidade as any);
    };

    const handleDeleteDisponibilidade = (id: number) => {
        if (confirm('Tem certeza que deseja excluir esta exceção?')) {
            deleteExcecao(route('fisioterapeuta.horarios.excecoes.destroy', id));
        }
    };

    const getDiaSemanaLabel = (dia: number) => {
        return diasSemana.find((d) => d.value === dia)?.label || '';
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Disponibilidade" />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold">Disponibilidade</h1>
                        <p className="text-muted-foreground">Configure seus horários de atendimento e exceções</p>
                    </div>
                </div>

                {/* Status Geral */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Clock className="h-5 w-5" />
                            Status de Disponibilidade
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="font-medium">Aceitar novos agendamentos</h3>
                                <p className="text-sm text-muted-foreground">Quando desabilitado, pacientes não poderão agendar consultas</p>
                            </div>
                            <div className="flex items-center gap-3">
                                <Badge variant={availabilityData.available ? 'default' : 'secondary'}>
                                    {availabilityData.available ? 'Disponível' : 'Indisponível'}
                                </Badge>
                                <Switch
                                    checked={availabilityData.available}
                                    onCheckedChange={(checked) => {
                                        setAvailabilityData('available', checked);
                                        handleAvailabilityToggle();
                                    }}
                                    disabled={processingAvailability}
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Tabs */}
                <div className="flex space-x-1 rounded-lg bg-muted p-1">
                    <button
                        onClick={() => setActiveTab('horarios')}
                        className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                            activeTab === 'horarios' ? 'bg-background text-foreground shadow-sm' : 'text-muted-foreground hover:text-foreground'
                        }`}
                    >
                        Horários Base
                    </button>
                    <button
                        onClick={() => setActiveTab('excecoes')}
                        className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                            activeTab === 'excecoes' ? 'bg-background text-foreground shadow-sm' : 'text-muted-foreground hover:text-foreground'
                        }`}
                    >
                        Exceções
                    </button>
                </div>

                {/* Horários Base */}
                {activeTab === 'horarios' && (
                    <div className="space-y-6">
                        {/* Formulário de Horário */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Plus className="h-5 w-5" />
                                    {editingHorario ? 'Editar Horário' : 'Adicionar Horário'}
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                    <div className="space-y-2">
                                        <Label>Dia da Semana</Label>
                                        <Select
                                            value={horarioData.dia_semana.toString()}
                                            onValueChange={(value) => setHorarioData('dia_semana', parseInt(value))}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {diasSemana.map((dia) => (
                                                    <SelectItem key={dia.value} value={dia.value.toString()}>
                                                        {dia.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="space-y-2">
                                        <Label>Hora Início</Label>
                                        <Input
                                            type="time"
                                            value={horarioData.hora_inicio}
                                            onChange={(e) => setHorarioData('hora_inicio', e.target.value)}
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label>Hora Fim</Label>
                                        <Input
                                            type="time"
                                            value={horarioData.hora_fim}
                                            onChange={(e) => setHorarioData('hora_fim', e.target.value)}
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label>Período</Label>
                                        <Input
                                            placeholder="Ex: Manhã, Tarde"
                                            value={horarioData.periodo_nome}
                                            onChange={(e) => setHorarioData('periodo_nome', e.target.value)}
                                        />
                                    </div>
                                </div>
                                <div className="flex items-center gap-4">
                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="ativo"
                                            checked={horarioData.ativo}
                                            onCheckedChange={(checked) => setHorarioData('ativo', checked as any)}
                                        />
                                        <Label htmlFor="ativo">Ativo</Label>
                                    </div>
                                </div>
                                <div className="flex gap-2">
                                    <Button onClick={handleSaveHorario} disabled={processingHorario}>
                                        <Save className="mr-2 h-4 w-4" />
                                        {editingHorario ? 'Atualizar' : 'Adicionar'}
                                    </Button>
                                    {editingHorario && (
                                        <Button
                                            variant="outline"
                                            onClick={() => {
                                                setEditingHorario(null);
                                                resetHorario();
                                            }}
                                        >
                                            Cancelar
                                        </Button>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Lista de Horários */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Horários Configurados</CardTitle>
                            </CardHeader>
                            <CardContent>
                                {horariosBase.length === 0 ? (
                                    <div className="py-8 text-center">
                                        <Clock className="mx-auto h-12 w-12 text-muted-foreground" />
                                        <h3 className="mt-4 text-lg font-semibold">Nenhum horário configurado</h3>
                                        <p className="text-muted-foreground">
                                            Configure seus horários de atendimento para que os pacientes possam agendar consultas.
                                        </p>
                                    </div>
                                ) : (
                                    <div className="space-y-3">
                                        {horariosBase.map((horario) => (
                                            <div key={horario.id} className="flex items-center justify-between rounded-lg border p-4">
                                                <div className="flex items-center gap-4">
                                                    <Badge variant={horario.ativo ? 'default' : 'secondary'}>
                                                        {getDiaSemanaLabel(horario.dia_semana)}
                                                    </Badge>
                                                    <div>
                                                        <p className="font-medium">
                                                            {horario.hora_inicio} - {horario.hora_fim}
                                                        </p>
                                                        <p className="text-sm text-muted-foreground">{horario.periodo_nome}</p>
                                                    </div>
                                                </div>
                                                <div className="flex gap-2">
                                                    <Button size="sm" variant="outline" onClick={() => handleEditHorario(horario)}>
                                                        Editar
                                                    </Button>
                                                    <Button size="sm" variant="outline" onClick={() => handleDeleteHorario(horario.id!)}>
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Exceções */}
                {activeTab === 'excecoes' && (
                    <div className="space-y-6">
                        {/* Formulário de Exceção */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <AlertCircle className="h-5 w-5" />
                                    {editingDisponibilidade ? 'Editar Exceção' : 'Adicionar Exceção'}
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label>Tipo</Label>
                                        <Select
                                            value={excecaoData.tipo}
                                            onValueChange={(value: 'disponivel' | 'indisponivel') => setExcecaoData('tipo', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="disponivel">Disponível (horário extra)</SelectItem>
                                                <SelectItem value="indisponivel">Indisponível (bloqueio)</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="space-y-2">
                                        <Label>Data Início</Label>
                                        <Input
                                            type="date"
                                            value={excecaoData.data_inicio}
                                            onChange={(e) => setExcecaoData('data_inicio', e.target.value)}
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label>Data Fim (opcional)</Label>
                                        <Input
                                            type="date"
                                            value={excecaoData.data_fim || ''}
                                            onChange={(e) => setExcecaoData('data_fim', e.target.value)}
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label>Hora Início (opcional)</Label>
                                        <Input
                                            type="time"
                                            value={excecaoData.hora_inicio || ''}
                                            onChange={(e) => setExcecaoData('hora_inicio', e.target.value)}
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label>Hora Fim (opcional)</Label>
                                        <Input
                                            type="time"
                                            value={excecaoData.hora_fim || ''}
                                            onChange={(e) => setExcecaoData('hora_fim', e.target.value)}
                                        />
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <Label>Motivo</Label>
                                    <Textarea
                                        placeholder="Descreva o motivo da exceção..."
                                        value={excecaoData.motivo || ''}
                                        onChange={(e) => setExcecaoData('motivo', e.target.value)}
                                    />
                                </div>
                                <div className="flex items-center gap-4">
                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="recorrente"
                                            checked={excecaoData.recorrente}
                                            onCheckedChange={(checked) => setExcecaoData('recorrente', checked as any)}
                                        />
                                        <Label htmlFor="recorrente">Recorrente</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="ativo-excecao"
                                            checked={excecaoData.ativo}
                                            onCheckedChange={(checked) => setExcecaoData('ativo', checked as any)}
                                        />
                                        <Label htmlFor="ativo-excecao">Ativo</Label>
                                    </div>
                                </div>
                                <div className="flex gap-2">
                                    <Button onClick={handleSaveExcecao} disabled={processingExcecao}>
                                        <Save className="mr-2 h-4 w-4" />
                                        {editingDisponibilidade ? 'Atualizar' : 'Adicionar'}
                                    </Button>
                                    {editingDisponibilidade && (
                                        <Button
                                            variant="outline"
                                            onClick={() => {
                                                setEditingDisponibilidade(null);
                                                resetExcecao();
                                            }}
                                        >
                                            Cancelar
                                        </Button>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Lista de Exceções */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Exceções Configuradas</CardTitle>
                            </CardHeader>
                            <CardContent>
                                {disponibilidades.length === 0 ? (
                                    <div className="py-8 text-center">
                                        <Calendar className="mx-auto h-12 w-12 text-muted-foreground" />
                                        <h3 className="mt-4 text-lg font-semibold">Nenhuma exceção configurada</h3>
                                        <p className="text-muted-foreground">Configure exceções para bloquear ou adicionar horários específicos.</p>
                                    </div>
                                ) : (
                                    <div className="space-y-3">
                                        {disponibilidades.map((disponibilidade) => (
                                            <div key={disponibilidade.id} className="flex items-center justify-between rounded-lg border p-4">
                                                <div className="flex items-center gap-4">
                                                    <Badge variant={disponibilidade.tipo === 'disponivel' ? 'default' : 'destructive'}>
                                                        {disponibilidade.tipo === 'disponivel' ? 'Disponível' : 'Indisponível'}
                                                    </Badge>
                                                    <div>
                                                        <p className="font-medium">
                                                            {new Date(disponibilidade.data_inicio).toLocaleDateString('pt-BR')}
                                                            {disponibilidade.data_fim &&
                                                                ` - ${new Date(disponibilidade.data_fim).toLocaleDateString('pt-BR')}`}
                                                        </p>
                                                        {(disponibilidade.hora_inicio || disponibilidade.hora_fim) && (
                                                            <p className="text-sm text-muted-foreground">
                                                                {disponibilidade.hora_inicio} - {disponibilidade.hora_fim}
                                                            </p>
                                                        )}
                                                        {disponibilidade.motivo && (
                                                            <p className="text-sm text-muted-foreground">{disponibilidade.motivo}</p>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="flex gap-2">
                                                    <Button size="sm" variant="outline" onClick={() => handleEditDisponibilidade(disponibilidade)}>
                                                        Editar
                                                    </Button>
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        onClick={() => handleDeleteDisponibilidade(disponibilidade.id!)}
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
