<?php

namespace App\Mail\Transport;

use Symfony\Component\Mailer\Transport\AbstractTransport;
use Illuminate\Support\Facades\Http;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mailer\Envelope;
use Symfony\Component\Mime\RawMessage;
use Symfony\Component\Mime\Email;

class CustomResendTransport extends AbstractTransport
{
    protected string $apiKey;

    public function __construct(string $apiKey)
    {
        parent::__construct();
        $this->apiKey = $apiKey;
    }

    protected function doSend(SentMessage $message): void
    {
        $email = $message->getOriginalMessage();
        
        if (!$email instanceof Email) {
            throw new \InvalidArgumentException('Message must be an instance of Email');
        }

        // Extrair dados do email
        $from = $email->getFrom()[0] ?? null;
        $to = array_map(fn($addr) => $addr->getAddress(), $email->getTo());
        $subject = $email->getSubject();
        $htmlBody = $email->getHtmlBody();
        $textBody = $email->getTextBody();

        // Preparar dados para a API do Resend
        $data = [
            'from' => $from ? $from->toString() : config('mail.from.address'),
            'to' => $to,
            'subject' => $subject,
        ];

        // Adicionar corpo do email
        if ($htmlBody) {
            $data['html'] = $htmlBody;
        } elseif ($textBody) {
            $data['text'] = $textBody;
        }

        // Enviar via HTTP com SSL desabilitado (igual ao script que funciona)
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ])
        ->withOptions([
            'verify' => false, // Desabilitar verificação SSL
            'timeout' => 30,
        ])
        ->post('https://api.resend.com/emails', $data);

        if (!$response->successful()) {
            throw new \Exception('Failed to send email via Resend: ' . $response->body());
        }
    }

    public function __toString(): string
    {
        return 'resend_custom';
    }
}